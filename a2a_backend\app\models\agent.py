#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统智能体模型

定义智能体相关的数据模型
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Index, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from .base import BaseModel


class Agent(BaseModel):
    """
    智能体模型
    
    存储智能体基本信息和配置
    """
    
    __tablename__ = "agents"
    
    # 用户关联
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="创建者用户ID"
    )
    
    owner_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="拥有者用户ID"
    )
    
    # 基本信息
    name = Column(
        String(100),
        nullable=False,
        comment="智能体名称"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="智能体描述"
    )
    
    avatar_url = Column(
        String(500),
        nullable=True,
        comment="头像URL"
    )
    
    # 类型和版本
    agent_type = Column(
        String(50),
        nullable=False,
        default="general",
        comment="智能体类型"
    )
    
    version = Column(
        String(20),
        nullable=False,
        default="1.0.0",
        comment="版本号"
    )
    
    # 配置信息
    config = Column(
        Text,
        nullable=True,
        comment="智能体配置（JSON格式）"
    )
    
    # 系统提示词
    system_prompt = Column(
        Text,
        nullable=True,
        comment="系统提示词"
    )
    
    # 模型配置
    model_name = Column(
        String(100),
        nullable=False,
        default="gpt-3.5-turbo",
        comment="使用的模型名称"
    )
    
    model_config = Column(
        Text,
        nullable=True,
        comment="模型配置（JSON格式）"
    )
    
    # 能力配置
    capabilities = Column(
        Text,
        nullable=True,
        comment="智能体能力列表（JSON格式）"
    )
    
    # 工具配置
    available_tools = Column(
        Text,
        nullable=True,
        comment="可用工具列表（JSON格式）"
    )
    
    # 状态信息
    status = Column(
        String(20),
        nullable=False,
        default="inactive",
        comment="智能体状态"
    )
    
    is_public = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否公开"
    )
    
    is_template = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否为模板"
    )
    
    # 统计信息
    usage_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="使用次数"
    )
    
    success_rate = Column(
        Float,
        nullable=False,
        default=0.0,
        comment="成功率"
    )
    
    average_response_time = Column(
        Float,
        nullable=False,
        default=0.0,
        comment="平均响应时间（秒）"
    )
    
    # 时间信息
    last_used_at = Column(
        DateTime,
        nullable=True,
        comment="最后使用时间"
    )
    
    # 关联关系
    user = relationship("User", foreign_keys=[user_id], back_populates="agents")
    owner = relationship("User", foreign_keys=[owner_id])
    sessions = relationship("Session", back_populates="agent")
    tasks = relationship("Task", back_populates="agent")
    task_steps = relationship("TaskStep", back_populates="agent")
    workflows = relationship("Workflow", back_populates="agent")
    artifacts = relationship("Artifact", back_populates="agent")
    agent_tools = relationship("AgentTool", back_populates="agent", cascade="all, delete-orphan")
    tool_executions = relationship("ToolExecution", back_populates="agent")
    agent_configs = relationship("AgentConfig", back_populates="agent", cascade="all, delete-orphan")
    llm_configs = relationship("LLMConfig", back_populates="agent", cascade="all, delete-orphan")
    tool_configs = relationship("ToolConfig", back_populates="agent", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index("idx_agents_user_id", "user_id"),
        Index("idx_agents_name", "name"),
        Index("idx_agents_type", "agent_type"),
        Index("idx_agents_status", "status"),
        Index("idx_agents_is_public", "is_public"),
        Index("idx_agents_is_template", "is_template"),
        Index("idx_agents_is_active", "is_active"),
    )
    
    def set_config(self, config: Dict[str, Any]) -> None:
        """
        设置智能体配置
        
        Args:
            config: 配置字典
        """
        import json
        self.config = json.dumps(config, ensure_ascii=False)
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取智能体配置
        
        Returns:
            Dict[str, Any]: 配置字典
        """
        if not self.config:
            return {}
        
        try:
            import json
            return json.loads(self.config)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_model_config(self, model_config: Dict[str, Any]) -> None:
        """
        设置模型配置
        
        Args:
            model_config: 模型配置字典
        """
        import json
        self.model_config = json.dumps(model_config, ensure_ascii=False)
    
    def get_model_config(self) -> Dict[str, Any]:
        """
        获取模型配置
        
        Returns:
            Dict[str, Any]: 模型配置字典
        """
        if not self.model_config:
            return {
                "temperature": 0.7,
                "max_tokens": 2048,
                "top_p": 1.0,
                "frequency_penalty": 0.0,
                "presence_penalty": 0.0
            }
        
        try:
            import json
            return json.loads(self.model_config)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_capabilities(self, capabilities: List[str]) -> None:
        """
        设置智能体能力
        
        Args:
            capabilities: 能力列表
        """
        import json
        self.capabilities = json.dumps(capabilities, ensure_ascii=False)
    
    def get_capabilities(self) -> List[str]:
        """
        获取智能体能力
        
        Returns:
            List[str]: 能力列表
        """
        if not self.capabilities:
            return []
        
        try:
            import json
            return json.loads(self.capabilities)
        except (json.JSONDecodeError, TypeError):
            return []
    
    def set_available_tools(self, tools: List[str]) -> None:
        """
        设置可用工具
        
        Args:
            tools: 工具列表
        """
        import json
        self.available_tools = json.dumps(tools, ensure_ascii=False)
    
    def get_available_tools(self) -> List[str]:
        """
        获取可用工具
        
        Returns:
            List[str]: 工具列表
        """
        if not self.available_tools:
            return []
        
        try:
            import json
            return json.loads(self.available_tools)
        except (json.JSONDecodeError, TypeError):
            return []
    
    def update_usage_stats(self, response_time: float, success: bool = True) -> None:
        """
        更新使用统计
        
        Args:
            response_time: 响应时间（秒）
            success: 是否成功
        """
        self.usage_count += 1
        self.last_used_at = datetime.now()
        
        # 更新平均响应时间
        if self.usage_count == 1:
            self.average_response_time = response_time
        else:
            self.average_response_time = (
                (self.average_response_time * (self.usage_count - 1) + response_time) / self.usage_count
            )
        
        # 更新成功率
        if success:
            success_count = int(self.success_rate * (self.usage_count - 1)) + 1
        else:
            success_count = int(self.success_rate * (self.usage_count - 1))
        
        self.success_rate = success_count / self.usage_count
    
    def activate(self) -> None:
        """
        激活智能体
        """
        self.status = "active"
    
    def deactivate(self) -> None:
        """
        停用智能体
        """
        self.status = "inactive"
    
    def __repr__(self) -> str:
        return f"<Agent(id={self.id}, name='{self.name}', type='{self.agent_type}')>"


class AgentTemplate(BaseModel):
    """
    智能体模板模型
    
    存储智能体模板信息
    """
    
    __tablename__ = "agent_templates"
    
    # 基本信息
    name = Column(
        String(100),
        nullable=False,
        comment="模板名称"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="模板描述"
    )
    
    category = Column(
        String(50),
        nullable=False,
        default="general",
        comment="模板分类"
    )
    
    # 模板配置
    template_config = Column(
        Text,
        nullable=False,
        comment="模板配置（JSON格式）"
    )
    
    # 版本信息
    version = Column(
        String(20),
        nullable=False,
        default="1.0.0",
        comment="模板版本"
    )
    
    # 状态信息
    is_official = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否为官方模板"
    )
    
    is_featured = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否为推荐模板"
    )
    
    # 统计信息
    usage_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="使用次数"
    )
    
    rating = Column(
        Float,
        nullable=False,
        default=0.0,
        comment="评分"
    )
    
    # 创建者信息
    created_by = Column(
        Integer,
        nullable=True,
        comment="创建者ID"
    )
    
    # 索引
    __table_args__ = (
        Index("idx_agent_templates_name", "name"),
        Index("idx_agent_templates_category", "category"),
        Index("idx_agent_templates_is_official", "is_official"),
        Index("idx_agent_templates_is_featured", "is_featured"),
        Index("idx_agent_templates_is_active", "is_active"),
    )
    
    def set_template_config(self, config: Dict[str, Any]) -> None:
        """
        设置模板配置
        
        Args:
            config: 配置字典
        """
        import json
        self.template_config = json.dumps(config, ensure_ascii=False)
    
    def get_template_config(self) -> Dict[str, Any]:
        """
        获取模板配置
        
        Returns:
            Dict[str, Any]: 配置字典
        """
        if not self.template_config:
            return {}
        
        try:
            import json
            return json.loads(self.template_config)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def increment_usage(self) -> None:
        """
        增加使用次数
        """
        self.usage_count += 1
    
    def update_rating(self, new_rating: float, total_ratings: int) -> None:
        """
        更新评分
        
        Args:
            new_rating: 新评分
            total_ratings: 总评分数
        """
        if total_ratings == 1:
            self.rating = new_rating
        else:
            self.rating = ((self.rating * (total_ratings - 1)) + new_rating) / total_ratings
    
    def __repr__(self) -> str:
        return f"<AgentTemplate(id={self.id}, name='{self.name}', category='{self.category}')>"


class AgentCollaboration(BaseModel):
    """
    智能体协作模型
    
    记录智能体之间的协作关系
    """
    
    __tablename__ = "agent_collaborations"
    
    # 智能体关联
    primary_agent_id = Column(
        Integer,
        ForeignKey("agents.id", ondelete="CASCADE"),
        nullable=False,
        comment="主智能体ID"
    )
    
    secondary_agent_id = Column(
        Integer,
        ForeignKey("agents.id", ondelete="CASCADE"),
        nullable=False,
        comment="协作智能体ID"
    )
    
    # 协作信息
    collaboration_type = Column(
        String(50),
        nullable=False,
        comment="协作类型"
    )
    
    priority = Column(
        Integer,
        nullable=False,
        default=1,
        comment="优先级"
    )
    
    # 配置信息
    config = Column(
        Text,
        nullable=True,
        comment="协作配置（JSON格式）"
    )
    
    # 状态信息
    status = Column(
        String(20),
        nullable=False,
        default="active",
        comment="协作状态"
    )
    
    # 统计信息
    interaction_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="交互次数"
    )
    
    success_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="成功次数"
    )
    
    # 时间信息
    last_interaction_at = Column(
        DateTime,
        nullable=True,
        comment="最后交互时间"
    )
    
    # 关联关系
    primary_agent = relationship("Agent", foreign_keys=[primary_agent_id])
    secondary_agent = relationship("Agent", foreign_keys=[secondary_agent_id])
    
    # 索引
    __table_args__ = (
        Index("idx_agent_collaborations_primary", "primary_agent_id"),
        Index("idx_agent_collaborations_secondary", "secondary_agent_id"),
        Index("idx_agent_collaborations_type", "collaboration_type"),
        Index("idx_agent_collaborations_status", "status"),
        # 复合唯一索引
        Index("idx_agent_collaborations_unique", "primary_agent_id", "secondary_agent_id", unique=True),
    )
    
    def set_config(self, config: Dict[str, Any]) -> None:
        """
        设置协作配置
        
        Args:
            config: 配置字典
        """
        import json
        self.config = json.dumps(config, ensure_ascii=False)
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取协作配置
        
        Returns:
            Dict[str, Any]: 配置字典
        """
        if not self.config:
            return {}
        
        try:
            import json
            return json.loads(self.config)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def record_interaction(self, success: bool = True) -> None:
        """
        记录交互
        
        Args:
            success: 是否成功
        """
        self.interaction_count += 1
        if success:
            self.success_count += 1
        self.last_interaction_at = datetime.now()
    
    @property
    def success_rate(self) -> float:
        """
        获取成功率
        
        Returns:
            float: 成功率
        """
        if self.interaction_count == 0:
            return 0.0
        return self.success_count / self.interaction_count
    
    def __repr__(self) -> str:
        return f"<AgentCollaboration(id={self.id}, primary={self.primary_agent_id}, secondary={self.secondary_agent_id})>"


class AgentHierarchy(BaseModel):
    """
    智能体层次关系模型
    
    定义智能体之间的层次关系
    """
    
    __tablename__ = "agent_hierarchies"
    
    # 用户关联
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="创建者用户ID"
    )
    
    owner_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="拥有者用户ID"
    )
    
    # 智能体关联
    parent_agent_id = Column(
        Integer,
        ForeignKey("agents.id", ondelete="CASCADE"),
        nullable=False,
        comment="父智能体ID"
    )
    
    child_agent_id = Column(
        Integer,
        ForeignKey("agents.id", ondelete="CASCADE"),
        nullable=False,
        comment="子智能体ID"
    )
    
    # 关系信息
    relationship_type = Column(
        String(50),
        nullable=False,
        default="parent_child",
        comment="关系类型"
    )
    
    # 层级信息
    level = Column(
        Integer,
        nullable=False,
        default=1,
        comment="层级深度"
    )
    
    # 权限信息
    permissions = Column(
        Text,
        nullable=True,
        comment="权限配置（JSON格式）"
    )
    
    # 状态信息
    status = Column(
        String(20),
        nullable=False,
        default="active",
        comment="关系状态"
    )
    
    # 配置信息
    config = Column(
        Text,
        nullable=True,
        comment="层次关系配置（JSON格式）"
    )
    
    # 关联关系
    parent_agent = relationship("Agent", foreign_keys=[parent_agent_id])
    child_agent = relationship("Agent", foreign_keys=[child_agent_id])
    
    # 索引
    __table_args__ = (
        Index("idx_agent_hierarchies_parent", "parent_agent_id"),
        Index("idx_agent_hierarchies_child", "child_agent_id"),
        Index("idx_agent_hierarchies_user_id", "user_id"),
        Index("idx_agent_hierarchies_owner_id", "owner_id"),
        Index("idx_agent_hierarchies_type", "relationship_type"),
        Index("idx_agent_hierarchies_level", "level"),
        Index("idx_agent_hierarchies_status", "status"),
        # 复合唯一索引
        Index("idx_agent_hierarchies_unique", "parent_agent_id", "child_agent_id", unique=True),
    )
    
    def set_permissions(self, permissions: Dict[str, Any]) -> None:
        """
        设置权限配置
        
        Args:
            permissions: 权限配置字典
        """
        import json
        self.permissions = json.dumps(permissions, ensure_ascii=False)
    
    def get_permissions(self) -> Dict[str, Any]:
        """
        获取权限配置
        
        Returns:
            Dict[str, Any]: 权限配置字典
        """
        if not self.permissions:
            return {}
        
        try:
            import json
            return json.loads(self.permissions)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_config(self, config: Dict[str, Any]) -> None:
        """
        设置层次关系配置
        
        Args:
            config: 配置字典
        """
        import json
        self.config = json.dumps(config, ensure_ascii=False)
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取层次关系配置
        
        Returns:
            Dict[str, Any]: 配置字典
        """
        if not self.config:
            return {}
        
        try:
            import json
            return json.loads(self.config)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def __repr__(self) -> str:
        return f"<AgentHierarchy(id={self.id}, parent={self.parent_agent_id}, child={self.child_agent_id})>"