import type { ExtractPropTypes, StyleValue } from 'vue';
import type { Dayjs } from 'dayjs';
import type Statistic from './statistic.vue';
export declare const statisticProps: {
    readonly decimalSeparator: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, ".", boolean>;
    readonly groupSeparator: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, ",", boolean>;
    readonly precision: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
    readonly formatter: FunctionConstructor;
    readonly value: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => number | Dayjs) | (() => number | Dayjs) | ((new (...args: any[]) => number | Dayjs) | (() => number | Dayjs))[], unknown, unknown, 0, boolean>;
    readonly prefix: StringConstructor;
    readonly suffix: StringConstructor;
    readonly title: StringConstructor;
    readonly valueStyle: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => string | import("vue").CSSProperties | StyleValue[]) | (() => StyleValue) | ((new (...args: any[]) => string | import("vue").CSSProperties | StyleValue[]) | (() => StyleValue))[], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
};
export type StatisticProps = ExtractPropTypes<typeof statisticProps>;
export type StatisticInstance = InstanceType<typeof Statistic> & unknown;
