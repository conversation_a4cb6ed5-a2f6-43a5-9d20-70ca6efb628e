<template>
  <div class="data-management">
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card title="数据备份">
          <template #header>
            <div class="card-header">
              <span>数据备份</span>
              <el-button type="primary" @click="createBackup">创建备份</el-button>
            </div>
          </template>
          
          <el-table :data="backupList" stripe>
            <el-table-column prop="id" label="备份ID" width="120" />
            <el-table-column prop="created_at" label="创建时间" width="160">
              <template #default="{ row }">
                {{ formatDate(row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column prop="size" label="大小" width="100">
              <template #default="{ row }">
                {{ formatSize(row.size) }}
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="{ row }">
                <el-button size="small" @click="restoreBackup(row.id)">恢复</el-button>
                <el-button type="danger" size="small" @click="deleteBackup(row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card title="数据清理">
          <template #header>
            <span>数据清理</span>
          </template>
          
          <el-form :model="cleanupForm" label-width="120px">
            <el-form-item label="清理日志">
              <el-switch v-model="cleanupForm.clean_logs" />
            </el-form-item>
            
            <el-form-item label="清理临时文件">
              <el-switch v-model="cleanupForm.clean_temp_files" />
            </el-form-item>
            
            <el-form-item label="清理旧会话">
              <el-switch v-model="cleanupForm.clean_old_sessions" />
            </el-form-item>
            
            <el-form-item label="保留天数">
              <el-input-number v-model="cleanupForm.days_to_keep" :min="1" :max="365" />
            </el-form-item>
            
            <el-form-item>
              <el-button type="warning" @click="performCleanup">执行清理</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAdminStore } from '@/stores/admin'
import dayjs from 'dayjs'

const adminStore = useAdminStore()

const backupList = ref<any[]>([])
const loading = ref(false)

const cleanupForm = reactive({
  clean_logs: false,
  clean_temp_files: false,
  clean_old_sessions: false,
  days_to_keep: 30
})

/**
 * 加载备份列表
 */
const loadBackups = async () => {
  try {
    loading.value = true
    await adminStore.fetchBackups()
    backupList.value = adminStore.backups
  } catch (error) {
    console.error('加载备份列表失败:', error)
    // 使用模拟数据作为后备
    backupList.value = [
      { id: 'backup_001', created_at: new Date(), size: 1024 * 1024 * 50 },
      { id: 'backup_002', created_at: new Date(), size: 1024 * 1024 * 48 }
    ]
  } finally {
    loading.value = false
  }
}

const createBackup = async () => {
  try {
    await ElMessageBox.confirm('确定要创建数据备份吗？', '确认备份', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })

    await adminStore.createBackup()
    ElMessage.success('备份创建成功')
    await loadBackups() // 刷新备份列表
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('备份创建失败')
      console.error('备份创建失败:', error)
    }
  }
}

const restoreBackup = async (backupId: string) => {
  try {
    await ElMessageBox.confirm('确定要恢复此备份吗？这将覆盖当前数据！', '确认恢复', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await adminStore.restoreBackup(backupId)
    ElMessage.success('备份恢复成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('备份恢复失败')
      console.error('备份恢复失败:', error)
    }
  }
}

const deleteBackup = async (backupId: string) => {
  try {
    await ElMessageBox.confirm('确定要删除此备份吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await adminStore.deleteBackup(backupId)
    ElMessage.success('备份删除成功')
    await loadBackups() // 刷新备份列表
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('备份删除失败')
      console.error('备份删除失败:', error)
    }
  }
}

const performCleanup = async () => {
  try {
    await ElMessageBox.confirm('确定要执行数据清理吗？', '确认清理', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await adminStore.cleanupData(cleanupForm)
    ElMessage.success('数据清理完成')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('数据清理失败')
      console.error('数据清理失败:', error)
    }
  }
}

const formatDate = (date: Date | string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

const formatSize = (bytes: number) => {
  const sizes = ['B', 'KB', 'MB', 'GB']
  if (bytes === 0) return '0 B'
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

onMounted(() => {
  loadBackups()
})
</script>

<style scoped>
.data-management {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
