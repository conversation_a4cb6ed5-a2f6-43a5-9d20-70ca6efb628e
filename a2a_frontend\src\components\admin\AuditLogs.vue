<template>
  <div class="audit-logs">
    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="用户ID">
          <el-input v-model="searchForm.user_id" placeholder="用户ID" clearable />
        </el-form-item>
        
        <el-form-item label="操作">
          <el-select v-model="searchForm.action" placeholder="选择操作" clearable>
            <el-option label="登录" value="login" />
            <el-option label="登出" value="logout" />
            <el-option label="创建" value="create" />
            <el-option label="更新" value="update" />
            <el-option label="删除" value="delete" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="资源类型">
          <el-select v-model="searchForm.resource_type" placeholder="选择资源类型" clearable>
            <el-option label="用户" value="user" />
            <el-option label="智能体" value="agent" />
            <el-option label="会话" value="session" />
            <el-option label="任务" value="task" />
            <el-option label="工作流" value="workflow" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 审计日志列表 -->
    <el-card>
      <el-table :data="auditLogsList" stripe>
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column prop="user_id" label="用户ID" width="100" />
        
        <el-table-column prop="username" label="用户名" width="120" />
        
        <el-table-column prop="action" label="操作" width="100">
          <template #default="{ row }">
            <el-tag :type="getActionColor(row.action)" size="small">
              {{ getActionLabel(row.action) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="resource_type" label="资源类型" width="100">
          <template #default="{ row }">
            <el-tag type="info" size="small">
              {{ getResourceLabel(row.resource_type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="resource_id" label="资源ID" width="100" />
        
        <el-table-column prop="ip_address" label="IP地址" width="120" />
        
        <el-table-column prop="user_agent" label="用户代理" min-width="200">
          <template #default="{ row }">
            <el-tooltip :content="row.user_agent" placement="top">
              <span class="truncate">{{ row.user_agent }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="100">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewDetails(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="searchForm.page"
          v-model:page-size="searchForm.size"
          :total="1000"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSearch"
          @current-change="handleSearch"
        />
      </div>
    </el-card>
    
    <!-- 详情对话框 -->
    <el-dialog v-model="showDetailsDialog" title="审计日志详情" width="600px">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="ID">{{ selectedLog?.id }}</el-descriptions-item>
        <el-descriptions-item label="用户ID">{{ selectedLog?.user_id }}</el-descriptions-item>
        <el-descriptions-item label="用户名">{{ selectedLog?.username }}</el-descriptions-item>
        <el-descriptions-item label="操作">{{ getActionLabel(selectedLog?.action) }}</el-descriptions-item>
        <el-descriptions-item label="资源类型">{{ getResourceLabel(selectedLog?.resource_type) }}</el-descriptions-item>
        <el-descriptions-item label="资源ID">{{ selectedLog?.resource_id }}</el-descriptions-item>
        <el-descriptions-item label="IP地址">{{ selectedLog?.ip_address }}</el-descriptions-item>
        <el-descriptions-item label="时间">{{ formatDate(selectedLog?.created_at) }}</el-descriptions-item>
        <el-descriptions-item label="用户代理" span="2">{{ selectedLog?.user_agent }}</el-descriptions-item>
      </el-descriptions>
      
      <div v-if="selectedLog?.details" style="margin-top: 20px;">
        <h4>详细信息</h4>
        <pre>{{ JSON.stringify(selectedLog.details, null, 2) }}</pre>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { useAdminStore } from '@/stores/admin'
import dayjs from 'dayjs'

const adminStore = useAdminStore()

// 状态
const showDetailsDialog = ref(false)
const selectedLog = ref<any>(null)
const dateRange = ref<[string, string]>()

// 搜索表单
const searchForm = reactive({
  user_id: '',
  action: '',
  resource_type: '',
  start_time: '',
  end_time: '',
  page: 1,
  size: 20
})

const auditLogsList = ref<any[]>([])
const loading = ref(false)
const pagination = ref({
  total: 0,
  page: 1,
  size: 20
})

/**
 * 加载审计日志
 */
const loadAuditLogs = async () => {
  try {
    loading.value = true

    const params = { ...searchForm }
    if (dateRange.value) {
      params.start_time = dateRange.value[0]
      params.end_time = dateRange.value[1]
    }

    await adminStore.fetchAuditLogs(params)
    auditLogsList.value = adminStore.auditLogs
    // 这里应该从API响应中获取分页信息
    pagination.value.total = adminStore.auditLogs.length
  } catch (error) {
    console.error('加载审计日志失败:', error)
    // 使用模拟数据作为后备
    auditLogsList.value = [
      {
        id: 1,
        user_id: 1,
        username: 'admin',
        action: 'login',
        resource_type: 'user',
        resource_id: 1,
        ip_address: '*************',
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        created_at: new Date(),
        details: { success: true }
      },
      {
        id: 2,
        user_id: 2,
        username: 'user1',
        action: 'create',
        resource_type: 'agent',
        resource_id: 5,
        ip_address: '*************',
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        created_at: new Date(),
        details: { agent_name: '测试智能体' }
      }
    ]
    pagination.value.total = auditLogsList.value.length
  } finally {
    loading.value = false
  }
}

/**
 * 搜索审计日志
 */
const handleSearch = async () => {
  searchForm.page = 1 // 重置到第一页
  await loadAuditLogs()
}

/**
 * 重置搜索
 */
const handleReset = () => {
  Object.assign(searchForm, {
    user_id: '',
    action: '',
    resource_type: '',
    start_time: '',
    end_time: '',
    page: 1,
    size: 20
  })
  dateRange.value = undefined
  handleSearch()
}

/**
 * 查看详情
 */
const viewDetails = (log: any) => {
  selectedLog.value = log
  showDetailsDialog.value = true
}

/**
 * 获取操作颜色
 */
const getActionColor = (action: string) => {
  const colors: Record<string, string> = {
    login: 'success',
    logout: 'info',
    create: 'primary',
    update: 'warning',
    delete: 'danger'
  }
  return colors[action] || 'info'
}

/**
 * 获取操作标签
 */
const getActionLabel = (action: string) => {
  const labels: Record<string, string> = {
    login: '登录',
    logout: '登出',
    create: '创建',
    update: '更新',
    delete: '删除'
  }
  return labels[action] || action
}

/**
 * 获取资源标签
 */
const getResourceLabel = (resourceType: string) => {
  const labels: Record<string, string> = {
    user: '用户',
    agent: '智能体',
    session: '会话',
    task: '任务',
    workflow: '工作流'
  }
  return labels[resourceType] || resourceType
}

/**
 * 格式化日期
 */
const formatDate = (date: Date | string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

onMounted(() => {
  loadAuditLogs()
})
</script>

<style scoped>
.audit-logs {
  padding: 0;
}

.search-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.truncate {
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>
