# -*- coding: utf-8 -*-
"""
A2A多智能体系统任务服务

提供任务的创建、执行、监控、步骤管理和资源控制功能
"""

import json
import uuid
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union
from enum import Enum
from sqlalchemy import and_, or_, desc, func
from sqlalchemy.orm import Session, selectinload
from loguru import logger

from app.core.database import get_database_manager
from app.models.task import Task, TaskStep, TaskResult, TaskDependency
from app.models.agent import Agent
from app.models.user import User
from app.models.session import Session as SessionModel
from app.services.auth_service import AuthService
from app.services.agent_service import AgentService
from app.core.config import get_settings
from adk.runners.base_runner import BaseRunner
from google.adk.agents.base_agent import BaseAgent


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"          # 等待执行
    RUNNING = "running"          # 正在执行
    PAUSED = "paused"            # 已暂停
    COMPLETED = "completed"      # 已完成
    FAILED = "failed"            # 执行失败
    CANCELLED = "cancelled"      # 已取消
    TIMEOUT = "timeout"          # 执行超时


class TaskPriority(str, Enum):
    """任务优先级枚举"""
    LOW = "low"                  # 低优先级
    NORMAL = "normal"            # 普通优先级
    HIGH = "high"                # 高优先级
    URGENT = "urgent"            # 紧急优先级


class TaskService:
    """
    任务服务
    
    提供任务的创建、执行、监控、步骤管理和资源控制功能
    """
    
    def __init__(self):
        self.db_manager = get_database_manager()
        self.auth_service = AuthService()
        self.agent_service = AgentService()
        self.settings = get_settings()
        self._running_tasks = {}  # 正在运行的任务
        self._task_queues = {     # 任务队列（按优先级）
            TaskPriority.URGENT: asyncio.Queue(),
            TaskPriority.HIGH: asyncio.Queue(),
            TaskPriority.NORMAL: asyncio.Queue(),
            TaskPriority.LOW: asyncio.Queue()
        }
        self._executor_pool = {}  # 执行器池
        self._resource_limits = {  # 资源限制
            "max_concurrent_tasks": self.settings.max_concurrent_tasks_per_user,
            "max_task_duration": self.settings.max_task_duration_minutes,
            "max_memory_usage": self.settings.max_memory_usage_mb
        }
    
    async def create_task(self, user_id: int, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建任务
        
        Args:
            user_id: 用户ID
            task_data: 任务数据
            
        Returns:
            Dict[str, Any]: 创建结果
            
        Raises:
            ValueError: 参数无效
            Exception: 创建失败
        """
        async with self.db_manager.get_session() as session:
            try:
                # 验证用户权限
                has_permission = await self.auth_service.check_permission(
                    user_id, "task", None, "create"
                )
                if not has_permission:
                    raise ValueError("没有创建任务的权限")
                
                # 验证必需字段
                required_fields = ["title", "type", "agent_id"]
                for field in required_fields:
                    if field not in task_data:
                        raise ValueError(f"缺少必需字段: {field}")
                
                # 验证智能体
                agent_id = task_data["agent_id"]
                agent_result = await session.execute(
                    session.query(Agent).filter(
                        and_(
                            Agent.id == agent_id,
                            Agent.deleted_at.is_(None),
                            Agent.status.in_(["active", "inactive"])
                        )
                    )
                )
                agent = agent_result.scalar_one_or_none()
                
                if not agent:
                    raise ValueError("智能体不存在或不可用")
                
                # 检查智能体访问权限
                is_owner = await self.auth_service.check_resource_ownership(
                    user_id, "agent", agent_id
                )
                has_execute_permission = await self.auth_service.check_permission(
                    user_id, "agent", agent_id, "execute"
                )
                
                if not is_owner and not has_execute_permission and not agent.is_public:
                    raise ValueError("没有使用该智能体的权限")
                
                # 检查用户任务配额
                await self._check_user_task_quota(session, user_id)
                
                # 验证会话（如果提供）
                session_id = task_data.get("session_id")
                if session_id:
                    session_result = await session.execute(
                        session.query(SessionModel).filter(
                            and_(
                                SessionModel.id == session_id,
                                SessionModel.deleted_at.is_(None)
                            )
                        )
                    )
                    session_obj = session_result.scalar_one_or_none()
                    
                    if not session_obj:
                        raise ValueError("会话不存在")
                
                # 创建任务记录
                task_id = str(uuid.uuid4())
                new_task = Task(
                    id=task_id,
                    user_id=user_id,
                    agent_id=agent_id,
                    session_id=session_id,
                    title=task_data["title"],
                    description=task_data.get("description", ""),
                    type=task_data["type"],
                    priority=task_data.get("priority", TaskPriority.NORMAL),
                    status=TaskStatus.PENDING,
                    input_data=json.dumps(task_data.get("input_data", {})),
                    config=json.dumps(task_data.get("config", {})),
                    metadata=json.dumps(task_data.get("metadata", {})),
                    max_duration=task_data.get("max_duration", self._resource_limits["max_task_duration"]),
                    retry_count=0,
                    max_retries=task_data.get("max_retries", 3),
                    scheduled_at=datetime.fromisoformat(task_data["scheduled_at"]) if task_data.get("scheduled_at") else None,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                
                session.add(new_task)
                await session.flush()  # 获取task.id
                
                # 创建任务步骤（如果提供）
                steps = task_data.get("steps", [])
                for i, step_data in enumerate(steps):
                    step = TaskStep(
                        id=str(uuid.uuid4()),
                        task_id=task_id,
                        name=step_data["name"],
                        description=step_data.get("description", ""),
                        step_order=i + 1,
                        type=step_data.get("type", "action"),
                        config=json.dumps(step_data.get("config", {})),
                        input_schema=json.dumps(step_data.get("input_schema", {})),
                        output_schema=json.dumps(step_data.get("output_schema", {})),
                        status="pending",
                        created_at=datetime.utcnow()
                    )
                    session.add(step)
                
                # 创建任务依赖关系（如果提供）
                dependencies = task_data.get("dependencies", [])
                for dep_task_id in dependencies:
                    # 验证依赖任务存在且用户有权限
                    dep_result = await session.execute(
                        session.query(Task).filter(
                            and_(
                                Task.id == dep_task_id,
                                Task.user_id == user_id,  # 只能依赖自己的任务
                                Task.deleted_at.is_(None)
                            )
                        )
                    )
                    if dep_result.scalar_one_or_none():
                        dependency = TaskDependency(
                            id=str(uuid.uuid4()),
                            task_id=task_id,
                            depends_on_task_id=dep_task_id,
                            dependency_type="completion",
                            created_at=datetime.utcnow()
                        )
                        session.add(dependency)
                
                await session.commit()
                
                # 如果是立即执行的任务，加入执行队列
                if not new_task.scheduled_at:
                    await self._enqueue_task(task_id, new_task.priority)
                
                logger.info(f"任务创建成功: {new_task.title}", extra={
                    "user_id": user_id,
                    "task_id": task_id,
                    "agent_id": agent_id,
                    "task_type": new_task.type,
                    "priority": new_task.priority
                })
                
                return {
                    "success": True,
                    "message": "任务创建成功",
                    "data": {
                        "task_id": task_id,
                        "title": new_task.title,
                        "type": new_task.type,
                        "status": new_task.status,
                        "priority": new_task.priority,
                        "created_at": new_task.created_at.isoformat()
                    }
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"任务创建失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"任务创建异常: {str(e)}")
                raise Exception(f"任务创建失败: {str(e)}")
    
    async def get_task(self, user_id: int, task_id: str) -> Dict[str, Any]:
        """
        获取任务详情
        
        Args:
            user_id: 用户ID
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 任务详情
            
        Raises:
            ValueError: 任务不存在或无权限
        """
        async with self.db_manager.get_session() as session:
            try:
                # 查询任务
                result = await session.execute(
                    session.query(Task)
                    .options(
                        selectinload(Task.steps),
                        selectinload(Task.results),
                        selectinload(Task.dependencies),
                        selectinload(Task.agent),
                        selectinload(Task.session)
                    )
                    .filter(
                        and_(
                            Task.id == task_id,
                            Task.deleted_at.is_(None)
                        )
                    )
                )
                task = result.scalar_one_or_none()
                
                if not task:
                    raise ValueError("任务不存在")
                
                # 检查访问权限
                is_owner = task.user_id == user_id
                has_read_permission = await self.auth_service.check_permission(
                    user_id, "task", task_id, "read"
                )
                
                if not is_owner and not has_read_permission:
                    raise ValueError("没有访问权限")
                
                # 格式化任务数据
                task_dict = {
                    "task_id": task.id,
                    "title": task.title,
                    "description": task.description,
                    "type": task.type,
                    "status": task.status,
                    "priority": task.priority,
                    "progress": task.progress,
                    "input_data": json.loads(task.input_data) if task.input_data else {},
                    "config": json.loads(task.config) if task.config else {},
                    "metadata": json.loads(task.metadata) if task.metadata else {},
                    "agent": {
                        "agent_id": task.agent.id,
                        "name": task.agent.name,
                        "type": task.agent.type
                    } if task.agent else None,
                    "session": {
                        "session_id": task.session.id,
                        "title": task.session.title
                    } if task.session else None,
                    "steps": [
                        {
                            "step_id": step.id,
                            "name": step.name,
                            "description": step.description,
                            "step_order": step.step_order,
                            "type": step.type,
                            "status": step.status,
                            "progress": step.progress,
                            "result": json.loads(step.result) if step.result else None,
                            "error_message": step.error_message,
                            "started_at": step.started_at.isoformat() if step.started_at else None,
                            "completed_at": step.completed_at.isoformat() if step.completed_at else None
                        } for step in sorted(task.steps, key=lambda x: x.step_order)
                    ],
                    "results": [
                        {
                            "result_id": result.id,
                            "type": result.type,
                            "data": json.loads(result.data) if result.data else {},
                            "metadata": json.loads(result.metadata) if result.metadata else {},
                            "created_at": result.created_at.isoformat()
                        } for result in task.results
                    ],
                    "dependencies": [
                        {
                            "dependency_id": dep.id,
                            "depends_on_task_id": dep.depends_on_task_id,
                            "dependency_type": dep.dependency_type,
                            "status": dep.status
                        } for dep in task.dependencies
                    ],
                    "retry_count": task.retry_count,
                    "max_retries": task.max_retries,
                    "max_duration": task.max_duration,
                    "scheduled_at": task.scheduled_at.isoformat() if task.scheduled_at else None,
                    "started_at": task.started_at.isoformat() if task.started_at else None,
                    "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                    "created_at": task.created_at.isoformat(),
                    "updated_at": task.updated_at.isoformat(),
                    "error_message": task.error_message
                }
                
                return task_dict
                
            except ValueError as e:
                logger.warning(f"获取任务失败: {str(e)}")
                raise
            except Exception as e:
                logger.error(f"获取任务异常: {str(e)}")
                raise Exception(f"获取任务失败: {str(e)}")
    
    async def update_task(self, user_id: int, task_id: str, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新任务
        
        Args:
            user_id: 用户ID
            task_id: 任务ID
            update_data: 更新数据
            
        Returns:
            Dict[str, Any]: 更新结果
        """
        async with self.db_manager.get_session() as session:
            try:
                # 查询任务
                result = await session.execute(
                    session.query(Task).filter(
                        and_(
                            Task.id == task_id,
                            Task.deleted_at.is_(None)
                        )
                    )
                )
                task = result.scalar_one_or_none()
                
                if not task:
                    raise ValueError("任务不存在")
                
                # 检查权限
                is_owner = task.user_id == user_id
                has_manage_permission = await self.auth_service.check_permission(
                    user_id, "task", task_id, "manage"
                )
                
                if not is_owner and not has_manage_permission:
                    raise ValueError("没有管理权限")
                
                # 检查任务状态是否允许更新
                if task.status in [TaskStatus.RUNNING]:
                    # 运行中的任务只能更新某些字段
                    allowed_fields = ["priority", "metadata"]
                    for field in update_data.keys():
                        if field not in allowed_fields:
                            raise ValueError(f"运行中的任务不能更新字段: {field}")
                
                # 更新基本信息
                if "title" in update_data:
                    task.title = update_data["title"]
                
                if "description" in update_data:
                    task.description = update_data["description"]
                
                if "priority" in update_data:
                    if update_data["priority"] in [p.value for p in TaskPriority]:
                        task.priority = update_data["priority"]
                    else:
                        raise ValueError("无效的优先级")
                
                if "config" in update_data:
                    task.config = json.dumps(update_data["config"])
                
                if "metadata" in update_data:
                    task.metadata = json.dumps(update_data["metadata"])
                
                if "max_duration" in update_data:
                    task.max_duration = update_data["max_duration"]
                
                if "max_retries" in update_data:
                    task.max_retries = update_data["max_retries"]
                
                if "scheduled_at" in update_data:
                    if update_data["scheduled_at"]:
                        task.scheduled_at = datetime.fromisoformat(update_data["scheduled_at"])
                    else:
                        task.scheduled_at = None
                
                task.updated_at = datetime.utcnow()
                await session.commit()
                
                logger.info(f"任务更新成功: {task.title}", extra={
                    "user_id": user_id,
                    "task_id": task_id
                })
                
                return {
                    "success": True,
                    "message": "任务更新成功",
                    "data": {
                        "task_id": task_id,
                        "updated_at": task.updated_at.isoformat()
                    }
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"任务更新失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"任务更新异常: {str(e)}")
                raise Exception(f"任务更新失败: {str(e)}")
    
    async def delete_task(self, user_id: int, task_id: str, hard_delete: bool = False) -> Dict[str, Any]:
        """
        删除任务
        
        Args:
            user_id: 用户ID
            task_id: 任务ID
            hard_delete: 是否硬删除
            
        Returns:
            Dict[str, Any]: 删除结果
        """
        async with self.db_manager.get_session() as session:
            try:
                # 查询任务
                result = await session.execute(
                    session.query(Task).filter(
                        and_(
                            Task.id == task_id,
                            Task.deleted_at.is_(None) if not hard_delete else True
                        )
                    )
                )
                task = result.scalar_one_or_none()
                
                if not task:
                    raise ValueError("任务不存在")
                
                # 检查权限
                is_owner = task.user_id == user_id
                has_manage_permission = await self.auth_service.check_permission(
                    user_id, "task", task_id, "manage"
                )
                
                if not is_owner and not has_manage_permission:
                    raise ValueError("没有删除权限")
                
                # 如果任务正在运行，先停止它
                if task.status == TaskStatus.RUNNING:
                    await self._stop_task_execution(task_id)
                
                if hard_delete:
                    # 硬删除：删除所有相关记录
                    await session.execute(
                        session.query(TaskStep).filter(
                            TaskStep.task_id == task_id
                        ).delete()
                    )
                    
                    await session.execute(
                        session.query(TaskResult).filter(
                            TaskResult.task_id == task_id
                        ).delete()
                    )
                    
                    await session.execute(
                        session.query(TaskDependency).filter(
                            or_(
                                TaskDependency.task_id == task_id,
                                TaskDependency.depends_on_task_id == task_id
                            )
                        ).delete()
                    )
                    
                    await session.delete(task)
                    message = "任务已永久删除"
                else:
                    # 软删除：标记删除时间
                    task.deleted_at = datetime.utcnow()
                    task.status = TaskStatus.CANCELLED
                    task.updated_at = datetime.utcnow()
                    message = "任务已删除"
                
                await session.commit()
                
                logger.info(f"任务删除成功: {task.title}", extra={
                    "user_id": user_id,
                    "task_id": task_id,
                    "hard_delete": hard_delete
                })
                
                return {
                    "success": True,
                    "message": message
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"任务删除失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"任务删除异常: {str(e)}")
                raise Exception(f"任务删除失败: {str(e)}")
    
    async def list_tasks(self, user_id: int, filters: Optional[Dict[str, Any]] = None,
                        page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """
        获取任务列表
        
        Args:
            user_id: 用户ID
            filters: 过滤条件
            page: 页码
            page_size: 页面大小
            
        Returns:
            Dict[str, Any]: 任务列表
        """
        async with self.db_manager.get_session() as session:
            try:
                # 构建查询条件
                query_conditions = [
                    Task.deleted_at.is_(None)
                ]
                
                # 权限过滤：只显示用户的任务或有权限访问的任务
                query_conditions.append(Task.user_id == user_id)
                
                # 应用过滤条件
                if filters:
                    if "status" in filters:
                        if isinstance(filters["status"], list):
                            query_conditions.append(Task.status.in_(filters["status"]))
                        else:
                            query_conditions.append(Task.status == filters["status"])
                    
                    if "type" in filters:
                        query_conditions.append(Task.type == filters["type"])
                    
                    if "priority" in filters:
                        query_conditions.append(Task.priority == filters["priority"])
                    
                    if "agent_id" in filters:
                        query_conditions.append(Task.agent_id == filters["agent_id"])
                    
                    if "session_id" in filters:
                        query_conditions.append(Task.session_id == filters["session_id"])
                    
                    if "title" in filters:
                        query_conditions.append(
                            Task.title.ilike(f"%{filters['title']}%")
                        )
                    
                    if "created_after" in filters:
                        query_conditions.append(
                            Task.created_at >= datetime.fromisoformat(filters["created_after"])
                        )
                    
                    if "created_before" in filters:
                        query_conditions.append(
                            Task.created_at <= datetime.fromisoformat(filters["created_before"])
                        )
                
                # 计算总数
                count_result = await session.execute(
                    session.query(func.count(Task.id)).filter(
                        and_(*query_conditions)
                    )
                )
                total = count_result.scalar()
                
                # 分页查询
                offset = (page - 1) * page_size
                result = await session.execute(
                    session.query(Task)
                    .options(
                        selectinload(Task.agent),
                        selectinload(Task.session)
                    )
                    .filter(and_(*query_conditions))
                    .order_by(desc(Task.created_at))
                    .offset(offset)
                    .limit(page_size)
                )
                tasks = result.scalars().all()
                
                # 格式化结果
                task_list = []
                for task in tasks:
                    task_dict = {
                        "task_id": task.id,
                        "title": task.title,
                        "description": task.description,
                        "type": task.type,
                        "status": task.status,
                        "priority": task.priority,
                        "progress": task.progress,
                        "agent": {
                            "agent_id": task.agent.id,
                            "name": task.agent.name,
                            "type": task.agent.type
                        } if task.agent else None,
                        "session": {
                            "session_id": task.session.id,
                            "title": task.session.title
                        } if task.session else None,
                        "retry_count": task.retry_count,
                        "max_retries": task.max_retries,
                        "scheduled_at": task.scheduled_at.isoformat() if task.scheduled_at else None,
                        "started_at": task.started_at.isoformat() if task.started_at else None,
                        "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                        "created_at": task.created_at.isoformat(),
                        "updated_at": task.updated_at.isoformat()
                    }
                    task_list.append(task_dict)
                
                return {
                    "tasks": task_list,
                    "pagination": {
                        "page": page,
                        "page_size": page_size,
                        "total": total,
                        "pages": (total + page_size - 1) // page_size
                    }
                }
                
            except Exception as e:
                logger.error(f"获取任务列表异常: {str(e)}")
                raise Exception(f"获取任务列表失败: {str(e)}")
    
    async def execute_task(self, user_id: int, task_id: str) -> Dict[str, Any]:
        """
        执行任务
        
        Args:
            user_id: 用户ID
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 执行结果
        """
        async with self.db_manager.get_session() as session:
            try:
                # 查询任务
                result = await session.execute(
                    session.query(Task)
                    .options(selectinload(Task.agent))
                    .filter(
                        and_(
                            Task.id == task_id,
                            Task.deleted_at.is_(None)
                        )
                    )
                )
                task = result.scalar_one_or_none()
                
                if not task:
                    raise ValueError("任务不存在")
                
                # 检查权限
                is_owner = task.user_id == user_id
                has_execute_permission = await self.auth_service.check_permission(
                    user_id, "task", task_id, "execute"
                )
                
                if not is_owner and not has_execute_permission:
                    raise ValueError("没有执行权限")
                
                # 检查任务状态
                if task.status not in [TaskStatus.PENDING, TaskStatus.FAILED, TaskStatus.PAUSED]:
                    raise ValueError(f"任务状态 {task.status} 不允许执行")
                
                # 检查依赖任务
                dependencies_met = await self._check_task_dependencies(session, task_id)
                if not dependencies_met:
                    raise ValueError("依赖任务尚未完成")
                
                # 检查资源限制
                await self._check_resource_limits(user_id)
                
                # 更新任务状态
                task.status = TaskStatus.RUNNING
                task.started_at = datetime.utcnow()
                task.updated_at = datetime.utcnow()
                await session.commit()
                
                # 异步执行任务
                asyncio.create_task(self._execute_task_async(task_id))
                
                logger.info(f"任务开始执行: {task.title}", extra={
                    "user_id": user_id,
                    "task_id": task_id
                })
                
                return {
                    "success": True,
                    "message": "任务开始执行",
                    "data": {
                        "task_id": task_id,
                        "status": task.status,
                        "started_at": task.started_at.isoformat()
                    }
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"任务执行失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"任务执行异常: {str(e)}")
                raise Exception(f"任务执行失败: {str(e)}")
    
    async def pause_task(self, user_id: int, task_id: str) -> Dict[str, Any]:
        """
        暂停任务
        
        Args:
            user_id: 用户ID
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 暂停结果
        """
        async with self.db_manager.get_session() as session:
            try:
                # 查询任务
                result = await session.execute(
                    session.query(Task).filter(
                        and_(
                            Task.id == task_id,
                            Task.deleted_at.is_(None)
                        )
                    )
                )
                task = result.scalar_one_or_none()
                
                if not task:
                    raise ValueError("任务不存在")
                
                # 检查权限
                is_owner = task.user_id == user_id
                has_manage_permission = await self.auth_service.check_permission(
                    user_id, "task", task_id, "manage"
                )
                
                if not is_owner and not has_manage_permission:
                    raise ValueError("没有管理权限")
                
                # 检查任务状态
                if task.status != TaskStatus.RUNNING:
                    raise ValueError("只能暂停正在运行的任务")
                
                # 停止任务执行
                await self._stop_task_execution(task_id)
                
                # 更新任务状态
                task.status = TaskStatus.PAUSED
                task.updated_at = datetime.utcnow()
                await session.commit()
                
                logger.info(f"任务暂停成功: {task.title}", extra={
                    "user_id": user_id,
                    "task_id": task_id
                })
                
                return {
                    "success": True,
                    "message": "任务已暂停",
                    "data": {
                        "task_id": task_id,
                        "status": task.status,
                        "updated_at": task.updated_at.isoformat()
                    }
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"任务暂停失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"任务暂停异常: {str(e)}")
                raise Exception(f"任务暂停失败: {str(e)}")
    
    async def cancel_task(self, user_id: int, task_id: str) -> Dict[str, Any]:
        """
        取消任务
        
        Args:
            user_id: 用户ID
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 取消结果
        """
        async with self.db_manager.get_session() as session:
            try:
                # 查询任务
                result = await session.execute(
                    session.query(Task).filter(
                        and_(
                            Task.id == task_id,
                            Task.deleted_at.is_(None)
                        )
                    )
                )
                task = result.scalar_one_or_none()
                
                if not task:
                    raise ValueError("任务不存在")
                
                # 检查权限
                is_owner = task.user_id == user_id
                has_manage_permission = await self.auth_service.check_permission(
                    user_id, "task", task_id, "manage"
                )
                
                if not is_owner and not has_manage_permission:
                    raise ValueError("没有管理权限")
                
                # 检查任务状态
                if task.status in [TaskStatus.COMPLETED, TaskStatus.CANCELLED]:
                    raise ValueError("任务已完成或已取消")
                
                # 如果任务正在运行，停止执行
                if task.status == TaskStatus.RUNNING:
                    await self._stop_task_execution(task_id)
                
                # 更新任务状态
                task.status = TaskStatus.CANCELLED
                task.completed_at = datetime.utcnow()
                task.updated_at = datetime.utcnow()
                await session.commit()
                
                logger.info(f"任务取消成功: {task.title}", extra={
                    "user_id": user_id,
                    "task_id": task_id
                })
                
                return {
                    "success": True,
                    "message": "任务已取消",
                    "data": {
                        "task_id": task_id,
                        "status": task.status,
                        "completed_at": task.completed_at.isoformat()
                    }
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"任务取消失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"任务取消异常: {str(e)}")
                raise Exception(f"任务取消失败: {str(e)}")
    
    async def get_task_statistics(self, user_id: int, 
                                 date_range: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        获取任务统计信息
        
        Args:
            user_id: 用户ID
            date_range: 日期范围
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        async with self.db_manager.get_session() as session:
            try:
                # 构建查询条件
                query_conditions = [
                    Task.user_id == user_id,
                    Task.deleted_at.is_(None)
                ]
                
                if date_range:
                    if "start_date" in date_range:
                        query_conditions.append(
                            Task.created_at >= datetime.fromisoformat(date_range["start_date"])
                        )
                    if "end_date" in date_range:
                        query_conditions.append(
                            Task.created_at <= datetime.fromisoformat(date_range["end_date"])
                        )
                
                # 总任务数
                total_result = await session.execute(
                    session.query(func.count(Task.id)).filter(
                        and_(*query_conditions)
                    )
                )
                total_tasks = total_result.scalar()
                
                # 按状态统计
                status_result = await session.execute(
                    session.query(Task.status, func.count(Task.id))
                    .filter(and_(*query_conditions))
                    .group_by(Task.status)
                )
                tasks_by_status = {row[0]: row[1] for row in status_result.fetchall()}
                
                # 按类型统计
                type_result = await session.execute(
                    session.query(Task.type, func.count(Task.id))
                    .filter(and_(*query_conditions))
                    .group_by(Task.type)
                )
                tasks_by_type = {row[0]: row[1] for row in type_result.fetchall()}
                
                # 按优先级统计
                priority_result = await session.execute(
                    session.query(Task.priority, func.count(Task.id))
                    .filter(and_(*query_conditions))
                    .group_by(Task.priority)
                )
                tasks_by_priority = {row[0]: row[1] for row in priority_result.fetchall()}
                
                # 成功率计算
                completed_count = tasks_by_status.get(TaskStatus.COMPLETED, 0)
                failed_count = tasks_by_status.get(TaskStatus.FAILED, 0)
                total_finished = completed_count + failed_count
                success_rate = (completed_count / total_finished * 100) if total_finished > 0 else 0
                
                # 平均执行时间
                duration_result = await session.execute(
                    session.query(
                        func.avg(
                            func.extract('epoch', Task.completed_at - Task.started_at)
                        )
                    ).filter(
                        and_(
                            *query_conditions,
                            Task.status == TaskStatus.COMPLETED,
                            Task.started_at.is_not(None),
                            Task.completed_at.is_not(None)
                        )
                    )
                )
                avg_duration_seconds = duration_result.scalar() or 0
                
                return {
                    "total_tasks": total_tasks,
                    "tasks_by_status": tasks_by_status,
                    "tasks_by_type": tasks_by_type,
                    "tasks_by_priority": tasks_by_priority,
                    "success_rate": round(success_rate, 2),
                    "average_duration_seconds": round(avg_duration_seconds, 2),
                    "date_range": date_range,
                    "generated_at": datetime.utcnow().isoformat()
                }
                
            except Exception as e:
                logger.error(f"获取任务统计异常: {str(e)}")
                raise Exception(f"获取任务统计失败: {str(e)}")
    
    async def _execute_task_async(self, task_id: str):
        """
        异步执行任务
        
        Args:
            task_id: 任务ID
        """
        try:
            async with self.db_manager.get_session() as session:
                # 获取任务详情
                result = await session.execute(
                    session.query(Task)
                    .options(
                        selectinload(Task.agent),
                        selectinload(Task.steps)
                    )
                    .filter(Task.id == task_id)
                )
                task = result.scalar_one_or_none()
                
                if not task:
                    logger.error(f"任务不存在: {task_id}")
                    return
                
                # 记录任务到运行池
                self._running_tasks[task_id] = {
                    "task": task,
                    "start_time": datetime.utcnow(),
                    "cancelled": False
                }
                
                try:
                    # 创建智能体实例
                    agent_instance = await self._create_agent_instance(task.agent)
                    
                    # 准备输入数据
                    input_data = json.loads(task.input_data) if task.input_data else {}
                    config = json.loads(task.config) if task.config else {}
                    
                    # 执行任务步骤
                    if task.steps:
                        await self._execute_task_steps(session, task, agent_instance)
                    else:
                        # 直接执行智能体
                        result = await agent_instance.run(input_data, config)
                        
                        # 保存结果
                        await self._save_task_result(session, task_id, "final", result)
                    
                    # 更新任务状态为完成
                    task.status = TaskStatus.COMPLETED
                    task.progress = 100
                    task.completed_at = datetime.utcnow()
                    task.updated_at = datetime.utcnow()
                    
                    await session.commit()
                    
                    logger.info(f"任务执行完成: {task.title}", extra={
                        "task_id": task_id
                    })
                    
                except asyncio.CancelledError:
                    # 任务被取消
                    task.status = TaskStatus.CANCELLED
                    task.completed_at = datetime.utcnow()
                    task.updated_at = datetime.utcnow()
                    await session.commit()
                    
                    logger.info(f"任务被取消: {task.title}", extra={
                        "task_id": task_id
                    })
                    
                except Exception as e:
                    # 任务执行失败
                    task.status = TaskStatus.FAILED
                    task.error_message = str(e)
                    task.completed_at = datetime.utcnow()
                    task.updated_at = datetime.utcnow()
                    
                    # 检查是否需要重试
                    if task.retry_count < task.max_retries:
                        task.retry_count += 1
                        task.status = TaskStatus.PENDING
                        task.completed_at = None
                        
                        # 重新加入队列
                        await self._enqueue_task(task_id, task.priority)
                        
                        logger.warning(f"任务执行失败，将重试: {task.title}", extra={
                            "task_id": task_id,
                            "retry_count": task.retry_count,
                            "error": str(e)
                        })
                    else:
                        logger.error(f"任务执行失败: {task.title}", extra={
                            "task_id": task_id,
                            "error": str(e)
                        })
                    
                    await session.commit()
                
                finally:
                    # 从运行池中移除
                    if task_id in self._running_tasks:
                        del self._running_tasks[task_id]
                
        except Exception as e:
            logger.error(f"任务执行异常: {str(e)}", extra={
                "task_id": task_id
            })
    
    async def _execute_task_steps(self, session, task: Task, agent_instance: BaseAgent):
        """
        执行任务步骤
        
        Args:
            session: 数据库会话
            task: 任务对象
            agent_instance: 智能体实例
        """
        steps = sorted(task.steps, key=lambda x: x.step_order)
        step_results = {}
        
        for step in steps:
            # 检查任务是否被取消
            if (task.id in self._running_tasks and 
                self._running_tasks[task.id].get("cancelled", False)):
                raise asyncio.CancelledError("任务被取消")
            
            try:
                # 更新步骤状态
                step.status = "running"
                step.started_at = datetime.utcnow()
                await session.commit()
                
                # 准备步骤输入
                step_config = json.loads(step.config) if step.config else {}
                step_input = step_results.copy()  # 使用之前步骤的结果
                
                # 执行步骤
                if step.type == "action":
                    result = await agent_instance.execute_action(step.name, step_input, step_config)
                elif step.type == "condition":
                    result = await agent_instance.evaluate_condition(step.name, step_input, step_config)
                elif step.type == "loop":
                    result = await agent_instance.execute_loop(step.name, step_input, step_config)
                else:
                    result = await agent_instance.execute_custom_step(step.type, step.name, step_input, step_config)
                
                # 保存步骤结果
                step.result = json.dumps(result)
                step.status = "completed"
                step.progress = 100
                step.completed_at = datetime.utcnow()
                
                # 更新任务进度
                completed_steps = sum(1 for s in steps if s.status == "completed")
                task.progress = int((completed_steps / len(steps)) * 100)
                
                await session.commit()
                
                # 保存结果供后续步骤使用
                step_results[step.name] = result
                
                logger.debug(f"步骤执行完成: {step.name}", extra={
                    "task_id": task.id,
                    "step_id": step.id
                })
                
            except Exception as e:
                # 步骤执行失败
                step.status = "failed"
                step.error_message = str(e)
                step.completed_at = datetime.utcnow()
                await session.commit()
                
                logger.error(f"步骤执行失败: {step.name}", extra={
                    "task_id": task.id,
                    "step_id": step.id,
                    "error": str(e)
                })
                
                raise Exception(f"步骤 {step.name} 执行失败: {str(e)}")
        
        # 保存最终结果
        await self._save_task_result(session, task.id, "final", step_results)
    
    async def _create_agent_instance(self, agent: Agent) -> BaseAgent:
        """
        创建智能体实例
        
        Args:
            agent: 智能体模型
            
        Returns:
            BaseAgent: 智能体实例
        """
        # 这里应该根据智能体类型创建相应的实例
        # 暂时返回一个基础实例
        from adk.agents.base_agent import BaseAgent
        
        config = json.loads(agent.config) if agent.config else {}
        
        # 根据智能体类型创建实例
        if agent.type == "llm_agent":
            from adk.agents.llm_agent import LLMAgent
            return LLMAgent(config)
        elif agent.type == "tool_agent":
            from adk.agents.tool_agent import ToolAgent
            return ToolAgent(config)
        else:
            return BaseAgent(config)
    
    async def _save_task_result(self, session, task_id: str, result_type: str, data: Any):
        """
        保存任务结果
        
        Args:
            session: 数据库会话
            task_id: 任务ID
            result_type: 结果类型
            data: 结果数据
        """
        result = TaskResult(
            id=str(uuid.uuid4()),
            task_id=task_id,
            type=result_type,
            data=json.dumps(data),
            metadata=json.dumps({}),
            created_at=datetime.utcnow()
        )
        
        session.add(result)
        await session.commit()
    
    async def _check_user_task_quota(self, session, user_id: int):
        """
        检查用户任务配额
        
        Args:
            session: 数据库会话
            user_id: 用户ID
            
        Raises:
            ValueError: 超出配额限制
        """
        # 检查并发任务数量
        running_count_result = await session.execute(
            session.query(func.count(Task.id)).filter(
                and_(
                    Task.user_id == user_id,
                    Task.status == TaskStatus.RUNNING,
                    Task.deleted_at.is_(None)
                )
            )
        )
        running_count = running_count_result.scalar()
        
        if running_count >= self._resource_limits["max_concurrent_tasks"]:
            raise ValueError(f"并发任务数量超出限制 ({self._resource_limits['max_concurrent_tasks']})")
        
        # 检查今日任务创建数量
        today = datetime.utcnow().date()
        today_start = datetime.combine(today, datetime.min.time())
        today_end = datetime.combine(today, datetime.max.time())
        
        today_count_result = await session.execute(
            session.query(func.count(Task.id)).filter(
                and_(
                    Task.user_id == user_id,
                    Task.created_at >= today_start,
                    Task.created_at <= today_end,
                    Task.deleted_at.is_(None)
                )
            )
        )
        today_count = today_count_result.scalar()
        
        daily_limit = self.settings.max_daily_tasks_per_user
        if today_count >= daily_limit:
            raise ValueError(f"今日任务创建数量超出限制 ({daily_limit})")
    
    async def _check_task_dependencies(self, session, task_id: str) -> bool:
        """
        检查任务依赖是否满足
        
        Args:
            session: 数据库会话
            task_id: 任务ID
            
        Returns:
            bool: 依赖是否满足
        """
        dependencies_result = await session.execute(
            session.query(TaskDependency)
            .join(Task, TaskDependency.depends_on_task_id == Task.id)
            .filter(
                and_(
                    TaskDependency.task_id == task_id,
                    Task.status != TaskStatus.COMPLETED
                )
            )
        )
        
        unmet_dependencies = dependencies_result.fetchall()
        return len(unmet_dependencies) == 0
    
    async def _check_resource_limits(self, user_id: int):
        """
        检查资源限制
        
        Args:
            user_id: 用户ID
            
        Raises:
            ValueError: 超出资源限制
        """
        # 检查用户当前运行的任务数量
        user_running_tasks = sum(
            1 for task_info in self._running_tasks.values()
            if task_info["task"].user_id == user_id
        )
        
        if user_running_tasks >= self._resource_limits["max_concurrent_tasks"]:
            raise ValueError("并发任务数量超出限制")
    
    async def _enqueue_task(self, task_id: str, priority: str):
        """
        将任务加入执行队列
        
        Args:
            task_id: 任务ID
            priority: 优先级
        """
        try:
            priority_enum = TaskPriority(priority)
            await self._task_queues[priority_enum].put(task_id)
            
            logger.debug(f"任务加入队列: {task_id}", extra={
                "priority": priority
            })
        except Exception as e:
            logger.error(f"任务入队失败: {str(e)}", extra={
                "task_id": task_id,
                "priority": priority
            })
    
    async def _stop_task_execution(self, task_id: str):
        """
        停止任务执行
        
        Args:
            task_id: 任务ID
        """
        if task_id in self._running_tasks:
            self._running_tasks[task_id]["cancelled"] = True
            
            logger.info(f"任务执行已停止: {task_id}")