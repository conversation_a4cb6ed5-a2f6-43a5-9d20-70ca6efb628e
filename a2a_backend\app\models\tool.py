#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统工具模型

定义工具相关的数据模型
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Index, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from .base import BaseModel


class Tool(BaseModel):
    """
    工具模型
    
    存储智能体可使用的工具信息
    """
    
    __tablename__ = "tools"
    
    # 工具基本信息
    tool_id = Column(
        String(36),
        nullable=False,
        unique=True,
        comment="工具唯一标识"
    )
    
    name = Column(
        String(100),
        nullable=False,
        comment="工具名称"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="工具描述"
    )
    
    # 工具类型和分类
    tool_type = Column(
        String(50),
        nullable=False,
        comment="工具类型"
    )
    
    category = Column(
        String(50),
        nullable=False,
        comment="工具分类"
    )
    
    # 工具定义
    schema = Column(
        Text,
        nullable=False,
        comment="工具模式定义（JSON Schema）"
    )
    
    implementation = Column(
        Text,
        nullable=True,
        comment="工具实现代码"
    )
    
    # 工具配置
    config = Column(
        Text,
        nullable=True,
        comment="工具配置（JSON格式）"
    )
    
    # 工具版本
    version = Column(
        String(20),
        nullable=False,
        default="1.0.0",
        comment="版本号"
    )
    
    # 工具状态
    status = Column(
        String(20),
        nullable=False,
        default="active",
        comment="工具状态"
    )
    
    # 安全和权限
    is_public = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否公开"
    )
    
    requires_auth = Column(
        Boolean,
        nullable=False,
        default=True,
        comment="是否需要认证"
    )
    
    permission_level = Column(
        String(20),
        nullable=False,
        default="user",
        comment="权限级别"
    )
    
    # 创建者信息
    created_by = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        comment="创建者用户ID"
    )
    
    # 统计信息
    usage_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="使用次数"
    )
    
    success_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="成功次数"
    )
    
    average_response_time = Column(
        Float,
        nullable=False,
        default=0.0,
        comment="平均响应时间（秒）"
    )
    
    # 时间信息
    last_used_at = Column(
        DateTime,
        nullable=True,
        comment="最后使用时间"
    )
    
    # 关联关系
    creator = relationship("User", back_populates="created_tools")
    agent_tools = relationship("AgentTool", back_populates="tool", cascade="all, delete-orphan")
    tool_executions = relationship("ToolExecution", back_populates="tool", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index("idx_tools_tool_id", "tool_id"),
        Index("idx_tools_name", "name"),
        Index("idx_tools_type", "tool_type"),
        Index("idx_tools_category", "category"),
        Index("idx_tools_status", "status"),
        Index("idx_tools_is_public", "is_public"),
        Index("idx_tools_created_by", "created_by"),
        Index("idx_tools_is_active", "is_active"),
    )
    
    def set_schema(self, schema: Dict[str, Any]) -> None:
        """
        设置工具模式定义
        
        Args:
            schema: 工具模式定义字典
        """
        import json
        self.schema = json.dumps(schema, ensure_ascii=False)
    
    def get_schema(self) -> Dict[str, Any]:
        """
        获取工具模式定义
        
        Returns:
            Dict[str, Any]: 工具模式定义字典
        """
        if not self.schema:
            return {}
        
        try:
            import json
            return json.loads(self.schema)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_config(self, config: Dict[str, Any]) -> None:
        """
        设置工具配置
        
        Args:
            config: 配置字典
        """
        import json
        self.config = json.dumps(config, ensure_ascii=False)
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取工具配置
        
        Returns:
            Dict[str, Any]: 配置字典
        """
        if not self.config:
            return {}
        
        try:
            import json
            return json.loads(self.config)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def update_usage_stats(self, response_time: float, success: bool = True) -> None:
        """
        更新使用统计
        
        Args:
            response_time: 响应时间（秒）
            success: 是否成功
        """
        self.usage_count += 1
        if success:
            self.success_count += 1
        
        # 更新平均响应时间
        if self.usage_count == 1:
            self.average_response_time = response_time
        else:
            self.average_response_time = (
                (self.average_response_time * (self.usage_count - 1) + response_time) / self.usage_count
            )
        
        self.last_used_at = datetime.now()
    
    @property
    def success_rate(self) -> float:
        """
        获取成功率
        
        Returns:
            float: 成功率
        """
        if self.usage_count == 0:
            return 0.0
        return self.success_count / self.usage_count
    
    def __repr__(self) -> str:
        return f"<Tool(id={self.id}, tool_id='{self.tool_id}', name='{self.name}')>"


class AgentTool(BaseModel):
    """
    智能体工具关联模型
    
    存储智能体与工具的关联信息
    """
    
    __tablename__ = "agent_tools"
    
    # 关联关系
    agent_id = Column(
        Integer,
        ForeignKey("agents.id", ondelete="CASCADE"),
        nullable=False,
        comment="智能体ID"
    )
    
    tool_id = Column(
        Integer,
        ForeignKey("tools.id", ondelete="CASCADE"),
        nullable=False,
        comment="工具ID"
    )
    
    # 配置信息
    config = Column(
        Text,
        nullable=True,
        comment="工具配置（JSON格式）"
    )
    
    # 使用权限
    is_enabled = Column(
        Boolean,
        nullable=False,
        default=True,
        comment="是否启用"
    )
    
    # 优先级
    priority = Column(
        Integer,
        nullable=False,
        default=0,
        comment="优先级"
    )
    
    # 统计信息
    usage_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="使用次数"
    )
    
    # 关联关系
    agent = relationship("Agent", back_populates="agent_tools")
    tool = relationship("Tool", back_populates="agent_tools")
    
    # 索引
    __table_args__ = (
        Index("idx_agent_tools_agent_id", "agent_id"),
        Index("idx_agent_tools_tool_id", "tool_id"),
        Index("idx_agent_tools_is_enabled", "is_enabled"),
        Index("idx_agent_tools_priority", "priority"),
        # 复合唯一索引
        Index("idx_agent_tools_unique", "agent_id", "tool_id", unique=True),
    )
    
    def set_config(self, config: Dict[str, Any]) -> None:
        """
        设置工具配置
        
        Args:
            config: 配置字典
        """
        import json
        self.config = json.dumps(config, ensure_ascii=False)
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取工具配置
        
        Returns:
            Dict[str, Any]: 配置字典
        """
        if not self.config:
            return {}
        
        try:
            import json
            return json.loads(self.config)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def increment_usage(self) -> None:
        """
        增加使用次数
        """
        self.usage_count += 1
    
    def __repr__(self) -> str:
        return f"<AgentTool(id={self.id}, agent_id={self.agent_id}, tool_id={self.tool_id})>"


class ToolExecution(BaseModel):
    """
    工具执行记录模型
    
    记录工具的执行历史
    """
    
    __tablename__ = "tool_executions"
    
    # 关联关系
    tool_id = Column(
        Integer,
        ForeignKey("tools.id", ondelete="CASCADE"),
        nullable=False,
        comment="工具ID"
    )
    
    agent_id = Column(
        Integer,
        ForeignKey("agents.id", ondelete="SET NULL"),
        nullable=True,
        comment="智能体ID"
    )
    
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        comment="用户ID"
    )
    
    session_id = Column(
        Integer,
        ForeignKey("sessions.id", ondelete="SET NULL"),
        nullable=True,
        comment="会话ID"
    )
    
    message_id = Column(
        Integer,
        ForeignKey("messages.id", ondelete="SET NULL"),
        nullable=True,
        comment="消息ID"
    )
    
    # 执行信息
    execution_id = Column(
        String(36),
        nullable=False,
        unique=True,
        comment="执行唯一标识"
    )
    
    # 输入输出数据
    input_data = Column(
        Text,
        nullable=True,
        comment="输入数据（JSON格式）"
    )
    
    output_data = Column(
        Text,
        nullable=True,
        comment="输出数据（JSON格式）"
    )
    
    # 执行状态
    status = Column(
        String(20),
        nullable=False,
        default="pending",
        comment="执行状态"
    )
    
    # 错误信息
    error_message = Column(
        Text,
        nullable=True,
        comment="错误信息"
    )
    
    error_type = Column(
        String(100),
        nullable=True,
        comment="错误类型"
    )
    
    # 时间信息
    started_at = Column(
        DateTime,
        nullable=False,
        default=func.now(),
        comment="开始时间"
    )
    
    completed_at = Column(
        DateTime,
        nullable=True,
        comment="完成时间"
    )
    
    # 性能指标
    duration = Column(
        Float,
        nullable=True,
        comment="执行时长（秒）"
    )
    
    # 关联关系
    tool = relationship("Tool", back_populates="tool_executions")
    agent = relationship("Agent", back_populates="tool_executions")
    user = relationship("User", back_populates="tool_executions")
    session = relationship("Session", back_populates="tool_executions")
    message = relationship("Message", back_populates="tool_executions")
    
    # 索引
    __table_args__ = (
        Index("idx_tool_executions_tool_id", "tool_id"),
        Index("idx_tool_executions_agent_id", "agent_id"),
        Index("idx_tool_executions_user_id", "user_id"),
        Index("idx_tool_executions_session_id", "session_id"),
        Index("idx_tool_executions_message_id", "message_id"),
        Index("idx_tool_executions_execution_id", "execution_id"),
        Index("idx_tool_executions_status", "status"),
        Index("idx_tool_executions_started_at", "started_at"),
    )
    
    def set_input_data(self, data: Dict[str, Any]) -> None:
        """
        设置输入数据
        
        Args:
            data: 输入数据字典
        """
        import json
        self.input_data = json.dumps(data, ensure_ascii=False)
    
    def get_input_data(self) -> Dict[str, Any]:
        """
        获取输入数据
        
        Returns:
            Dict[str, Any]: 输入数据字典
        """
        if not self.input_data:
            return {}
        
        try:
            import json
            return json.loads(self.input_data)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_output_data(self, data: Dict[str, Any]) -> None:
        """
        设置输出数据
        
        Args:
            data: 输出数据字典
        """
        import json
        self.output_data = json.dumps(data, ensure_ascii=False)
    
    def get_output_data(self) -> Dict[str, Any]:
        """
        获取输出数据
        
        Returns:
            Dict[str, Any]: 输出数据字典
        """
        if not self.output_data:
            return {}
        
        try:
            import json
            return json.loads(self.output_data)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def complete_execution(self, output_data: Dict[str, Any] = None) -> None:
        """
        完成执行
        
        Args:
            output_data: 输出数据
        """
        self.status = "completed"
        self.completed_at = datetime.now()
        self.duration = (self.completed_at - self.started_at).total_seconds()
        
        if output_data:
            self.set_output_data(output_data)
    
    def fail_execution(self, error_message: str, error_type: str = None) -> None:
        """
        执行失败
        
        Args:
            error_message: 错误信息
            error_type: 错误类型
        """
        self.status = "failed"
        self.error_message = error_message
        self.error_type = error_type
        self.completed_at = datetime.now()
        self.duration = (self.completed_at - self.started_at).total_seconds()
    
    def __repr__(self) -> str:
        return f"<ToolExecution(id={self.id}, execution_id='{self.execution_id}', status='{self.status}')>"


class ToolCategory(BaseModel):
    """
    工具分类模型
    
    存储工具分类信息
    """
    
    __tablename__ = "tool_categories"
    
    # 分类信息
    name = Column(
        String(50),
        nullable=False,
        unique=True,
        comment="分类名称"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="分类描述"
    )
    
    # 分类层级
    parent_id = Column(
        Integer,
        ForeignKey("tool_categories.id", ondelete="SET NULL"),
        nullable=True,
        comment="父分类ID"
    )
    
    # 排序
    display_order = Column(
        Integer,
        nullable=False,
        default=0,
        comment="显示顺序"
    )
    
    # 图标
    icon = Column(
        String(100),
        nullable=True,
        comment="图标"
    )
    
    # 关联关系
    parent = relationship("ToolCategory", back_populates="children", remote_side="ToolCategory.id")
    children = relationship("ToolCategory", back_populates="parent")
    
    # 索引
    __table_args__ = (
        Index("idx_tool_categories_name", "name"),
        Index("idx_tool_categories_parent_id", "parent_id"),
        Index("idx_tool_categories_display_order", "display_order"),
        Index("idx_tool_categories_is_active", "is_active"),
    )
    
    def __repr__(self) -> str:
        return f"<ToolCategory(id={self.id}, name='{self.name}')>"