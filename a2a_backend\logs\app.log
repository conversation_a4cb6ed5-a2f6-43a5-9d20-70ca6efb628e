2025-06-26 12:30:37.256 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-26 12:30:51.249 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-26 12:30:51.249 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-26 12:30:51.250 | INFO     | app.core.storage:create_optimized_engine:566 - 数据库引擎创建完成: default
2025-06-26 12:30:51.250 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-26 12:30:51.251 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-26 12:30:51.251 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-26 12:31:14.291 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-26 12:31:14.292 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-26 12:31:14.293 | INFO     | app.core.storage:cleanup_storage:761 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-26 12:31:14.293 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-26 12:31:14.294 | INFO     | app.core.database:close:110 - 数据库连接已关闭
2025-06-26 12:31:14.294 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-26 12:31:14.294 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-26 12:31:37.805 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-26 12:31:37.805 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-26 12:31:37.808 | INFO     | app.core.storage:create_optimized_engine:566 - 数据库引擎创建完成: default
2025-06-26 12:31:37.808 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-26 12:31:37.808 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-26 12:31:37.808 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-26 12:36:24.873 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-26 12:36:24.873 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-26 12:36:24.873 | INFO     | app.core.storage:cleanup_storage:761 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-26 12:36:24.873 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-26 12:36:24.873 | INFO     | app.core.database:close:110 - 数据库连接已关闭
2025-06-26 12:36:24.873 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-26 12:36:24.873 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-26 13:27:49.061 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-26 13:27:49.061 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-26 13:27:49.062 | INFO     | app.core.storage:create_optimized_engine:566 - 数据库引擎创建完成: default
2025-06-26 13:27:49.062 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-26 13:27:49.062 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-26 13:27:49.062 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-26 13:28:25.878 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-26 13:28:25.880 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-26 13:28:25.880 | INFO     | app.core.storage:cleanup_storage:761 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-26 13:28:25.880 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-26 13:28:25.880 | INFO     | app.core.database:close:110 - 数据库连接已关闭
2025-06-26 13:28:25.880 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-26 13:28:25.882 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-26 13:29:25.227 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-26 13:29:25.227 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-26 13:38:30.216 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-26 13:38:30.216 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-26 13:38:53.293 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-26 13:38:53.293 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-26 13:39:08.893 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-26 13:39:08.893 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-26 13:39:42.157 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-26 13:39:42.157 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-26 13:39:57.313 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-26 13:39:57.313 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-26 13:40:11.921 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-26 13:40:11.921 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-26 13:40:33.706 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-26 13:40:33.707 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-26 13:40:54.142 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-26 13:40:54.142 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-26 13:41:11.155 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-26 13:41:11.155 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-26 13:41:11.156 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-26 13:41:11.156 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-26 13:41:11.157 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-26 13:41:11.157 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-26 13:41:27.166 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-26 13:41:27.166 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-26 13:41:27.166 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-26 13:41:27.166 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-26 13:41:27.166 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-26 13:41:27.166 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-26 13:41:53.934 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-26 13:41:53.934 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-26 13:41:53.934 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-26 13:41:53.934 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-26 13:41:53.934 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-26 13:41:53.934 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-26 13:41:53.934 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-26 13:42:16.569 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-26 13:42:16.570 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-26 13:42:16.570 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-26 13:42:16.573 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-26 13:42:16.573 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-26 13:42:16.573 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-26 13:42:42.912 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-26 13:42:42.912 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-26 13:42:42.912 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-26 13:42:42.912 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-26 13:42:42.916 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-26 13:42:42.916 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-26 13:42:42.916 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 20:26:07.175 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 20:26:07.175 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 20:26:07.175 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 20:26:07.175 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 20:26:07.175 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 20:26:07.175 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 20:31:06.300 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 20:31:06.300 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 20:31:06.300 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 20:31:06.300 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 20:31:06.308 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 20:31:06.309 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 20:31:06.309 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 20:31:25.508 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 20:31:25.510 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 20:31:25.510 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 20:31:25.510 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 20:31:25.514 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 20:31:25.514 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 20:49:51.448 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 20:49:51.448 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 20:49:51.456 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 20:49:51.456 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 20:49:51.456 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 20:49:51.456 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 21:12:11.200 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 21:12:11.200 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 21:12:11.201 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 21:12:11.201 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 21:12:11.202 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 21:12:11.202 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 21:12:11.202 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 21:12:19.312 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 21:12:19.312 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 21:12:19.312 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 21:12:19.317 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 21:12:19.317 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 21:12:19.317 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 21:12:43.518 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 21:12:43.519 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 21:12:43.519 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 21:12:43.519 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 21:12:43.522 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 21:12:43.522 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 21:12:43.522 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 21:12:51.968 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 21:12:51.969 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 21:12:51.969 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 21:12:51.969 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 21:12:51.969 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 21:12:51.969 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 21:16:57.446 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 21:16:57.446 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 21:16:57.446 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 21:16:57.446 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 21:16:57.446 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 21:16:57.446 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 21:16:57.450 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 21:17:05.198 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 21:17:05.198 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 21:17:05.198 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 21:17:05.198 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 21:17:05.198 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 21:17:05.198 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 21:17:14.217 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: GET /api/v1/auth/login
2025-06-27 21:17:14.221 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: GET /api/v1/auth/login - 405 (0.003s)
2025-06-27 21:18:12.857 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: OPTIONS /api/v1/auth/login
2025-06-27 21:18:12.857 | INFO     | app.middleware.logging:dispatch:105 - 请求完成: OPTIONS /api/v1/auth/login - 200 (0.000s)
2025-06-27 21:18:12.862 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 21:18:12.864 | ERROR    | app.api.v1.auth:login_user:344 - 用户登录异常: RateLimiter.check_rate_limit() got an unexpected keyword argument 'key'
2025-06-27 21:18:12.864 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.002s)
2025-06-27 21:19:50.411 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 21:19:50.411 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 21:19:50.411 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 21:19:50.411 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 21:19:50.411 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 21:19:50.411 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 21:19:50.411 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 21:20:43.811 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 21:20:43.811 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 21:20:43.812 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 21:20:43.812 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 21:20:43.813 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 21:20:43.813 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 21:21:24.605 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 21:21:24.605 | ERROR    | app.api.v1.auth:login_user:344 - 用户登录异常: RateLimiter.check_rate_limit() got an unexpected keyword argument 'key'
2025-06-27 21:21:24.605 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.000s)
2025-06-27 21:24:11.898 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 21:24:11.900 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 21:24:11.900 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 21:24:11.901 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 21:24:11.901 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 21:24:11.901 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 21:24:11.901 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 21:24:19.920 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 21:24:19.920 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 21:24:19.923 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 21:24:19.923 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 21:24:19.924 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 21:24:19.924 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 21:24:57.142 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 21:24:57.147 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 21:24:57.148 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 21:24:57.148 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 21:24:57.148 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 21:24:57.148 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 21:24:57.148 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 21:25:13.534 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 21:25:13.541 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 21:25:13.544 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 21:25:13.551 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 21:25:13.551 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 21:25:13.556 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 21:25:22.654 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 21:25:22.654 | ERROR    | app.api.v1.auth:login_user:341 - 用户登录异常: object bool can't be used in 'await' expression
2025-06-27 21:25:22.660 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.005s)
2025-06-27 21:26:07.198 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 21:26:07.198 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 21:26:07.198 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 21:26:07.198 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 21:26:07.198 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 21:26:07.198 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 21:26:07.198 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 21:26:15.036 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 21:26:15.036 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 21:26:15.036 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 21:26:15.036 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 21:26:15.041 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 21:26:15.041 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 21:26:34.019 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 21:26:34.020 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 21:26:34.020 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 21:26:34.020 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 21:26:34.020 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 21:26:34.020 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 21:26:34.020 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 21:30:38.294 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 21:30:38.294 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 21:30:38.294 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 21:30:38.294 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 21:30:38.294 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 21:30:38.294 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 21:30:44.844 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: OPTIONS /api/v1/auth/login
2025-06-27 21:30:44.844 | INFO     | app.middleware.logging:dispatch:105 - 请求完成: OPTIONS /api/v1/auth/login - 200 (0.000s)
2025-06-27 21:30:44.850 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 21:30:44.851 | ERROR    | app.api.v1.auth:login_user:340 - 用户登录异常: UserService.authenticate_user() missing 1 required positional argument: 'password'
2025-06-27 21:30:44.851 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.004s)
2025-06-27 21:31:46.138 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 21:31:46.140 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 21:31:46.140 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 21:31:46.140 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 21:31:46.140 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 21:31:46.140 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 21:31:46.140 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 21:31:54.966 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 21:31:54.966 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 21:31:54.966 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 21:31:54.966 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 21:31:54.966 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 21:31:54.966 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 21:33:28.695 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 21:33:28.697 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 21:33:28.697 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 21:33:28.697 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 21:33:28.697 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 21:33:28.697 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 21:33:28.697 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 21:38:54.469 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 21:38:54.474 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 21:38:54.474 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 21:38:54.474 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 21:38:54.476 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 21:38:54.476 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 21:38:57.129 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 21:38:57.132 | ERROR    | app.api.v1.auth:login_user:336 - 用户登录异常: 'coroutine' object does not support the asynchronous context manager protocol
2025-06-27 21:38:57.132 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.003s)
2025-06-27 21:42:57.079 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 21:42:57.079 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 21:42:57.079 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 21:42:57.079 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 21:42:57.079 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 21:42:57.079 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 21:42:57.079 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 21:55:34.886 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 21:55:34.886 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 21:55:34.886 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 21:55:34.886 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 21:55:34.886 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 21:55:34.886 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 21:55:38.334 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: OPTIONS /api/v1/auth/login
2025-06-27 21:55:38.334 | INFO     | app.middleware.logging:dispatch:105 - 请求完成: OPTIONS /api/v1/auth/login - 200 (0.000s)
2025-06-27 21:55:38.334 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 21:55:38.351 | ERROR    | app.services.user_service:authenticate_user:253 - 用户认证异常: Could not determine join condition between parent/child tables on relationship User.agents - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-06-27 21:55:38.352 | ERROR    | app.api.v1.auth:login_user:328 - 用户登录异常: 认证失败: Could not determine join condition between parent/child tables on relationship User.agents - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-06-27 21:55:38.352 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.017s)
2025-06-27 21:58:37.874 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 21:58:37.874 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 21:58:37.874 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 21:58:37.874 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 21:58:37.874 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 21:58:37.874 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 21:58:37.874 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 21:58:45.481 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 21:58:45.481 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 21:58:45.483 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 21:58:45.483 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 21:58:45.484 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 21:58:45.484 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 21:59:01.518 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 21:59:01.519 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 21:59:01.519 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 21:59:01.519 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 21:59:01.519 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 21:59:01.519 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 21:59:01.519 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 21:59:09.183 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 21:59:09.183 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 21:59:09.183 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 21:59:09.183 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 21:59:09.183 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 21:59:09.183 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:00:13.914 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 22:00:13.914 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 22:00:13.914 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 22:00:13.916 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 22:00:13.916 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 22:00:13.916 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 22:00:13.916 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 22:06:38.967 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:06:38.967 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:06:38.967 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:06:38.967 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:06:38.970 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:06:38.970 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:06:40.802 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 22:06:40.802 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 22:06:40.802 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 22:06:40.804 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 22:06:40.804 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 22:06:40.804 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 22:06:40.804 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 22:10:32.590 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:10:32.590 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:10:32.590 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:10:32.590 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:10:32.590 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:10:32.590 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:10:36.193 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: OPTIONS /api/v1/auth/login
2025-06-27 22:10:36.193 | INFO     | app.middleware.logging:dispatch:105 - 请求完成: OPTIONS /api/v1/auth/login - 200 (0.000s)
2025-06-27 22:10:36.193 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:10:36.210 | ERROR    | app.services.user_service:authenticate_user:253 - 用户认证异常: Could not determine join condition between parent/child tables on relationship User.artifacts - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-06-27 22:10:36.210 | ERROR    | app.api.v1.auth:login_user:328 - 用户登录异常: 认证失败: Could not determine join condition between parent/child tables on relationship User.artifacts - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-06-27 22:10:36.210 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.017s)
2025-06-27 22:12:45.925 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 22:12:45.925 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 22:12:45.925 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 22:12:45.925 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 22:12:45.925 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 22:12:45.925 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 22:12:45.925 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 22:12:54.413 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:12:54.413 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:12:54.413 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:12:54.413 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:12:54.413 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:12:54.413 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:13:27.485 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 22:13:27.486 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 22:13:27.486 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 22:13:27.487 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 22:13:27.487 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 22:13:27.489 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 22:13:27.489 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 22:13:36.343 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:13:36.343 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:13:36.343 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:13:36.343 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:13:36.343 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:13:36.343 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:14:59.621 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 22:14:59.621 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 22:14:59.621 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 22:14:59.621 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 22:14:59.621 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 22:14:59.621 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 22:14:59.621 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 22:15:08.208 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:15:08.208 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:15:08.208 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:15:08.208 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:15:08.208 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:15:08.208 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:15:19.552 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 22:15:19.553 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 22:15:19.553 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 22:15:19.553 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 22:15:19.553 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 22:15:19.553 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 22:15:19.553 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 22:15:28.481 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:15:28.482 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:15:28.483 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:15:28.483 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:15:28.483 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:15:28.484 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:15:41.136 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 22:15:41.136 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 22:15:41.138 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 22:15:41.138 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 22:15:41.138 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 22:15:41.138 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 22:15:41.138 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 22:15:49.830 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:15:49.830 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:15:49.830 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:15:49.830 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:15:49.830 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:15:49.830 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:16:50.831 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:16:50.831 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:16:50.831 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:16:50.831 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:16:50.831 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:16:50.831 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:17:18.831 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:17:18.964 | ERROR    | app.services.user_service:authenticate_user:253 - 用户认证异常: Column expression expected for argument 'remote_side'; got <built-in function id>.
2025-06-27 22:17:18.964 | ERROR    | app.api.v1.auth:login_user:328 - 用户登录异常: 认证失败: Column expression expected for argument 'remote_side'; got <built-in function id>.
2025-06-27 22:17:18.964 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.134s)
2025-06-27 22:17:33.707 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:17:33.707 | ERROR    | app.services.user_service:authenticate_user:253 - 用户认证异常: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ToolCategory(tool_categories)]'. Original exception was: Column expression expected for argument 'remote_side'; got <built-in function id>.
2025-06-27 22:17:33.707 | ERROR    | app.api.v1.auth:login_user:328 - 用户登录异常: 认证失败: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ToolCategory(tool_categories)]'. Original exception was: Column expression expected for argument 'remote_side'; got <built-in function id>.
2025-06-27 22:17:33.707 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.000s)
2025-06-27 22:17:56.645 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 22:17:56.647 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 22:17:56.647 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 22:17:56.647 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 22:17:56.647 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 22:17:56.647 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 22:17:56.647 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 22:18:09.433 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:18:09.437 | ERROR    | app.services.user_service:authenticate_user:253 - 用户认证异常: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ToolCategory(tool_categories)]'. Original exception was: Column expression expected for argument 'remote_side'; got <built-in function id>.
2025-06-27 22:18:09.437 | ERROR    | app.api.v1.auth:login_user:328 - 用户登录异常: 认证失败: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ToolCategory(tool_categories)]'. Original exception was: Column expression expected for argument 'remote_side'; got <built-in function id>.
2025-06-27 22:18:09.439 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.006s)
2025-06-27 22:18:53.196 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 22:18:53.196 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 22:18:53.196 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 22:18:53.196 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 22:18:53.196 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 22:18:53.196 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 22:18:53.196 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 22:19:02.156 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:19:02.157 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:19:02.157 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:19:02.157 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:19:02.157 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:19:02.157 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:19:06.204 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 22:19:06.205 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 22:19:06.205 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 22:19:06.206 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 22:19:06.206 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 22:19:06.207 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 22:19:06.207 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 22:19:14.714 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:19:14.730 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:19:14.730 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:19:14.730 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:19:14.730 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:19:14.730 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:19:37.901 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:19:37.901 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:19:37.903 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:19:37.903 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:19:37.903 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:19:37.903 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:19:45.566 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:19:45.730 | ERROR    | app.services.user_service:authenticate_user:253 - 用户认证异常: Could not determine join condition between parent/child tables on relationship Artifact.user - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-06-27 22:19:45.730 | ERROR    | app.api.v1.auth:login_user:328 - 用户登录异常: 认证失败: Could not determine join condition between parent/child tables on relationship Artifact.user - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-06-27 22:19:45.730 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.164s)
2025-06-27 22:20:58.807 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 22:20:58.807 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 22:20:58.807 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 22:20:58.807 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 22:20:58.807 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 22:20:58.807 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 22:20:58.807 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 22:21:07.596 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:21:07.596 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:21:07.597 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:21:07.597 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:21:07.598 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:21:07.598 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:21:21.008 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:21:21.008 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:21:21.010 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:21:21.010 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:21:21.010 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:21:21.011 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:21:27.751 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: OPTIONS /api/v1/auth/login
2025-06-27 22:21:27.751 | INFO     | app.middleware.logging:dispatch:105 - 请求完成: OPTIONS /api/v1/auth/login - 200 (0.000s)
2025-06-27 22:21:27.755 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:21:27.935 | ERROR    | app.services.user_service:authenticate_user:253 - 用户认证异常: Could not determine join condition between parent/child tables on relationship ConfigTemplate.creator - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-06-27 22:21:27.935 | ERROR    | app.api.v1.auth:login_user:328 - 用户登录异常: 认证失败: Could not determine join condition between parent/child tables on relationship ConfigTemplate.creator - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-06-27 22:21:27.935 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.180s)
2025-06-27 22:22:18.035 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 22:22:18.035 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 22:22:18.035 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 22:22:18.035 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 22:22:18.035 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 22:22:18.035 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 22:22:18.035 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 22:22:26.790 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:22:26.790 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:22:26.790 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:22:26.790 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:22:26.790 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:22:26.790 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:23:50.064 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:23:50.064 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:23:50.064 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:23:50.064 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:23:50.064 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:23:50.064 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:24:08.621 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:24:08.838 | WARNING  | app.services.user_service:authenticate_user:249 - 用户认证失败: 用户名或密码错误
2025-06-27 22:24:08.838 | WARNING  | app.api.v1.auth:login_user:319 - 用户登录失败: 用户名或密码错误
2025-06-27 22:24:08.838 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.217s)
2025-06-27 22:24:38.022 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:24:38.029 | WARNING  | app.services.user_service:authenticate_user:249 - 用户认证失败: 用户名或密码错误
2025-06-27 22:24:38.029 | WARNING  | app.api.v1.auth:login_user:319 - 用户登录失败: 用户名或密码错误
2025-06-27 22:24:38.029 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.007s)
2025-06-27 22:27:47.773 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 22:27:47.773 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 22:27:47.773 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 22:27:47.773 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 22:27:47.773 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 22:27:47.773 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 22:27:47.773 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 22:28:04.480 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:28:04.480 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:28:04.480 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:28:04.480 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:28:04.480 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:28:04.480 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:28:09.479 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:28:09.696 | WARNING  | app.services.user_service:authenticate_user:249 - 用户认证失败: 用户名或密码错误
2025-06-27 22:28:09.696 | WARNING  | app.api.v1.auth:login_user:319 - 用户登录失败: 用户名或密码错误
2025-06-27 22:28:09.696 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.217s)
2025-06-27 22:30:46.084 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:30:46.091 | WARNING  | app.services.user_service:authenticate_user:249 - 用户认证失败: 用户名或密码错误
2025-06-27 22:30:46.093 | WARNING  | app.api.v1.auth:login_user:319 - 用户登录失败: 用户名或密码错误
2025-06-27 22:30:46.093 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.010s)
2025-06-27 22:32:32.863 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: OPTIONS /api/v1/auth/login
2025-06-27 22:32:32.863 | INFO     | app.middleware.logging:dispatch:105 - 请求完成: OPTIONS /api/v1/auth/login - 200 (0.000s)
2025-06-27 22:32:32.866 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:32:32.873 | WARNING  | app.services.user_service:authenticate_user:249 - 用户认证失败: 用户名或密码错误
2025-06-27 22:32:32.873 | WARNING  | app.api.v1.auth:login_user:319 - 用户登录失败: 用户名或密码错误
2025-06-27 22:32:32.873 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.007s)
2025-06-27 22:32:34.072 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:32:34.080 | WARNING  | app.services.user_service:authenticate_user:249 - 用户认证失败: 用户名或密码错误
2025-06-27 22:32:34.080 | WARNING  | app.api.v1.auth:login_user:319 - 用户登录失败: 用户名或密码错误
2025-06-27 22:32:34.080 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.009s)
2025-06-27 22:32:34.800 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:32:34.808 | WARNING  | app.services.user_service:authenticate_user:249 - 用户认证失败: 用户名或密码错误
2025-06-27 22:32:34.808 | WARNING  | app.api.v1.auth:login_user:319 - 用户登录失败: 用户名或密码错误
2025-06-27 22:32:34.808 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.009s)
2025-06-27 22:32:35.844 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:32:35.852 | WARNING  | app.services.user_service:authenticate_user:249 - 用户认证失败: 用户名或密码错误
2025-06-27 22:32:35.853 | WARNING  | app.api.v1.auth:login_user:319 - 用户登录失败: 用户名或密码错误
2025-06-27 22:32:35.853 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.010s)
2025-06-27 22:32:36.479 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:32:36.487 | WARNING  | app.services.user_service:authenticate_user:249 - 用户认证失败: 用户名或密码错误
2025-06-27 22:32:36.487 | WARNING  | app.api.v1.auth:login_user:319 - 用户登录失败: 用户名或密码错误
2025-06-27 22:32:36.487 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.008s)
2025-06-27 22:32:37.155 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:32:37.162 | WARNING  | app.services.user_service:authenticate_user:249 - 用户认证失败: 用户名或密码错误
2025-06-27 22:32:37.162 | WARNING  | app.api.v1.auth:login_user:319 - 用户登录失败: 用户名或密码错误
2025-06-27 22:32:37.162 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.007s)
2025-06-27 22:33:34.312 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: OPTIONS /api/v1/auth/login
2025-06-27 22:33:34.313 | INFO     | app.middleware.logging:dispatch:105 - 请求完成: OPTIONS /api/v1/auth/login - 200 (0.001s)
2025-06-27 22:33:34.313 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:33:34.323 | WARNING  | app.services.user_service:authenticate_user:249 - 用户认证失败: 用户名或密码错误
2025-06-27 22:33:34.323 | WARNING  | app.api.v1.auth:login_user:319 - 用户登录失败: 用户名或密码错误
2025-06-27 22:33:34.323 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.010s)
2025-06-27 22:34:09.845 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:34:09.854 | WARNING  | app.services.user_service:authenticate_user:249 - 用户认证失败: 用户名或密码错误
2025-06-27 22:34:09.854 | WARNING  | app.api.v1.auth:login_user:319 - 用户登录失败: 用户名或密码错误
2025-06-27 22:34:09.854 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.009s)
2025-06-27 22:34:31.679 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:34:31.688 | WARNING  | app.services.user_service:authenticate_user:249 - 用户认证失败: 用户名或密码错误
2025-06-27 22:34:31.688 | WARNING  | app.api.v1.auth:login_user:319 - 用户登录失败: 用户名或密码错误
2025-06-27 22:34:31.688 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.009s)
2025-06-27 22:34:46.984 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:34:46.993 | WARNING  | app.services.user_service:authenticate_user:249 - 用户认证失败: 用户名或密码错误
2025-06-27 22:34:46.995 | WARNING  | app.api.v1.auth:login_user:319 - 用户登录失败: 用户名或密码错误
2025-06-27 22:34:46.995 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.012s)
2025-06-27 22:35:00.394 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:35:00.411 | WARNING  | app.services.user_service:authenticate_user:249 - 用户认证失败: 用户名或密码错误
2025-06-27 22:35:00.411 | WARNING  | app.api.v1.auth:login_user:319 - 用户登录失败: 用户名或密码错误
2025-06-27 22:35:00.411 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.017s)
2025-06-27 22:35:01.188 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:35:01.196 | WARNING  | app.services.user_service:authenticate_user:249 - 用户认证失败: 用户名或密码错误
2025-06-27 22:35:01.197 | WARNING  | app.api.v1.auth:login_user:319 - 用户登录失败: 用户名或密码错误
2025-06-27 22:35:01.198 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.010s)
2025-06-27 22:35:01.801 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:35:01.809 | WARNING  | app.services.user_service:authenticate_user:249 - 用户认证失败: 用户名或密码错误
2025-06-27 22:35:01.810 | WARNING  | app.api.v1.auth:login_user:319 - 用户登录失败: 用户名或密码错误
2025-06-27 22:35:01.810 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.009s)
2025-06-27 22:35:02.403 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:35:02.412 | WARNING  | app.services.user_service:authenticate_user:249 - 用户认证失败: 用户名或密码错误
2025-06-27 22:35:02.412 | WARNING  | app.api.v1.auth:login_user:319 - 用户登录失败: 用户名或密码错误
2025-06-27 22:35:02.412 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.009s)
2025-06-27 22:35:02.965 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:35:02.974 | WARNING  | app.services.user_service:authenticate_user:249 - 用户认证失败: 用户名或密码错误
2025-06-27 22:35:02.976 | WARNING  | app.api.v1.auth:login_user:319 - 用户登录失败: 用户名或密码错误
2025-06-27 22:35:02.977 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.011s)
2025-06-27 22:35:03.513 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:35:03.513 | WARNING  | app.services.user_service:authenticate_user:249 - 用户认证失败: 用户名或密码错误
2025-06-27 22:35:03.513 | WARNING  | app.api.v1.auth:login_user:319 - 用户登录失败: 用户名或密码错误
2025-06-27 22:35:03.513 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.000s)
2025-06-27 22:35:04.111 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:35:04.111 | WARNING  | app.services.user_service:authenticate_user:249 - 用户认证失败: 用户名或密码错误
2025-06-27 22:35:04.111 | WARNING  | app.api.v1.auth:login_user:319 - 用户登录失败: 用户名或密码错误
2025-06-27 22:35:04.111 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.000s)
2025-06-27 22:35:04.733 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:35:04.734 | WARNING  | app.core.rate_limiter:check_rate_limit:63 - 客户端 login:127.0.0.1 超过速率限制: 10/10
2025-06-27 22:35:04.734 | ERROR    | app.api.v1.auth:login_user:328 - 用户登录异常: 429: 请求过于频繁，请稍后再试。限制: 10次/分钟
2025-06-27 22:35:04.734 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.001s)
2025-06-27 22:37:48.988 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 22:37:48.990 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 22:37:48.990 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 22:37:48.991 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 22:37:48.992 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 22:37:48.992 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 22:37:48.992 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 22:37:57.983 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:37:57.983 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:37:57.992 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:37:57.993 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:37:57.994 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:37:57.994 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:38:15.593 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 22:38:15.593 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 22:38:15.593 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 22:38:15.593 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 22:38:15.593 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 22:38:15.593 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 22:38:15.593 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 22:38:23.702 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:38:23.702 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:38:23.702 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:38:23.702 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:38:23.710 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:38:23.711 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:38:47.429 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:38:47.429 | INFO     | app.api.v1.auth:login_user:259 - 登录请求参数 - 用户名: admin, 密码: Admin123!@#, 记住我: True
2025-06-27 22:38:47.637 | INFO     | app.services.user_service:authenticate_user:174 - 未找到用户 - 查询用户名: admin
2025-06-27 22:38:47.637 | WARNING  | app.services.user_service:authenticate_user:260 - 用户认证失败: 用户名或密码错误
2025-06-27 22:38:47.645 | WARNING  | app.api.v1.auth:login_user:322 - 用户登录失败: 用户名或密码错误
2025-06-27 22:38:47.645 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.216s)
2025-06-27 22:45:41.138 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 22:45:41.139 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 22:45:41.140 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 22:45:41.140 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 22:45:41.141 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 22:45:41.141 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 22:45:41.141 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 22:45:50.226 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:45:50.226 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:45:50.226 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:45:50.226 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:45:50.226 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:45:50.226 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:45:59.649 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 22:45:59.649 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 22:45:59.649 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 22:45:59.649 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 22:45:59.649 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 22:45:59.649 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 22:45:59.649 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 22:46:17.716 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:46:17.716 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:46:17.718 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:46:17.718 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:46:17.720 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:46:17.720 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:46:28.827 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 22:46:28.830 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 22:46:28.830 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 22:46:28.830 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 22:46:28.830 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 22:46:28.830 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 22:46:28.830 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 22:47:09.260 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:47:09.262 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:47:09.264 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:47:09.264 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:47:09.264 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:47:09.264 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:47:28.355 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: OPTIONS /api/v1/auth/login
2025-06-27 22:47:28.355 | INFO     | app.middleware.logging:dispatch:105 - 请求完成: OPTIONS /api/v1/auth/login - 200 (0.000s)
2025-06-27 22:47:28.355 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:47:28.355 | INFO     | app.api.v1.auth:login_user:259 - 登录请求参数 - 用户名: admin, 密码: Admin123!@#, 记住我: True
2025-06-27 22:47:28.377 | INFO     | app.services.user_service:authenticate_user:172 - 找到用户 - ID: 1, 用户名: admin, 邮箱: <EMAIL>, 密码哈希: $2b$12$LQv3c1yqBwEHx...
2025-06-27 22:47:28.377 | INFO     | app.services.user_service:authenticate_user:186 - 密码验证 - 输入密码: Admin123!@#, 存储的密码哈希: $2b$12$LQv3c1yqBwEHxPuNYjHNTO.eMQZOyCbquzHbnGJRBjOxMxEwVzgGS
2025-06-27 22:47:28.885 | INFO     | app.services.user_service:authenticate_user:188 - 密码验证结果: False
2025-06-27 22:47:28.885 | ERROR    | app.services.user_service:authenticate_user:264 - 用户认证异常: 'Settings' object has no attribute 'max_login_attempts'
2025-06-27 22:47:28.885 | ERROR    | app.api.v1.auth:login_user:331 - 用户登录异常: 认证失败: 'Settings' object has no attribute 'max_login_attempts'
2025-06-27 22:47:28.885 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.530s)
2025-06-27 22:49:31.417 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 22:49:31.417 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 22:49:31.417 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 22:49:31.417 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 22:49:31.419 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 22:49:31.419 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 22:49:31.419 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 22:49:40.966 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:49:40.966 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:49:40.982 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:49:40.982 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:49:40.982 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:49:40.982 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:49:51.478 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 22:49:51.479 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 22:49:51.479 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 22:49:51.479 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 22:49:51.479 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 22:49:51.479 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 22:49:51.479 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 22:49:59.873 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:49:59.873 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:49:59.873 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:49:59.873 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:49:59.873 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:49:59.873 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:50:29.758 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:50:29.759 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:50:29.759 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:50:29.760 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:50:29.761 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:50:29.761 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:51:23.111 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 22:51:23.111 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 22:51:23.111 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 22:51:23.111 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 22:51:23.111 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 22:51:23.111 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 22:51:23.111 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 22:51:35.494 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:51:35.494 | INFO     | app.api.v1.auth:login_user:259 - 登录请求参数 - 用户名: admin, 密码: Admin123!@#, 记住我: True
2025-06-27 22:51:35.506 | INFO     | app.services.user_service:authenticate_user:172 - 找到用户 - ID: 1, 用户名: admin, 邮箱: <EMAIL>, 密码哈希: $2b$12$LQv3c1yqBwEHx...
2025-06-27 22:51:35.506 | INFO     | app.services.user_service:authenticate_user:186 - 密码验证 - 输入密码: Admin123!@#, 存储的密码哈希: $2b$12$LQv3c1yqBwEHxPuNYjHNTO.eMQZOyCbquzHbnGJRBjOxMxEwVzgGS
2025-06-27 22:51:36.008 | INFO     | app.services.user_service:authenticate_user:188 - 密码验证结果: False
2025-06-27 22:51:36.040 | WARNING  | app.services.user_service:authenticate_user:260 - 用户认证失败: 用户名或密码错误
2025-06-27 22:51:36.040 | WARNING  | app.api.v1.auth:login_user:322 - 用户登录失败: 用户名或密码错误
2025-06-27 22:51:36.040 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.547s)
2025-06-27 22:53:59.245 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 22:53:59.247 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 22:53:59.247 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 22:53:59.248 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 22:53:59.248 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 22:53:59.248 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 22:53:59.249 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 22:54:08.448 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:54:08.448 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:54:08.448 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:54:08.448 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:54:08.448 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:54:08.448 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:54:41.636 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 22:54:41.637 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 22:54:41.637 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 22:54:41.637 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 22:54:41.639 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 22:54:41.639 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 22:54:41.639 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 22:54:50.652 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:54:50.652 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:54:50.652 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:54:50.652 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:54:50.652 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:54:50.652 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:54:59.687 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 22:54:59.687 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 22:54:59.692 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 22:54:59.692 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 22:54:59.692 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 22:54:59.692 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 22:54:59.692 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 22:55:19.509 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:55:19.511 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:55:19.511 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:55:19.511 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:55:19.511 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:55:19.511 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:55:37.079 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 22:55:37.079 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 22:55:37.079 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 22:55:37.079 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 22:55:37.079 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 22:55:37.079 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 22:55:37.079 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 22:55:44.655 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:55:44.655 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:55:44.658 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:55:44.660 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:55:44.660 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:55:44.660 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:56:20.192 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 22:56:20.192 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 22:56:20.192 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 22:56:20.192 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 22:56:20.192 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 22:56:20.192 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 22:56:20.192 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 22:56:28.860 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:56:28.875 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:56:28.875 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:56:28.875 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:56:28.875 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:56:28.875 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:57:20.362 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 22:57:20.362 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 22:57:20.362 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 22:57:20.362 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 22:57:20.362 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 22:57:20.369 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 22:57:20.369 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 22:57:48.355 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 22:57:48.355 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 22:57:48.355 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 22:57:48.355 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 22:57:48.355 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 22:57:48.363 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 22:57:54.194 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: OPTIONS /api/v1/auth/login
2025-06-27 22:57:54.194 | INFO     | app.middleware.logging:dispatch:105 - 请求完成: OPTIONS /api/v1/auth/login - 200 (0.000s)
2025-06-27 22:57:54.194 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 22:57:54.194 | INFO     | app.api.v1.auth:login_user:259 - 登录请求参数 - 用户名: admin, 密码: Admin123!@#, 记住我: True
2025-06-27 22:57:54.213 | INFO     | app.services.user_service:authenticate_user:172 - 找到用户 - ID: 1, 用户名: admin, 邮箱: <EMAIL>, 密码哈希: $2b$12$pn60.MiNLE7JP...
2025-06-27 22:57:54.213 | INFO     | app.services.user_service:authenticate_user:186 - 密码验证 - 输入密码: Admin123!@#, 存储的密码哈希: $2b$12$pn60.MiNLE7JPXZeqMWkGepmUxJI4WYOtNRew50tRKnH/AwqN75Ka
2025-06-27 22:57:54.668 | INFO     | app.services.user_service:authenticate_user:188 - 密码验证结果: True
2025-06-27 22:57:54.678 | ERROR    | app.auth.jwt_handler:_save_token_to_database:344 - 保存令牌到数据库失败: (sqlite3.IntegrityError) NOT NULL constraint failed: user_tokens.is_revoked
[SQL: 
                INSERT INTO user_tokens (
                    user_id, session_id, access_token, refresh_token,
                    access_expires_at, refresh_expires_at, device_info,
                    ip_address, user_agent, created_at
                ) VALUES (
                    ?, ?, ?, ?,
                    ?, ?, ?,
                    ?, ?, ?
                )
            ]
[parameters: (1, 'e12b7493-f69d-4412-8bb3-1e2f73d02b23', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiZTEyYjc0OTMtZjY5ZC00NDEyLThiYjMtMWUyZjczZDAyYjIzIiwidG9rZW5fdHlwZSI6ImFjY2Vz ... (5 characters truncated) ... mlhdCI6MTc1MTAzNjI3NCwiZXhwIjoxNzUxMDM4MDc0LCJqdGkiOiJjNTk0Yzg5Ni05ZGU0LTRmNjgtYmIzYy0wYmI4MmNlOTZjYTcifQ.kMll7ajA4YL-YZPcZzAJXDpPpgDfW7dC4Tk-fUWqdtI', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiZTEyYjc0OTMtZjY5ZC00NDEyLThiYjMtMWUyZjczZDAyYjIzIiwidG9rZW5fdHlwZSI6InJlZnJl ... (6 characters truncated) ... JpYXQiOjE3NTEwMzYyNzQsImV4cCI6MTc1MTY0MTA3NCwianRpIjoiNmY5ODI5ZWQtZDQ4MS00ZjM4LThiMjQtNzMyNDY4ZTk4ODI5In0.UlZG2yzs3s8GREi1jzvbST1ODU1-zUiNAKjraOt8ZeA', datetime.datetime(2025, 6, 27, 15, 27, 54, 677411), datetime.datetime(2025, 7, 4, 14, 57, 54, 677411), None, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0', datetime.datetime(2025, 6, 27, 22, 57, 54, 678494))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-27 22:57:54.678 | ERROR    | app.auth.jwt_handler:generate_tokens:116 - 生成JWT令牌失败: (sqlite3.IntegrityError) NOT NULL constraint failed: user_tokens.is_revoked
[SQL: 
                INSERT INTO user_tokens (
                    user_id, session_id, access_token, refresh_token,
                    access_expires_at, refresh_expires_at, device_info,
                    ip_address, user_agent, created_at
                ) VALUES (
                    ?, ?, ?, ?,
                    ?, ?, ?,
                    ?, ?, ?
                )
            ]
[parameters: (1, 'e12b7493-f69d-4412-8bb3-1e2f73d02b23', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiZTEyYjc0OTMtZjY5ZC00NDEyLThiYjMtMWUyZjczZDAyYjIzIiwidG9rZW5fdHlwZSI6ImFjY2Vz ... (5 characters truncated) ... mlhdCI6MTc1MTAzNjI3NCwiZXhwIjoxNzUxMDM4MDc0LCJqdGkiOiJjNTk0Yzg5Ni05ZGU0LTRmNjgtYmIzYy0wYmI4MmNlOTZjYTcifQ.kMll7ajA4YL-YZPcZzAJXDpPpgDfW7dC4Tk-fUWqdtI', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiZTEyYjc0OTMtZjY5ZC00NDEyLThiYjMtMWUyZjczZDAyYjIzIiwidG9rZW5fdHlwZSI6InJlZnJl ... (6 characters truncated) ... JpYXQiOjE3NTEwMzYyNzQsImV4cCI6MTc1MTY0MTA3NCwianRpIjoiNmY5ODI5ZWQtZDQ4MS00ZjM4LThiMjQtNzMyNDY4ZTk4ODI5In0.UlZG2yzs3s8GREi1jzvbST1ODU1-zUiNAKjraOt8ZeA', datetime.datetime(2025, 6, 27, 15, 27, 54, 677411), datetime.datetime(2025, 7, 4, 14, 57, 54, 677411), None, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0', datetime.datetime(2025, 6, 27, 22, 57, 54, 678494))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-27 22:57:54.678 | WARNING  | app.services.user_service:authenticate_user:260 - 用户认证失败: 生成令牌失败: (sqlite3.IntegrityError) NOT NULL constraint failed: user_tokens.is_revoked
[SQL: 
                INSERT INTO user_tokens (
                    user_id, session_id, access_token, refresh_token,
                    access_expires_at, refresh_expires_at, device_info,
                    ip_address, user_agent, created_at
                ) VALUES (
                    ?, ?, ?, ?,
                    ?, ?, ?,
                    ?, ?, ?
                )
            ]
[parameters: (1, 'e12b7493-f69d-4412-8bb3-1e2f73d02b23', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiZTEyYjc0OTMtZjY5ZC00NDEyLThiYjMtMWUyZjczZDAyYjIzIiwidG9rZW5fdHlwZSI6ImFjY2Vz ... (5 characters truncated) ... mlhdCI6MTc1MTAzNjI3NCwiZXhwIjoxNzUxMDM4MDc0LCJqdGkiOiJjNTk0Yzg5Ni05ZGU0LTRmNjgtYmIzYy0wYmI4MmNlOTZjYTcifQ.kMll7ajA4YL-YZPcZzAJXDpPpgDfW7dC4Tk-fUWqdtI', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiZTEyYjc0OTMtZjY5ZC00NDEyLThiYjMtMWUyZjczZDAyYjIzIiwidG9rZW5fdHlwZSI6InJlZnJl ... (6 characters truncated) ... JpYXQiOjE3NTEwMzYyNzQsImV4cCI6MTc1MTY0MTA3NCwianRpIjoiNmY5ODI5ZWQtZDQ4MS00ZjM4LThiMjQtNzMyNDY4ZTk4ODI5In0.UlZG2yzs3s8GREi1jzvbST1ODU1-zUiNAKjraOt8ZeA', datetime.datetime(2025, 6, 27, 15, 27, 54, 677411), datetime.datetime(2025, 7, 4, 14, 57, 54, 677411), None, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0', datetime.datetime(2025, 6, 27, 22, 57, 54, 678494))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-27 22:57:54.678 | WARNING  | app.api.v1.auth:login_user:322 - 用户登录失败: 生成令牌失败: (sqlite3.IntegrityError) NOT NULL constraint failed: user_tokens.is_revoked
[SQL: 
                INSERT INTO user_tokens (
                    user_id, session_id, access_token, refresh_token,
                    access_expires_at, refresh_expires_at, device_info,
                    ip_address, user_agent, created_at
                ) VALUES (
                    ?, ?, ?, ?,
                    ?, ?, ?,
                    ?, ?, ?
                )
            ]
[parameters: (1, 'e12b7493-f69d-4412-8bb3-1e2f73d02b23', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiZTEyYjc0OTMtZjY5ZC00NDEyLThiYjMtMWUyZjczZDAyYjIzIiwidG9rZW5fdHlwZSI6ImFjY2Vz ... (5 characters truncated) ... mlhdCI6MTc1MTAzNjI3NCwiZXhwIjoxNzUxMDM4MDc0LCJqdGkiOiJjNTk0Yzg5Ni05ZGU0LTRmNjgtYmIzYy0wYmI4MmNlOTZjYTcifQ.kMll7ajA4YL-YZPcZzAJXDpPpgDfW7dC4Tk-fUWqdtI', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiZTEyYjc0OTMtZjY5ZC00NDEyLThiYjMtMWUyZjczZDAyYjIzIiwidG9rZW5fdHlwZSI6InJlZnJl ... (6 characters truncated) ... JpYXQiOjE3NTEwMzYyNzQsImV4cCI6MTc1MTY0MTA3NCwianRpIjoiNmY5ODI5ZWQtZDQ4MS00ZjM4LThiMjQtNzMyNDY4ZTk4ODI5In0.UlZG2yzs3s8GREi1jzvbST1ODU1-zUiNAKjraOt8ZeA', datetime.datetime(2025, 6, 27, 15, 27, 54, 677411), datetime.datetime(2025, 7, 4, 14, 57, 54, 677411), None, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0', datetime.datetime(2025, 6, 27, 22, 57, 54, 678494))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-27 22:57:54.678 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.484s)
2025-06-27 23:00:01.017 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 23:00:01.021 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 23:00:01.021 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 23:00:01.021 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 23:00:01.021 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 23:00:01.021 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 23:00:01.021 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 23:00:10.012 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 23:00:10.012 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 23:00:10.022 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 23:00:10.022 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 23:00:10.022 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 23:00:10.022 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 23:00:14.297 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 23:00:14.297 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 23:00:14.297 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 23:00:14.297 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 23:00:14.297 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 23:00:14.297 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 23:00:14.297 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
2025-06-27 23:00:23.246 | INFO     | app.core.logging:setup_logging:333 - 日志系统初始化完成
2025-06-27 23:00:23.246 | INFO     | main:lifespan:48 - ✅ 日志系统已初始化
2025-06-27 23:00:23.246 | INFO     | app.core.storage:create_optimized_engine:562 - 数据库引擎创建完成: default
2025-06-27 23:00:23.254 | INFO     | main:lifespan:52 - ✅ 存储管理器已初始化
2025-06-27 23:00:23.254 | INFO     | main:lifespan:56 - ✅ 缓存管理器已初始化
2025-06-27 23:00:23.254 | INFO     | main:lifespan:58 - 🎉 A2A多智能体系统启动完成
2025-06-27 23:00:47.174 | INFO     | app.middleware.logging:dispatch:79 - 请求开始: POST /api/v1/auth/login
2025-06-27 23:00:47.181 | INFO     | app.api.v1.auth:login_user:259 - 登录请求参数 - 用户名: admin, 密码: Admin123!@#, 记住我: True
2025-06-27 23:00:47.187 | INFO     | app.services.user_service:authenticate_user:172 - 找到用户 - ID: 1, 用户名: admin, 邮箱: <EMAIL>, 密码哈希: $2b$12$pn60.MiNLE7JP...
2025-06-27 23:00:47.187 | INFO     | app.services.user_service:authenticate_user:186 - 密码验证 - 输入密码: Admin123!@#, 存储的密码哈希: $2b$12$pn60.MiNLE7JPXZeqMWkGepmUxJI4WYOtNRew50tRKnH/AwqN75Ka
2025-06-27 23:00:47.674 | INFO     | app.services.user_service:authenticate_user:188 - 密码验证结果: True
2025-06-27 23:00:47.691 | ERROR    | app.auth.jwt_handler:_save_token_to_database:345 - 保存令牌到数据库失败: (sqlite3.IntegrityError) NOT NULL constraint failed: user_tokens.updated_at
[SQL: 
                INSERT INTO user_tokens (
                    user_id, session_id, access_token, refresh_token,
                    access_expires_at, refresh_expires_at, device_info,
                    ip_address, user_agent, is_revoked, created_at
                ) VALUES (
                    ?, ?, ?, ?,
                    ?, ?, ?,
                    ?, ?, ?, ?
                )
            ]
[parameters: (1, 'd32b0915-8132-407c-a767-2827709cbba9', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiZDMyYjA5MTUtODEzMi00MDdjLWE3NjctMjgyNzcwOWNiYmE5IiwidG9rZW5fdHlwZSI6ImFjY2Vz ... (5 characters truncated) ... mlhdCI6MTc1MTAzNjQ0NywiZXhwIjoxNzUxMDM4MjQ3LCJqdGkiOiI2ODExZTU0YS1lZTk5LTRiYTMtODdmMC1lY2I2YTVhMTc1MTUifQ.JG5VWet-nX1GoY_R-W-mBa9MYGJf-6zjCd0fxehgbjk', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiZDMyYjA5MTUtODEzMi00MDdjLWE3NjctMjgyNzcwOWNiYmE5IiwidG9rZW5fdHlwZSI6InJlZnJl ... (6 characters truncated) ... JpYXQiOjE3NTEwMzY0NDcsImV4cCI6MTc1MTY0MTI0NywianRpIjoiNmVlYjFkYmItOTRhNy00NDM0LTlkNTUtZDRlNGEyYTAyOTQ4In0.YSLYPGYM8Uddh3CSwMzn7QjWru2tCbiq5pKkfL-m4KU', datetime.datetime(2025, 6, 27, 15, 30, 47, 674184), datetime.datetime(2025, 7, 4, 15, 0, 47, 674184), None, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0', False, datetime.datetime(2025, 6, 27, 23, 0, 47, 674184))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-27 23:00:47.691 | ERROR    | app.auth.jwt_handler:generate_tokens:116 - 生成JWT令牌失败: (sqlite3.IntegrityError) NOT NULL constraint failed: user_tokens.updated_at
[SQL: 
                INSERT INTO user_tokens (
                    user_id, session_id, access_token, refresh_token,
                    access_expires_at, refresh_expires_at, device_info,
                    ip_address, user_agent, is_revoked, created_at
                ) VALUES (
                    ?, ?, ?, ?,
                    ?, ?, ?,
                    ?, ?, ?, ?
                )
            ]
[parameters: (1, 'd32b0915-8132-407c-a767-2827709cbba9', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiZDMyYjA5MTUtODEzMi00MDdjLWE3NjctMjgyNzcwOWNiYmE5IiwidG9rZW5fdHlwZSI6ImFjY2Vz ... (5 characters truncated) ... mlhdCI6MTc1MTAzNjQ0NywiZXhwIjoxNzUxMDM4MjQ3LCJqdGkiOiI2ODExZTU0YS1lZTk5LTRiYTMtODdmMC1lY2I2YTVhMTc1MTUifQ.JG5VWet-nX1GoY_R-W-mBa9MYGJf-6zjCd0fxehgbjk', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiZDMyYjA5MTUtODEzMi00MDdjLWE3NjctMjgyNzcwOWNiYmE5IiwidG9rZW5fdHlwZSI6InJlZnJl ... (6 characters truncated) ... JpYXQiOjE3NTEwMzY0NDcsImV4cCI6MTc1MTY0MTI0NywianRpIjoiNmVlYjFkYmItOTRhNy00NDM0LTlkNTUtZDRlNGEyYTAyOTQ4In0.YSLYPGYM8Uddh3CSwMzn7QjWru2tCbiq5pKkfL-m4KU', datetime.datetime(2025, 6, 27, 15, 30, 47, 674184), datetime.datetime(2025, 7, 4, 15, 0, 47, 674184), None, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0', False, datetime.datetime(2025, 6, 27, 23, 0, 47, 674184))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-27 23:00:47.691 | WARNING  | app.services.user_service:authenticate_user:260 - 用户认证失败: 生成令牌失败: (sqlite3.IntegrityError) NOT NULL constraint failed: user_tokens.updated_at
[SQL: 
                INSERT INTO user_tokens (
                    user_id, session_id, access_token, refresh_token,
                    access_expires_at, refresh_expires_at, device_info,
                    ip_address, user_agent, is_revoked, created_at
                ) VALUES (
                    ?, ?, ?, ?,
                    ?, ?, ?,
                    ?, ?, ?, ?
                )
            ]
[parameters: (1, 'd32b0915-8132-407c-a767-2827709cbba9', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiZDMyYjA5MTUtODEzMi00MDdjLWE3NjctMjgyNzcwOWNiYmE5IiwidG9rZW5fdHlwZSI6ImFjY2Vz ... (5 characters truncated) ... mlhdCI6MTc1MTAzNjQ0NywiZXhwIjoxNzUxMDM4MjQ3LCJqdGkiOiI2ODExZTU0YS1lZTk5LTRiYTMtODdmMC1lY2I2YTVhMTc1MTUifQ.JG5VWet-nX1GoY_R-W-mBa9MYGJf-6zjCd0fxehgbjk', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiZDMyYjA5MTUtODEzMi00MDdjLWE3NjctMjgyNzcwOWNiYmE5IiwidG9rZW5fdHlwZSI6InJlZnJl ... (6 characters truncated) ... JpYXQiOjE3NTEwMzY0NDcsImV4cCI6MTc1MTY0MTI0NywianRpIjoiNmVlYjFkYmItOTRhNy00NDM0LTlkNTUtZDRlNGEyYTAyOTQ4In0.YSLYPGYM8Uddh3CSwMzn7QjWru2tCbiq5pKkfL-m4KU', datetime.datetime(2025, 6, 27, 15, 30, 47, 674184), datetime.datetime(2025, 7, 4, 15, 0, 47, 674184), None, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0', False, datetime.datetime(2025, 6, 27, 23, 0, 47, 674184))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-27 23:00:47.691 | WARNING  | app.api.v1.auth:login_user:322 - 用户登录失败: 生成令牌失败: (sqlite3.IntegrityError) NOT NULL constraint failed: user_tokens.updated_at
[SQL: 
                INSERT INTO user_tokens (
                    user_id, session_id, access_token, refresh_token,
                    access_expires_at, refresh_expires_at, device_info,
                    ip_address, user_agent, is_revoked, created_at
                ) VALUES (
                    ?, ?, ?, ?,
                    ?, ?, ?,
                    ?, ?, ?, ?
                )
            ]
[parameters: (1, 'd32b0915-8132-407c-a767-2827709cbba9', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiZDMyYjA5MTUtODEzMi00MDdjLWE3NjctMjgyNzcwOWNiYmE5IiwidG9rZW5fdHlwZSI6ImFjY2Vz ... (5 characters truncated) ... mlhdCI6MTc1MTAzNjQ0NywiZXhwIjoxNzUxMDM4MjQ3LCJqdGkiOiI2ODExZTU0YS1lZTk5LTRiYTMtODdmMC1lY2I2YTVhMTc1MTUifQ.JG5VWet-nX1GoY_R-W-mBa9MYGJf-6zjCd0fxehgbjk', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiZDMyYjA5MTUtODEzMi00MDdjLWE3NjctMjgyNzcwOWNiYmE5IiwidG9rZW5fdHlwZSI6InJlZnJl ... (6 characters truncated) ... JpYXQiOjE3NTEwMzY0NDcsImV4cCI6MTc1MTY0MTI0NywianRpIjoiNmVlYjFkYmItOTRhNy00NDM0LTlkNTUtZDRlNGEyYTAyOTQ4In0.YSLYPGYM8Uddh3CSwMzn7QjWru2tCbiq5pKkfL-m4KU', datetime.datetime(2025, 6, 27, 15, 30, 47, 674184), datetime.datetime(2025, 7, 4, 15, 0, 47, 674184), None, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0', False, datetime.datetime(2025, 6, 27, 23, 0, 47, 674184))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-27 23:00:47.691 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.517s)
2025-06-27 23:03:23.020 | INFO     | main:lifespan:63 - 🔄 A2A多智能体系统关闭中...
2025-06-27 23:03:23.020 | INFO     | main:lifespan:67 - ✅ 缓存管理器已清理
2025-06-27 23:03:23.020 | INFO     | app.core.storage:cleanup_storage:757 - 存储清理完成: {'temp_files_deleted': 0, 'archived_files_deleted': 0, 'orphaned_chunks_deleted': 0, 'space_freed_bytes': 0, 'errors': []}
2025-06-27 23:03:23.020 | INFO     | main:lifespan:71 - ✅ 存储管理器已清理
2025-06-27 23:03:23.020 | INFO     | app.core.database:close:100 - 数据库连接已关闭
2025-06-27 23:03:23.020 | INFO     | main:lifespan:74 - ✅ 数据库连接已关闭
2025-06-27 23:03:23.020 | INFO     | main:lifespan:75 - 👋 A2A多智能体系统已关闭
