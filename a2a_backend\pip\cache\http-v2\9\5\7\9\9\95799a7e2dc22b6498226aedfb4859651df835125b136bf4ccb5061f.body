Metadata-Version: 2.1
Name: grpcio-status
Version: 1.66.0
Summary: Status proto mapping for gRPC
Home-page: https://grpc.io
Author: The gRPC Authors
Author-email: <EMAIL>
License: Apache License 2.0
Classifier: Development Status :: 5 - Production/Stable
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: License :: OSI Approved :: Apache Software License
Requires-Python: >=3.8
License-File: LICENSE
Requires-Dist: protobuf <6.0dev,>=5.26.1
Requires-Dist: grpcio >=1.66.0
Requires-Dist: googleapis-common-protos >=1.5.5

gRPC Python Status Proto
===========================

Reference package for GRPC Python status proto mapping.

Supported Python Versions
-------------------------
Python >= 3.8

Dependencies
------------

Depends on the `grpcio` package, available from PyPI via `pip install grpcio`.
