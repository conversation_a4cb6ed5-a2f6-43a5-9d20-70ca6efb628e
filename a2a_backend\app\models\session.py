#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统会话模型

定义会话相关的数据模型
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Index, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from .base import BaseModel


class Session(BaseModel):
    """
    会话模型
    
    存储用户与智能体的对话会话信息
    """
    
    __tablename__ = "sessions"
    
    # 用户和智能体关联
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="用户ID"
    )
    
    agent_id = Column(
        Integer,
        ForeignKey("agents.id", ondelete="CASCADE"),
        nullable=False,
        comment="智能体ID"
    )
    
    # 会话信息
    session_id = Column(
        String(36),
        nullable=False,
        unique=True,
        comment="会话唯一标识"
    )
    
    title = Column(
        String(200),
        nullable=True,
        comment="会话标题"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="会话描述"
    )
    
    # 会话类型和模式
    session_type = Column(
        String(50),
        nullable=False,
        default="chat",
        comment="会话类型"
    )
    
    mode = Column(
        String(20),
        nullable=False,
        default="interactive",
        comment="会话模式"
    )
    
    # 状态信息
    status = Column(
        String(20),
        nullable=False,
        default="active",
        comment="会话状态"
    )
    
    is_pinned = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否置顶"
    )
    
    is_archived = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否归档"
    )
    
    # 配置信息
    config = Column(
        Text,
        nullable=True,
        comment="会话配置（JSON格式）"
    )
    
    # 上下文信息
    context = Column(
        Text,
        nullable=True,
        comment="会话上下文（JSON格式）"
    )
    
    # 统计信息
    message_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="消息数量"
    )
    
    total_tokens = Column(
        Integer,
        nullable=False,
        default=0,
        comment="总token数"
    )
    
    total_cost = Column(
        Float,
        nullable=False,
        default=0.0,
        comment="总费用"
    )
    
    # 时间信息
    started_at = Column(
        DateTime,
        nullable=False,
        default=func.now(),
        comment="开始时间"
    )
    
    last_activity_at = Column(
        DateTime,
        nullable=False,
        default=func.now(),
        comment="最后活动时间"
    )
    
    ended_at = Column(
        DateTime,
        nullable=True,
        comment="结束时间"
    )
    
    # 关联关系
    user = relationship("User", back_populates="sessions")
    agent = relationship("Agent", back_populates="sessions")
    messages = relationship("Message", back_populates="session", cascade="all, delete-orphan")
    artifacts = relationship("Artifact", back_populates="session")
    tool_executions = relationship("ToolExecution", back_populates="session")
    
    # 索引
    __table_args__ = (
        Index("idx_sessions_user_id", "user_id"),
        Index("idx_sessions_agent_id", "agent_id"),
        Index("idx_sessions_session_id", "session_id"),
        Index("idx_sessions_status", "status"),
        Index("idx_sessions_type", "session_type"),
        Index("idx_sessions_is_pinned", "is_pinned"),
        Index("idx_sessions_is_archived", "is_archived"),
        Index("idx_sessions_last_activity", "last_activity_at"),
        Index("idx_sessions_is_active", "is_active"),
    )
    
    def set_config(self, config: Dict[str, Any]) -> None:
        """
        设置会话配置
        
        Args:
            config: 配置字典
        """
        import json
        self.config = json.dumps(config, ensure_ascii=False)
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取会话配置
        
        Returns:
            Dict[str, Any]: 配置字典
        """
        if not self.config:
            return {}
        
        try:
            import json
            return json.loads(self.config)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_context(self, context: Dict[str, Any]) -> None:
        """
        设置会话上下文
        
        Args:
            context: 上下文字典
        """
        import json
        self.context = json.dumps(context, ensure_ascii=False)
    
    def get_context(self) -> Dict[str, Any]:
        """
        获取会话上下文
        
        Returns:
            Dict[str, Any]: 上下文字典
        """
        if not self.context:
            return {}
        
        try:
            import json
            return json.loads(self.context)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def update_activity(self) -> None:
        """
        更新最后活动时间
        """
        self.last_activity_at = datetime.now()
    
    def add_message_stats(self, tokens: int, cost: float = 0.0) -> None:
        """
        添加消息统计
        
        Args:
            tokens: token数量
            cost: 费用
        """
        self.message_count += 1
        self.total_tokens += tokens
        self.total_cost += cost
        self.update_activity()
    
    def pin(self) -> None:
        """
        置顶会话
        """
        self.is_pinned = True
    
    def unpin(self) -> None:
        """
        取消置顶
        """
        self.is_pinned = False
    
    def archive(self) -> None:
        """
        归档会话
        """
        self.is_archived = True
        self.status = "archived"
    
    def unarchive(self) -> None:
        """
        取消归档
        """
        self.is_archived = False
        self.status = "active"
    
    def end_session(self) -> None:
        """
        结束会话
        """
        self.status = "ended"
        self.ended_at = datetime.now()
    
    def generate_title(self, first_message: str = None) -> str:
        """
        生成会话标题
        
        Args:
            first_message: 第一条消息内容
            
        Returns:
            str: 生成的标题
        """
        if first_message:
            # 截取前50个字符作为标题
            title = first_message[:50]
            if len(first_message) > 50:
                title += "..."
            return title
        else:
            return f"会话 {self.session_id[:8]}"
    
    @property
    def duration(self) -> Optional[float]:
        """
        获取会话持续时间（秒）
        
        Returns:
            Optional[float]: 持续时间
        """
        end_time = self.ended_at or datetime.now()
        return (end_time - self.started_at).total_seconds()
    
    @property
    def average_cost_per_message(self) -> float:
        """
        获取每条消息平均费用
        
        Returns:
            float: 平均费用
        """
        if self.message_count == 0:
            return 0.0
        return self.total_cost / self.message_count
    
    def __repr__(self) -> str:
        return f"<Session(id={self.id}, session_id='{self.session_id}', user_id={self.user_id})>"


class SessionShare(BaseModel):
    """
    会话分享模型
    
    存储会话分享信息
    """
    
    __tablename__ = "session_shares"
    
    # 会话关联
    session_id = Column(
        Integer,
        ForeignKey("sessions.id", ondelete="CASCADE"),
        nullable=False,
        comment="会话ID"
    )
    
    # 分享信息
    share_token = Column(
        String(64),
        nullable=False,
        unique=True,
        comment="分享令牌"
    )
    
    share_url = Column(
        String(500),
        nullable=True,
        comment="分享链接"
    )
    
    # 权限设置
    is_public = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否公开"
    )
    
    allow_copy = Column(
        Boolean,
        nullable=False,
        default=True,
        comment="是否允许复制"
    )
    
    allow_download = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否允许下载"
    )
    
    # 访问控制
    password = Column(
        String(255),
        nullable=True,
        comment="访问密码哈希"
    )
    
    max_views = Column(
        Integer,
        nullable=True,
        comment="最大查看次数"
    )
    
    view_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="查看次数"
    )
    
    # 时间控制
    expires_at = Column(
        DateTime,
        nullable=True,
        comment="过期时间"
    )
    
    # 创建者信息
    created_by = Column(
        Integer,
        nullable=False,
        comment="创建者ID"
    )
    
    # 关联关系
    session = relationship("Session")
    
    # 索引
    __table_args__ = (
        Index("idx_session_shares_session_id", "session_id"),
        Index("idx_session_shares_share_token", "share_token"),
        Index("idx_session_shares_is_public", "is_public"),
        Index("idx_session_shares_expires_at", "expires_at"),
        Index("idx_session_shares_created_by", "created_by"),
    )
    
    def increment_view(self) -> bool:
        """
        增加查看次数
        
        Returns:
            bool: 是否成功（未超过最大次数）
        """
        if self.max_views and self.view_count >= self.max_views:
            return False
        
        self.view_count += 1
        return True
    
    @property
    def is_expired(self) -> bool:
        """
        检查是否过期
        
        Returns:
            bool: 是否过期
        """
        if not self.expires_at:
            return False
        return datetime.now() > self.expires_at
    
    @property
    def is_view_limit_reached(self) -> bool:
        """
        检查是否达到查看限制
        
        Returns:
            bool: 是否达到限制
        """
        if not self.max_views:
            return False
        return self.view_count >= self.max_views
    
    @property
    def is_accessible(self) -> bool:
        """
        检查是否可访问
        
        Returns:
            bool: 是否可访问
        """
        return not self.is_expired and not self.is_view_limit_reached
    
    def __repr__(self) -> str:
        return f"<SessionShare(id={self.id}, session_id={self.session_id}, token='{self.share_token[:8]}...')>"


class SessionBookmark(BaseModel):
    """
    会话书签模型
    
    存储用户对会话的书签
    """
    
    __tablename__ = "session_bookmarks"
    
    # 用户和会话关联
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="用户ID"
    )
    
    session_id = Column(
        Integer,
        ForeignKey("sessions.id", ondelete="CASCADE"),
        nullable=False,
        comment="会话ID"
    )
    
    # 书签信息
    title = Column(
        String(200),
        nullable=True,
        comment="自定义标题"
    )
    
    notes = Column(
        Text,
        nullable=True,
        comment="备注"
    )
    
    # 分类信息
    category = Column(
        String(50),
        nullable=True,
        comment="分类"
    )
    
    tags = Column(
        Text,
        nullable=True,
        comment="标签（JSON格式）"
    )
    
    # 关联关系
    user = relationship("User")
    session = relationship("Session")
    
    # 索引
    __table_args__ = (
        Index("idx_session_bookmarks_user_id", "user_id"),
        Index("idx_session_bookmarks_session_id", "session_id"),
        Index("idx_session_bookmarks_category", "category"),
        # 复合唯一索引
        Index("idx_session_bookmarks_unique", "user_id", "session_id", unique=True),
    )
    
    def set_tags(self, tags: List[str]) -> None:
        """
        设置标签
        
        Args:
            tags: 标签列表
        """
        import json
        self.tags = json.dumps(tags, ensure_ascii=False)
    
    def get_tags(self) -> List[str]:
        """
        获取标签
        
        Returns:
            List[str]: 标签列表
        """
        if not self.tags:
            return []
        
        try:
            import json
            return json.loads(self.tags)
        except (json.JSONDecodeError, TypeError):
            return []
    
    def __repr__(self) -> str:
        return f"<SessionBookmark(id={self.id}, user_id={self.user_id}, session_id={self.session_id})>"


class SessionContext(BaseModel):
    """
    会话上下文模型
    
    存储会话的上下文信息
    """
    
    __tablename__ = "session_contexts"
    
    # 会话关联
    session_id = Column(
        String(36),
        nullable=False,
        comment="会话ID"
    )
    
    # 上下文信息
    context_data = Column(
        Text,
        nullable=True,
        comment="上下文数据（JSON格式）"
    )
    
    version = Column(
        Integer,
        nullable=False,
        default=1,
        comment="版本号"
    )
    
    is_current = Column(
        Boolean,
        nullable=False,
        default=True,
        comment="是否为当前版本"
    )
    
    # 索引
    __table_args__ = (
        Index("idx_session_contexts_session_id", "session_id"),
        Index("idx_session_contexts_version", "version"),
        Index("idx_session_contexts_current", "is_current"),
    )
    
    def __repr__(self) -> str:
        return f"<SessionContext(id={self.id}, session_id={self.session_id}, version={self.version})>"


class SessionParticipant(BaseModel):
    """
    会话参与者模型
    
    存储会话的参与者信息
    """
    
    __tablename__ = "session_participants"
    
    # 会话和用户关联
    session_id = Column(
        String(36),
        nullable=False,
        comment="会话ID"
    )
    
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="用户ID"
    )
    
    # 参与信息
    role = Column(
        String(20),
        nullable=False,
        default="participant",
        comment="参与角色"
    )
    
    joined_at = Column(
        DateTime,
        nullable=False,
        default=func.now(),
        comment="加入时间"
    )
    
    left_at = Column(
        DateTime,
        nullable=True,
        comment="离开时间"
    )
    
    # 关联关系
    user = relationship("User")
    
    # 索引
    __table_args__ = (
        Index("idx_session_participants_session_id", "session_id"),
        Index("idx_session_participants_user_id", "user_id"),
        Index("idx_session_participants_role", "role"),
    )
    
    def __repr__(self) -> str:
        return f"<SessionParticipant(id={self.id}, session_id={self.session_id}, user_id={self.user_id})>"