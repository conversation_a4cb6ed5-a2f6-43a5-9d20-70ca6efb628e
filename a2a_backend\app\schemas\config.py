#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统配置相关Pydantic模式

定义配置相关的请求和响应模式
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pydantic import Field, validator
from enum import Enum

from .base import BaseSchema


class ConfigType(str, Enum):
    """配置类型枚举"""
    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    JSON = "json"
    LIST = "list"
    DICT = "dict"


class ConfigCategory(str, Enum):
    """配置分类枚举"""
    SYSTEM = "system"
    DATABASE = "database"
    CACHE = "cache"
    SECURITY = "security"
    API = "api"
    LOGGING = "logging"
    EMAIL = "email"
    STORAGE = "storage"
    AI = "ai"
    WORKFLOW = "workflow"
    NOTIFICATION = "notification"
    INTEGRATION = "integration"
    CUSTOM = "custom"


class ConfigStatus(str, Enum):
    """配置状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    DEPRECATED = "deprecated"
    TESTING = "testing"


class TemplateType(str, Enum):
    """模板类型枚举"""
    SYSTEM = "system"
    USER = "user"
    AGENT = "agent"
    WORKFLOW = "workflow"
    CUSTOM = "custom"


class SystemConfigCreate(BaseSchema):
    """
    系统配置创建请求模式
    """
    
    key: str = Field(
        min_length=1,
        max_length=100,
        description="配置键"
    )
    
    value: str = Field(
        description="配置值（字符串形式）"
    )
    
    type: ConfigType = Field(
        description="配置类型"
    )
    
    category: ConfigCategory = Field(
        description="配置分类"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=500,
        description="配置描述"
    )
    
    constraints: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="配置约束"
    )
    
    default_value: Optional[str] = Field(
        default=None,
        description="默认值"
    )
    
    validation_rules: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="验证规则"
    )
    
    is_sensitive: bool = Field(
        default=False,
        description="是否敏感信息"
    )
    
    is_readonly: bool = Field(
        default=False,
        description="是否只读"
    )
    
    requires_restart: bool = Field(
        default=False,
        description="是否需要重启"
    )
    
    updated_by: str = Field(
        description="更新者ID"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )


class LLMConfigCreate(BaseSchema):
    """
    LLM配置创建请求模式
    """
    
    provider: str = Field(
        min_length=1,
        max_length=50,
        description="LLM提供商"
    )
    
    model: str = Field(
        min_length=1,
        max_length=100,
        description="模型名称"
    )
    
    api_key: Optional[str] = Field(
        default=None,
        description="API密钥"
    )
    
    api_url: Optional[str] = Field(
        default=None,
        description="API地址"
    )
    
    max_tokens: Optional[int] = Field(
        default=4096,
        ge=1,
        le=100000,
        description="最大令牌数"
    )
    
    temperature: Optional[float] = Field(
        default=0.7,
        ge=0.0,
        le=2.0,
        description="温度参数"
    )
    
    is_default: bool = Field(
        default=False,
        description="是否默认配置"
    )


class LLMConfigUpdate(BaseSchema):
    """
    LLM配置更新请求模式
    """
    
    provider: Optional[str] = Field(
        default=None,
        min_length=1,
        max_length=50,
        description="LLM提供商"
    )
    
    model: Optional[str] = Field(
        default=None,
        min_length=1,
        max_length=100,
        description="模型名称"
    )
    
    api_key: Optional[str] = Field(
        default=None,
        description="API密钥"
    )
    
    api_url: Optional[str] = Field(
        default=None,
        description="API地址"
    )
    
    max_tokens: Optional[int] = Field(
        default=None,
        ge=1,
        le=100000,
        description="最大令牌数"
    )
    
    temperature: Optional[float] = Field(
        default=None,
        ge=0.0,
        le=2.0,
        description="温度参数"
    )
    
    is_default: Optional[bool] = Field(
        default=None,
        description="是否默认配置"
    )


class LLMConfigResponse(BaseSchema):
    """
    LLM配置响应模式
    """
    
    id: str = Field(
        description="配置ID"
    )
    
    provider: str = Field(
        description="LLM提供商"
    )
    
    model: str = Field(
        description="模型名称"
    )
    
    api_url: Optional[str] = Field(
        description="API地址"
    )
    
    max_tokens: int = Field(
        description="最大令牌数"
    )
    
    temperature: float = Field(
        description="温度参数"
    )
    
    is_default: bool = Field(
        description="是否默认配置"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )


class ToolConfigCreate(BaseSchema):
    """
    工具配置创建请求模式
    """
    
    name: str = Field(
        min_length=1,
        max_length=100,
        description="工具名称"
    )
    
    type: str = Field(
        min_length=1,
        max_length=50,
        description="工具类型"
    )
    
    config: Dict[str, Any] = Field(
        default_factory=dict,
        description="工具配置"
    )
    
    is_enabled: bool = Field(
        default=True,
        description="是否启用"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=500,
        description="工具描述"
    )


class ToolConfigUpdate(BaseSchema):
    """
    工具配置更新请求模式
    """
    
    name: Optional[str] = Field(
        default=None,
        min_length=1,
        max_length=100,
        description="工具名称"
    )
    
    type: Optional[str] = Field(
        default=None,
        min_length=1,
        max_length=50,
        description="工具类型"
    )
    
    config: Optional[Dict[str, Any]] = Field(
        default=None,
        description="工具配置"
    )
    
    is_enabled: Optional[bool] = Field(
        default=None,
        description="是否启用"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=500,
        description="工具描述"
    )


class ToolConfigResponse(BaseSchema):
    """
    工具配置响应模式
    """
    
    id: str = Field(
        description="配置ID"
    )
    
    name: str = Field(
        description="工具名称"
    )
    
    type: str = Field(
        description="工具类型"
    )
    
    config: Dict[str, Any] = Field(
        description="工具配置"
    )
    
    is_enabled: bool = Field(
        description="是否启用"
    )
    
    description: Optional[str] = Field(
        description="工具描述"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )


class ConfigBackupResponse(BaseSchema):
    """
    配置备份响应模式
    """
    
    backup_id: str = Field(
        description="备份ID"
    )
    
    filename: str = Field(
        description="备份文件名"
    )
    
    size: int = Field(
        description="文件大小（字节）"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )


class ConfigImportRequest(BaseSchema):
    """
    配置导入请求模式
    """
    
    backup_data: Dict[str, Any] = Field(
        description="备份数据"
    )
    
    overwrite: bool = Field(
        default=False,
        description="是否覆盖现有配置"
    )
    
    validate_only: bool = Field(
        default=False,
        description="仅验证不导入"
    )


class UserConfigCreate(BaseSchema):
    """
    用户配置创建请求模式
    """
    
    key: str = Field(
        min_length=1,
        max_length=100,
        description="配置键"
    )
    
    value: str = Field(
        description="配置值（字符串形式）"
    )
    
    type: ConfigType = Field(
        description="配置类型"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=500,
        description="配置描述"
    )
    
    @validator('key')
    def validate_key(cls, v):
        """验证配置键格式"""
        if not v.replace('_', '').replace('.', '').isalnum():
            raise ValueError('配置键只能包含字母、数字、下划线和点')
        return v.lower()


class UserConfigUpdate(BaseSchema):
    """
    用户配置更新请求模式
    """
    
    value: Optional[str] = Field(
        default=None,
        description="配置值（字符串形式）"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=500,
        description="配置描述"
    )


class UserConfigResponse(BaseSchema):
    """
    用户配置响应模式
    """
    
    id: str = Field(
        description="配置ID"
    )
    
    user_id: str = Field(
        description="用户ID"
    )
    
    key: str = Field(
        description="配置键"
    )
    
    value: str = Field(
        description="配置值（字符串形式）"
    )
    
    type: ConfigType = Field(
        description="配置类型"
    )
    
    description: Optional[str] = Field(
        description="配置描述"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )


class AgentConfigCreate(BaseSchema):
    """
    智能体配置创建请求模式
    """
    
    key: str = Field(
        min_length=1,
        max_length=100,
        description="配置键"
    )
    
    value: str = Field(
        description="配置值（字符串形式）"
    )
    
    type: ConfigType = Field(
        description="配置类型"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=500,
        description="配置描述"
    )
    
    @validator('key')
    def validate_key(cls, v):
        """验证配置键格式"""
        if not v.replace('_', '').replace('.', '').isalnum():
            raise ValueError('配置键只能包含字母、数字、下划线和点')
        return v.lower()


class AgentConfigUpdate(BaseSchema):
    """
    智能体配置更新请求模式
    """
    
    value: Optional[str] = Field(
        default=None,
        description="配置值（字符串形式）"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=500,
        description="配置描述"
    )


class AgentConfigResponse(BaseSchema):
    """
    智能体配置响应模式
    """
    
    id: str = Field(
        description="配置ID"
    )
    
    agent_id: str = Field(
        description="智能体ID"
    )
    
    key: str = Field(
        description="配置键"
    )
    
    value: str = Field(
        description="配置值（字符串形式）"
    )
    
    type: ConfigType = Field(
        description="配置类型"
    )
    
    description: Optional[str] = Field(
        description="配置描述"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )


class ConfigTemplateCreate(BaseSchema):
    """
    配置模板创建请求模式
    """
    
    name: str = Field(
        min_length=1,
        max_length=100,
        description="模板名称"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="模板描述"
    )
    
    type: TemplateType = Field(
        description="模板类型"
    )
    
    template_data: Dict[str, Any] = Field(
        description="模板数据"
    )
    
    tags: List[str] = Field(
        default_factory=list,
        description="标签列表"
    )
    
    is_public: bool = Field(
        default=False,
        description="是否公开"
    )


class ConfigTemplateUpdate(BaseSchema):
    """
    配置模板更新请求模式
    """
    
    name: Optional[str] = Field(
        default=None,
        min_length=1,
        max_length=100,
        description="模板名称"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="模板描述"
    )
    
    template_data: Optional[Dict[str, Any]] = Field(
        default=None,
        description="模板数据"
    )
    
    status: Optional[ConfigStatus] = Field(
        default=None,
        description="模板状态"
    )
    
    tags: Optional[List[str]] = Field(
        default=None,
        description="标签列表"
    )
    
    is_public: Optional[bool] = Field(
        default=None,
        description="是否公开"
    )


class ConfigTemplateResponse(BaseSchema):
    """
    配置模板响应模式
    """
    
    id: str = Field(
        description="模板ID"
    )
    
    name: str = Field(
        description="模板名称"
    )
    
    description: Optional[str] = Field(
        description="模板描述"
    )
    
    type: TemplateType = Field(
        description="模板类型"
    )
    
    template_data: Dict[str, Any] = Field(
        description="模板数据"
    )
    
    status: ConfigStatus = Field(
        description="模板状态"
    )
    
    tags: List[str] = Field(
        description="标签列表"
    )
    
    is_public: bool = Field(
        description="是否公开"
    )
    
    creator_id: str = Field(
        description="创建者ID"
    )
    
    usage_count: int = Field(
        description="使用次数"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )


class ConfigSearchRequest(BaseSchema):
    """
    配置搜索请求模式
    """
    
    query: Optional[str] = Field(
        default=None,
        max_length=200,
        description="搜索查询"
    )
    
    category: Optional[ConfigCategory] = Field(
        default=None,
        description="配置分类过滤"
    )
    
    type: Optional[ConfigType] = Field(
        default=None,
        description="配置类型过滤"
    )
    
    is_sensitive: Optional[bool] = Field(
        default=None,
        description="是否敏感信息过滤"
    )
    
    is_readonly: Optional[bool] = Field(
        default=None,
        description="是否只读过滤"
    )
    
    requires_restart: Optional[bool] = Field(
        default=None,
        description="是否需要重启过滤"
    )
    
    sort_by: str = Field(
        default="key",
        description="排序字段"
    )
    
    sort_order: str = Field(
        default="asc",
        description="排序方向（asc/desc）"
    )


class ConfigBatchRequest(BaseSchema):
    """
    配置批量操作请求模式
    """
    
    config_keys: List[str] = Field(
        min_items=1,
        max_items=100,
        description="配置键列表"
    )
    
    action: str = Field(
        description="批量操作类型（get/set/delete/reset）"
    )
    
    values: Optional[Dict[str, str]] = Field(
        default=None,
        description="配置值映射（用于批量设置）"
    )


class ConfigValidationRequest(BaseSchema):
    """
    配置验证请求模式
    """
    
    key: str = Field(
        description="配置键"
    )
    
    value: str = Field(
        description="配置值"
    )
    
    type: ConfigType = Field(
        description="配置类型"
    )


class ConfigBackupRequest(BaseSchema):
    """
    配置备份请求模式
    """
    
    categories: List[ConfigCategory] = Field(
        default_factory=list,
        description="要备份的配置分类（空表示全部）"
    )
    
    include_sensitive: bool = Field(
        default=False,
        description="是否包含敏感配置"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=500,
        description="备份描述"
    )


class ConfigRestoreRequest(BaseSchema):
    """
    配置恢复请求模式
    """
    
    backup_id: str = Field(
        description="备份ID"
    )
    
    categories: List[ConfigCategory] = Field(
        default_factory=list,
        description="要恢复的配置分类（空表示全部）"
    )
    
    overwrite_existing: bool = Field(
        default=False,
        description="是否覆盖现有配置"
    )
    
    dry_run: bool = Field(
        default=False,
        description="是否仅预览不实际执行"
    )


class SystemConfigUpdate(BaseSchema):
    """
    系统配置更新请求模式
    """
    
    value: Optional[str] = Field(
        default=None,
        description="配置值（字符串形式）"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=500,
        description="配置描述"
    )
    
    constraints: Optional[Dict[str, Any]] = Field(
        default=None,
        description="配置约束"
    )
    
    validation_rules: Optional[Dict[str, Any]] = Field(
        default=None,
        description="验证规则"
    )
    
    is_sensitive: Optional[bool] = Field(
        default=None,
        description="是否敏感信息"
    )
    
    is_readonly: Optional[bool] = Field(
        default=None,
        description="是否只读"
    )
    
    requires_restart: Optional[bool] = Field(
        default=None,
        description="是否需要重启"
    )


class SystemConfigResponse(BaseSchema):
    """
    系统配置响应模式
    """
    
    id: str = Field(
        description="配置ID"
    )
    
    key: str = Field(
        description="配置键"
    )
    
    value: str = Field(
        description="配置值（字符串形式）"
    )
    
    type: ConfigType = Field(
        description="配置类型"
    )
    
    category: ConfigCategory = Field(
        description="配置分类"
    )
    
    description: Optional[str] = Field(
        description="配置描述"
    )
    
    constraints: Dict[str, Any] = Field(
        description="配置约束"
    )
    
    default_value: Optional[str] = Field(
        description="默认值"
    )
    
    validation_rules: Dict[str, Any] = Field(
        description="验证规则"
    )
    
    is_sensitive: bool = Field(
        description="是否敏感信息"
    )
    
    is_readonly: bool = Field(
        description="是否只读"
    )
    
    requires_restart: bool = Field(
        description="是否需要重启"
    )
    
    updated_by: str = Field(
        description="更新者ID"
    )
    
    created_at: datetime = Field(
        description="创建时间"
    )
    
    updated_at: datetime = Field(
        description="更新时间"
    )