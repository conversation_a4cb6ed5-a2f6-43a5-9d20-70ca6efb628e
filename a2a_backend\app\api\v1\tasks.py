# -*- coding: utf-8 -*-
"""
A2A多智能体系统任务API接口

提供任务创建、执行、监控和管理功能
"""

from datetime import datetime
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, status, Request, Query, BackgroundTasks
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field, validator
from loguru import logger
import json
import asyncio

from app.services.task_service import TaskService, TaskStatus, TaskPriority
from app.services.auth_service import AuthService
from app.core.config import get_settings
from app.core.rate_limiter import RateLimiter
from app.core.security import get_client_ip


# 创建路由器
router = APIRouter(prefix="/tasks", tags=["任务管理"])

# 安全依赖
security = HTTPBearer()

# 服务实例
task_service = TaskService()
auth_service = AuthService()
settings = get_settings()
rate_limiter = RateLimiter()


# 请求模型
class TaskCreateRequest(BaseModel):
    """任务创建请求模型"""
    title: str = Field(..., min_length=1, max_length=200, description="任务标题")
    description: Optional[str] = Field(None, max_length=2000, description="任务描述")
    task_type: str = Field(..., description="任务类型")
    agent_id: Optional[int] = Field(None, description="执行任务的智能体ID")
    session_id: Optional[int] = Field(None, description="关联的会话ID")
    priority: str = Field("medium", description="任务优先级")
    config: Optional[Dict[str, Any]] = Field(None, description="任务配置")
    input_data: Optional[Dict[str, Any]] = Field(None, description="输入数据")
    dependencies: Optional[List[int]] = Field(None, description="依赖的任务ID列表")
    scheduled_at: Optional[datetime] = Field(None, description="计划执行时间")
    timeout: Optional[int] = Field(None, description="超时时间（秒）")
    max_retries: int = Field(3, description="最大重试次数")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    
    @validator('task_type')
    def validate_task_type(cls, v):
        """验证任务类型"""
        allowed_types = ['agent_execution', 'data_processing', 'workflow', 'analysis', 'generation', 'custom']
        if v not in allowed_types:
            raise ValueError(f'任务类型必须是: {", ".join(allowed_types)}')
        return v
    
    @validator('priority')
    def validate_priority(cls, v):
        """验证优先级"""
        allowed_priorities = ['low', 'medium', 'high', 'urgent']
        if v not in allowed_priorities:
            raise ValueError(f'优先级必须是: {", ".join(allowed_priorities)}')
        return v


class TaskUpdateRequest(BaseModel):
    """任务更新请求模型"""
    title: Optional[str] = Field(None, min_length=1, max_length=200, description="任务标题")
    description: Optional[str] = Field(None, max_length=2000, description="任务描述")
    priority: Optional[str] = Field(None, description="任务优先级")
    config: Optional[Dict[str, Any]] = Field(None, description="任务配置")
    scheduled_at: Optional[datetime] = Field(None, description="计划执行时间")
    timeout: Optional[int] = Field(None, description="超时时间（秒）")
    max_retries: Optional[int] = Field(None, description="最大重试次数")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    
    @validator('priority')
    def validate_priority(cls, v):
        """验证优先级"""
        if v is not None:
            allowed_priorities = ['low', 'medium', 'high', 'urgent']
            if v not in allowed_priorities:
                raise ValueError(f'优先级必须是: {", ".join(allowed_priorities)}')
        return v


class TaskStepRequest(BaseModel):
    """任务步骤请求模型"""
    step_name: str = Field(..., min_length=1, max_length=100, description="步骤名称")
    step_type: str = Field(..., description="步骤类型")
    config: Optional[Dict[str, Any]] = Field(None, description="步骤配置")
    input_data: Optional[Dict[str, Any]] = Field(None, description="输入数据")
    dependencies: Optional[List[str]] = Field(None, description="依赖的步骤名称列表")
    timeout: Optional[int] = Field(None, description="超时时间（秒）")
    
    @validator('step_type')
    def validate_step_type(cls, v):
        """验证步骤类型"""
        allowed_types = ['agent_call', 'data_transform', 'condition', 'loop', 'parallel', 'custom']
        if v not in allowed_types:
            raise ValueError(f'步骤类型必须是: {", ".join(allowed_types)}')
        return v


# 响应模型
class TaskResponse(BaseModel):
    """任务响应模型"""
    task_id: int = Field(..., description="任务ID")
    title: str = Field(..., description="任务标题")
    description: Optional[str] = Field(None, description="任务描述")
    task_type: str = Field(..., description="任务类型")
    status: str = Field(..., description="任务状态")
    priority: str = Field(..., description="任务优先级")
    agent_id: Optional[int] = Field(None, description="执行任务的智能体ID")
    session_id: Optional[int] = Field(None, description="关联的会话ID")
    owner_id: int = Field(..., description="任务所有者ID")
    config: Optional[Dict[str, Any]] = Field(None, description="任务配置")
    input_data: Optional[Dict[str, Any]] = Field(None, description="输入数据")
    output_data: Optional[Dict[str, Any]] = Field(None, description="输出数据")
    error_message: Optional[str] = Field(None, description="错误信息")
    progress: float = Field(..., description="执行进度（0-100）")
    current_step: Optional[str] = Field(None, description="当前步骤")
    total_steps: int = Field(..., description="总步骤数")
    completed_steps: int = Field(..., description="已完成步骤数")
    retry_count: int = Field(..., description="重试次数")
    max_retries: int = Field(..., description="最大重试次数")
    scheduled_at: Optional[datetime] = Field(None, description="计划执行时间")
    started_at: Optional[datetime] = Field(None, description="开始执行时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    timeout: Optional[int] = Field(None, description="超时时间（秒）")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class TaskStepResponse(BaseModel):
    """任务步骤响应模型"""
    step_id: int = Field(..., description="步骤ID")
    task_id: int = Field(..., description="任务ID")
    step_name: str = Field(..., description="步骤名称")
    step_type: str = Field(..., description="步骤类型")
    status: str = Field(..., description="步骤状态")
    order_index: int = Field(..., description="执行顺序")
    config: Optional[Dict[str, Any]] = Field(None, description="步骤配置")
    input_data: Optional[Dict[str, Any]] = Field(None, description="输入数据")
    output_data: Optional[Dict[str, Any]] = Field(None, description="输出数据")
    error_message: Optional[str] = Field(None, description="错误信息")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    duration: Optional[float] = Field(None, description="执行时长（秒）")
    retry_count: int = Field(..., description="重试次数")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class TaskStatisticsResponse(BaseModel):
    """任务统计响应模型"""
    total_tasks: int = Field(..., description="总任务数")
    tasks_by_status: Dict[str, int] = Field(..., description="按状态分组的任务数")
    tasks_by_type: Dict[str, int] = Field(..., description="按类型分组的任务数")
    tasks_by_priority: Dict[str, int] = Field(..., description="按优先级分组的任务数")
    average_execution_time: float = Field(..., description="平均执行时间（秒）")
    success_rate: float = Field(..., description="成功率（百分比）")
    active_tasks: int = Field(..., description="活跃任务数")
    pending_tasks: int = Field(..., description="待执行任务数")
    failed_tasks: int = Field(..., description="失败任务数")
    completed_tasks: int = Field(..., description="已完成任务数")


class ApiResponse(BaseModel):
    """API响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")


# 依赖函数
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """获取当前用户"""
    try:
        token = credentials.credentials
        user_info = await auth_service.verify_token(token)
        return user_info
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的访问令牌",
            headers={"WWW-Authenticate": "Bearer"}
        )


async def check_task_permission(task_id: int, user_id: int, permission: str = "read"):
    """检查任务权限"""
    has_permission = await auth_service.check_resource_permission(
        user_id, "task", str(task_id), permission
    )
    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"没有对任务{task_id}的{permission}权限"
        )


# API接口
@router.post("/", response_model=TaskResponse, summary="创建任务")
async def create_task(
    request: TaskCreateRequest,
    background_tasks: BackgroundTasks,
    http_request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    创建新任务
    
    - **title**: 任务标题
    - **description**: 任务描述（可选）
    - **task_type**: 任务类型（agent_execution/data_processing/workflow/analysis/generation/custom）
    - **agent_id**: 执行任务的智能体ID（可选）
    - **session_id**: 关联的会话ID（可选）
    - **priority**: 任务优先级（low/medium/high/urgent）
    - **config**: 任务配置（可选）
    - **input_data**: 输入数据（可选）
    - **dependencies**: 依赖的任务ID列表（可选）
    - **scheduled_at**: 计划执行时间（可选）
    - **timeout**: 超时时间（秒，可选）
    - **max_retries**: 最大重试次数（默认3）
    - **tags**: 标签列表（可选）
    
    需要提供有效的访问令牌
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 速率限制检查
        rate_limiter.check_rate_limit(
            client_id=f"task_create:{current_user['user_id']}",
            limit=100  # 每分钟最多创建100个任务
        )
        
        # 准备任务数据
        task_data = {
            "title": request.title,
            "description": request.description,
            "task_type": request.task_type,
            "agent_id": request.agent_id,
            "session_id": request.session_id,
            "priority": request.priority,
            "config": request.config or {},
            "input_data": request.input_data or {},
            "dependencies": request.dependencies or [],
            "scheduled_at": request.scheduled_at,
            "timeout": request.timeout,
            "max_retries": request.max_retries,
            "tags": request.tags or [],
            "owner_id": current_user["user_id"]
        }
        
        # 创建任务
        result = await task_service.create_task(
            task_data, current_user["user_id"], client_ip
        )
        
        if result["success"]:
            task = result["data"]
            task_id = task["task_id"]
            
            logger.info(f"任务创建成功", extra={
                "task_id": task_id,
                "task_title": task["title"],
                "task_type": task["task_type"],
                "owner_id": current_user["user_id"],
                "client_ip": client_ip
            })
            
            # 如果是立即执行的任务，添加到后台任务队列
            if not request.scheduled_at:
                background_tasks.add_task(
                    task_service.execute_task_async,
                    task_id,
                    current_user["user_id"],
                    client_ip
                )
            
            return TaskResponse(**task)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except ValueError as e:
        logger.warning(f"任务创建失败: {str(e)}", extra={
            "user_id": current_user["user_id"],
            "client_ip": get_client_ip(http_request)
        })
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"任务创建异常: {str(e)}", extra={
            "user_id": current_user["user_id"],
            "client_ip": get_client_ip(http_request)
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="任务创建失败，请稍后重试"
        )


@router.get("/{task_id}", response_model=TaskResponse, summary="获取任务信息")
async def get_task(
    task_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取指定任务的详细信息
    
    - **task_id**: 任务ID
    
    需要对任务有读取权限
    """
    try:
        # 检查权限
        await check_task_permission(task_id, current_user["user_id"], "read")
        
        # 获取任务信息
        task = await task_service.get_task(task_id, current_user["user_id"])
        
        if task:
            return TaskResponse(**task)
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务信息异常: {str(e)}", extra={
            "task_id": task_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取任务信息失败"
        )


@router.put("/{task_id}", response_model=TaskResponse, summary="更新任务")
async def update_task(
    task_id: int,
    request: TaskUpdateRequest,
    http_request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    更新任务信息
    
    - **task_id**: 任务ID
    - **title**: 任务标题（可选）
    - **description**: 任务描述（可选）
    - **priority**: 任务优先级（可选）
    - **config**: 任务配置（可选）
    - **scheduled_at**: 计划执行时间（可选）
    - **timeout**: 超时时间（可选）
    - **max_retries**: 最大重试次数（可选）
    - **tags**: 标签列表（可选）
    
    需要对任务有写入权限
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 检查权限
        await check_task_permission(task_id, current_user["user_id"], "write")
        
        # 准备更新数据
        update_data = {}
        if request.title is not None:
            update_data["title"] = request.title
        if request.description is not None:
            update_data["description"] = request.description
        if request.priority is not None:
            update_data["priority"] = request.priority
        if request.config is not None:
            update_data["config"] = request.config
        if request.scheduled_at is not None:
            update_data["scheduled_at"] = request.scheduled_at
        if request.timeout is not None:
            update_data["timeout"] = request.timeout
        if request.max_retries is not None:
            update_data["max_retries"] = request.max_retries
        if request.tags is not None:
            update_data["tags"] = request.tags
        
        # 更新任务
        result = await task_service.update_task(
            task_id, update_data, current_user["user_id"], client_ip
        )
        
        if result["success"]:
            task = result["data"]
            
            logger.info(f"任务更新成功", extra={
                "task_id": task_id,
                "updated_fields": list(update_data.keys()),
                "user_id": current_user["user_id"],
                "client_ip": client_ip
            })
            
            return TaskResponse(**task)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except ValueError as e:
        logger.warning(f"任务更新失败: {str(e)}", extra={
            "task_id": task_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"任务更新异常: {str(e)}", extra={
            "task_id": task_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="任务更新失败，请稍后重试"
        )


@router.delete("/{task_id}", response_model=ApiResponse, summary="删除任务")
async def delete_task(
    task_id: int,
    hard_delete: bool = Query(False, description="是否硬删除"),
    http_request: Request = None,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    删除任务
    
    - **task_id**: 任务ID
    - **hard_delete**: 是否硬删除（默认false，软删除）
    
    需要对任务有删除权限
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 检查权限
        await check_task_permission(task_id, current_user["user_id"], "delete")
        
        # 删除任务
        result = await task_service.delete_task(
            task_id, hard_delete, current_user["user_id"], client_ip
        )
        
        if result["success"]:
            logger.info(f"任务删除成功", extra={
                "task_id": task_id,
                "hard_delete": hard_delete,
                "user_id": current_user["user_id"],
                "client_ip": client_ip
            })
            
            return ApiResponse(
                success=True,
                message=result["message"],
                data={
                    "task_id": task_id,
                    "hard_delete": hard_delete,
                    "deleted_at": datetime.utcnow().isoformat()
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"任务删除异常: {str(e)}", extra={
            "task_id": task_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="任务删除失败，请稍后重试"
        )


@router.get("/", response_model=Dict[str, Any], summary="获取任务列表")
async def list_tasks(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="页面大小"),
    task_type: Optional[str] = Query(None, description="任务类型过滤"),
    status: Optional[str] = Query(None, description="状态过滤"),
    priority: Optional[str] = Query(None, description="优先级过滤"),
    agent_id: Optional[int] = Query(None, description="智能体ID过滤"),
    session_id: Optional[int] = Query(None, description="会话ID过滤"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    tags: Optional[str] = Query(None, description="标签过滤（逗号分隔）"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取任务列表
    
    - **page**: 页码（默认1）
    - **page_size**: 页面大小（默认20，最大100）
    - **task_type**: 任务类型过滤
    - **status**: 状态过滤
    - **priority**: 优先级过滤
    - **agent_id**: 智能体ID过滤
    - **session_id**: 会话ID过滤
    - **search**: 搜索关键词（标题、描述）
    - **tags**: 标签过滤（逗号分隔）
    - **start_date**: 开始日期
    - **end_date**: 结束日期
    
    返回用户有权限访问的任务列表
    """
    try:
        # 构建过滤条件
        filters = {}
        if task_type:
            filters["task_type"] = task_type
        if status:
            filters["status"] = status
        if priority:
            filters["priority"] = priority
        if agent_id:
            filters["agent_id"] = agent_id
        if session_id:
            filters["session_id"] = session_id
        if search:
            filters["search"] = search
        if tags:
            filters["tags"] = [tag.strip() for tag in tags.split(",")]
        if start_date:
            filters["start_date"] = start_date.isoformat()
        if end_date:
            filters["end_date"] = end_date.isoformat()
        
        # 获取任务列表
        result = await task_service.list_tasks(
            current_user["user_id"], filters, page, page_size
        )
        
        return {
            "success": True,
            "message": "获取任务列表成功",
            "data": result
        }
        
    except Exception as e:
        logger.error(f"获取任务列表异常: {str(e)}", extra={
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取任务列表失败"
        )


@router.post("/{task_id}/execute", response_model=ApiResponse, summary="执行任务")
async def execute_task(
    task_id: int,
    background_tasks: BackgroundTasks,
    http_request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    手动执行任务
    
    - **task_id**: 任务ID
    
    需要对任务有执行权限
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 检查权限
        await check_task_permission(task_id, current_user["user_id"], "execute")
        
        # 速率限制检查
        rate_limiter.check_rate_limit(
            client_id=f"task_execute:{current_user['user_id']}",
            limit=50  # 每分钟最多执行50个任务
        )
        
        # 添加到后台任务队列
        background_tasks.add_task(
            task_service.execute_task_async,
            task_id,
            current_user["user_id"],
            client_ip
        )
        
        logger.info(f"任务执行请求已提交", extra={
            "task_id": task_id,
            "user_id": current_user["user_id"],
            "client_ip": client_ip
        })
        
        return ApiResponse(
            success=True,
            message="任务执行请求已提交",
            data={
                "task_id": task_id,
                "submitted_at": datetime.utcnow().isoformat()
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"任务执行异常: {str(e)}", extra={
            "task_id": task_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="任务执行失败，请稍后重试"
        )


@router.post("/{task_id}/pause", response_model=ApiResponse, summary="暂停任务")
async def pause_task(
    task_id: int,
    http_request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    暂停正在执行的任务
    
    - **task_id**: 任务ID
    
    需要对任务有控制权限
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 检查权限
        await check_task_permission(task_id, current_user["user_id"], "control")
        
        # 暂停任务
        result = await task_service.pause_task(
            task_id, current_user["user_id"], client_ip
        )
        
        if result["success"]:
            logger.info(f"任务暂停成功", extra={
                "task_id": task_id,
                "user_id": current_user["user_id"],
                "client_ip": client_ip
            })
            
            return ApiResponse(
                success=True,
                message=result["message"],
                data={
                    "task_id": task_id,
                    "paused_at": datetime.utcnow().isoformat()
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"任务暂停异常: {str(e)}", extra={
            "task_id": task_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="任务暂停失败，请稍后重试"
        )


@router.post("/{task_id}/cancel", response_model=ApiResponse, summary="取消任务")
async def cancel_task(
    task_id: int,
    http_request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    取消任务执行
    
    - **task_id**: 任务ID
    
    需要对任务有控制权限
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 检查权限
        await check_task_permission(task_id, current_user["user_id"], "control")
        
        # 取消任务
        result = await task_service.cancel_task(
            task_id, current_user["user_id"], client_ip
        )
        
        if result["success"]:
            logger.info(f"任务取消成功", extra={
                "task_id": task_id,
                "user_id": current_user["user_id"],
                "client_ip": client_ip
            })
            
            return ApiResponse(
                success=True,
                message=result["message"],
                data={
                    "task_id": task_id,
                    "cancelled_at": datetime.utcnow().isoformat()
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"任务取消异常: {str(e)}", extra={
            "task_id": task_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="任务取消失败，请稍后重试"
        )


@router.get("/{task_id}/steps", response_model=Dict[str, Any], summary="获取任务步骤")
async def get_task_steps(
    task_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取任务的执行步骤列表
    
    - **task_id**: 任务ID
    
    需要对任务有读取权限
    """
    try:
        # 检查权限
        await check_task_permission(task_id, current_user["user_id"], "read")
        
        # 获取任务步骤
        steps = await task_service.get_task_steps(task_id, current_user["user_id"])
        
        return {
            "success": True,
            "message": "获取任务步骤成功",
            "data": {
                "task_id": task_id,
                "steps": steps,
                "total_steps": len(steps)
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务步骤异常: {str(e)}", extra={
            "task_id": task_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取任务步骤失败"
        )


@router.get("/statistics", response_model=TaskStatisticsResponse, summary="获取任务统计")
async def get_task_statistics(
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    task_type: Optional[str] = Query(None, description="任务类型过滤"),
    agent_id: Optional[int] = Query(None, description="智能体ID过滤"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取任务统计信息
    
    - **start_date**: 开始日期（可选）
    - **end_date**: 结束日期（可选）
    - **task_type**: 任务类型过滤（可选）
    - **agent_id**: 智能体ID过滤（可选）
    
    返回用户有权限访问的任务统计数据
    """
    try:
        # 构建过滤条件
        filters = {
            "start_date": start_date.isoformat() if start_date else None,
            "end_date": end_date.isoformat() if end_date else None,
            "task_type": task_type,
            "agent_id": agent_id
        }
        
        # 获取统计信息
        statistics = await task_service.get_task_statistics(
            current_user["user_id"], filters
        )
        
        return TaskStatisticsResponse(**statistics)
        
    except Exception as e:
        logger.error(f"获取任务统计异常: {str(e)}", extra={
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取任务统计失败"
        )


@router.get("/stream/{task_id}", summary="流式获取任务状态")
async def stream_task_status(
    task_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    流式获取任务执行状态
    
    - **task_id**: 任务ID
    
    返回Server-Sent Events格式的任务状态流
    """
    try:
        # 检查权限
        await check_task_permission(task_id, current_user["user_id"], "read")
        
        async def generate_status():
            """生成任务状态流"""
            try:
                async for status_data in task_service.stream_task_status(
                    task_id, current_user["user_id"]
                ):
                    yield f"data: {json.dumps(status_data)}\n\n"
                    await asyncio.sleep(1)  # 每秒推送一次状态
            except Exception as e:
                logger.error(f"任务状态流生成异常: {str(e)}", extra={
                    "task_id": task_id,
                    "user_id": current_user["user_id"]
                })
                yield f"event: error\ndata: {{\"error\": \"任务状态流异常\", \"message\": \"{str(e)}\"}}\n\n"
        
        return StreamingResponse(
            generate_status(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"任务状态流异常: {str(e)}", extra={
            "task_id": task_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="任务状态流获取失败"
        )