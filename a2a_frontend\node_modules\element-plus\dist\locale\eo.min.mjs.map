{"version": 3, "file": "eo.min.mjs", "sources": ["../../../../packages/locale/lang/eo.ts"], "sourcesContent": ["export default {\n  name: 'eo',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: '<PERSON>',\n      clear: '<PERSON><PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: 'Nun',\n      today: '<PERSON><PERSON><PERSON>',\n      cancel: '<PERSON>uli<PERSON>',\n      clear: '<PERSON><PERSON><PERSON><PERSON>',\n      confirm: '<PERSON>',\n      selectDate: '<PERSON><PERSON><PERSON> daton',\n      selectTime: '<PERSON><PERSON><PERSON> horon',\n      startDate: 'Komen<PERSON> Da<PERSON>',\n      startTime: 'Komenca Horo',\n      endDate: '<PERSON><PERSON> Dato',\n      endTime: '<PERSON>a Horo',\n      prevYear: 'Antaŭa Jaro',\n      nextYear: 'Sekva <PERSON>',\n      prevMonth: 'Anta<PERSON>a Mona<PERSON>',\n      nextMonth: 'Sek<PERSON>',\n      year: 'J<PERSON>',\n      month1: 'Janua<PERSON>',\n      month2: 'Februaro',\n      month3: 'Mart<PERSON>',\n      month4: 'Aprilo',\n      month5: 'Majo',\n      month6: 'Jun<PERSON>',\n      month7: '<PERSON>',\n      month8: 'Aŭgusto',\n      month9: 'Septembro',\n      month10: 'Okto<PERSON>',\n      month11: 'Novem<PERSON>',\n      month12: 'Decembro',\n      week: '<PERSON><PERSON><PERSON><PERSON>',\n      weeks: {\n        sun: 'Dim',\n        mon: 'Lun',\n        tue: 'Mar',\n        wed: 'Mer',\n        thu: 'Ĵaŭ',\n        fri: 'Ven',\n        sat: 'Sab',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'Maj',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Aŭg',\n        sep: 'Sep',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Dec',\n      },\n    },\n    select: {\n      loading: 'Ŝarĝante',\n      noMatch: 'Neniuj kongruaj datumoj',\n      noData: 'Neniuj datumoj',\n      placeholder: 'Bonvolu elekti',\n    },\n    mention: {\n      loading: 'Ŝarĝante',\n    },\n    cascader: {\n      noMatch: 'Neniuj kongruaj datumoj',\n      loading: 'Ŝarĝante',\n      placeholder: 'Bonvolu elekti',\n      noData: 'Neniuj datumoj',\n    },\n    pagination: {\n      goto: 'Iru al',\n      pagesize: '/ paĝo',\n      total: 'Entute {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Mesaĝo',\n      confirm: 'Bone',\n      cancel: 'Nuligi',\n      error: 'Nevalida Enigo!',\n    },\n    upload: {\n      deleteTip: 'Premu \"Delete\" por forigi',\n      delete: 'Forigi',\n      preview: 'Antaŭrigardi',\n      continue: 'Daŭrigi',\n    },\n    table: {\n      emptyText: 'Neniuj datumoj',\n      confirmFilter: 'Konfirmi',\n      resetFilter: 'Restarigi',\n      clearFilter: 'Ĉiuj',\n      sumText: 'Sumo',\n    },\n    tree: {\n      emptyText: 'Neniuj datumoj',\n    },\n    transfer: {\n      noMatch: 'Neniuj kongruaj datumoj',\n      noData: 'Neniuj datumoj',\n      titles: ['Listo 1', 'Listo 2'],\n      filterPlaceholder: 'Enigu ŝlosilvorton',\n      noCheckedFormat: '{total} elementoj',\n      hasCheckedFormat: '{checked}/{total} elektitaj',\n    },\n    image: {\n      error: 'MALSUKCESIS',\n    },\n    pageHeader: {\n      title: 'Reen',\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,SAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,cAAc,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,kBAAkB,CAAC,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,oBAAoB,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC,yBAAyB,CAAC,MAAM,CAAC,gBAAgB,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,OAAO,CAAC,oBAAoB,CAAC,WAAW,CAAC,gBAAgB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,mBAAmB,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC,yBAAyB,CAAC,eAAe,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,6BAA6B,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;"}