#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统用户模型

定义用户相关的数据模型
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from .base import BaseModel


class User(BaseModel):
    """
    用户模型
    
    存储用户基本信息
    """
    
    __tablename__ = "users"
    
    # 基本信息
    username = Column(
        String(50),
        nullable=False,
        unique=True,
        comment="用户名"
    )
    
    email = Column(
        String(100),
        nullable=False,
        unique=True,
        comment="邮箱地址"
    )
    
    password_hash = Column(
        String(255),
        nullable=False,
        comment="密码哈希"
    )
    
    # 个人信息
    full_name = Column(
        String(100),
        nullable=True,
        comment="全名"
    )
    
    avatar_url = Column(
        String(500),
        nullable=True,
        comment="头像URL"
    )
    
    phone = Column(
        String(20),
        nullable=True,
        comment="手机号码"
    )
    
    # 角色和权限
    role = Column(
        String(20),
        nullable=False,
        default="user",
        comment="用户角色"
    )
    
    # 状态信息
    is_verified = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否已验证邮箱"
    )
    
    is_locked = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否被锁定"
    )
    
    # 登录信息
    last_login_at = Column(
        DateTime,
        nullable=True,
        comment="最后登录时间"
    )
    
    last_login_ip = Column(
        String(45),
        nullable=True,
        comment="最后登录IP"
    )
    
    login_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="登录次数"
    )
    
    # 密码相关
    password_changed_at = Column(
        DateTime,
        nullable=True,
        comment="密码修改时间"
    )
    
    failed_login_attempts = Column(
        Integer,
        nullable=False,
        default=0,
        comment="失败登录尝试次数"
    )
    
    locked_until = Column(
        DateTime,
        nullable=True,
        comment="锁定到期时间"
    )
    
    # 配置信息
    preferences = Column(
        Text,
        nullable=True,
        comment="用户偏好设置（JSON格式）"
    )
    
    timezone = Column(
        String(50),
        nullable=False,
        default="UTC",
        comment="时区"
    )
    
    language = Column(
        String(10),
        nullable=False,
        default="zh-CN",
        comment="语言"
    )
    
    # 关联关系
    tokens = relationship("UserToken", back_populates="user", cascade="all, delete-orphan")
    permissions = relationship("UserPermission", back_populates="user", cascade="all, delete-orphan")
    activity_logs = relationship("UserActivityLog", back_populates="user", cascade="all, delete-orphan")
    agents = relationship("Agent", foreign_keys="Agent.user_id", back_populates="user")
    sessions = relationship("Session", back_populates="user")
    tasks = relationship("Task", foreign_keys="Task.user_id", back_populates="user")
    task_steps = relationship("TaskStep", foreign_keys="TaskStep.user_id", back_populates="user")
    workflows = relationship("Workflow", foreign_keys="Workflow.user_id", back_populates="user")
    artifacts = relationship("Artifact", foreign_keys="Artifact.user_id", back_populates="user")
    memories = relationship("Memory", foreign_keys="Memory.user_id", back_populates="user")
    created_tools = relationship("Tool", back_populates="creator")
    user_configs = relationship("UserConfig", back_populates="user", cascade="all, delete-orphan")
    llm_configs = relationship("LLMConfig", back_populates="user", cascade="all, delete-orphan")
    tool_configs = relationship("ToolConfig", back_populates="user", cascade="all, delete-orphan")
    message_attachments = relationship("MessageAttachment", back_populates="user")
    tool_executions = relationship("ToolExecution", back_populates="user")
    
    # 索引
    __table_args__ = (
        Index("idx_users_username", "username"),
        Index("idx_users_email", "email"),
        Index("idx_users_role", "role"),
        Index("idx_users_is_active", "is_active"),
        Index("idx_users_deleted_at", "deleted_at"),
    )
    
    def set_preferences(self, preferences: Dict[str, Any]) -> None:
        """
        设置用户偏好
        
        Args:
            preferences: 偏好设置字典
        """
        import json
        self.preferences = json.dumps(preferences, ensure_ascii=False)
    
    def get_preferences(self) -> Dict[str, Any]:
        """
        获取用户偏好
        
        Returns:
            Dict[str, Any]: 偏好设置字典
        """
        if not self.preferences:
            return {}
        
        try:
            import json
            return json.loads(self.preferences)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def update_login_info(self, ip_address: str) -> None:
        """
        更新登录信息
        
        Args:
            ip_address: 登录IP地址
        """
        self.last_login_at = datetime.now()
        self.last_login_ip = ip_address
        self.login_count += 1
        self.failed_login_attempts = 0  # 重置失败次数
    
    def increment_failed_login(self) -> None:
        """
        增加失败登录次数
        """
        self.failed_login_attempts += 1
    
    def lock_account(self, duration_minutes: int = 30) -> None:
        """
        锁定账户
        
        Args:
            duration_minutes: 锁定时长（分钟）
        """
        from datetime import timedelta
        self.is_locked = True
        self.locked_until = datetime.now() + timedelta(minutes=duration_minutes)
    
    def unlock_account(self) -> None:
        """
        解锁账户
        """
        self.is_locked = False
        self.locked_until = None
        self.failed_login_attempts = 0
    
    @property
    def is_account_locked(self) -> bool:
        """
        检查账户是否被锁定
        
        Returns:
            bool: 是否被锁定
        """
        if not self.is_locked:
            return False
        
        if self.locked_until and datetime.now() > self.locked_until:
            # 锁定时间已过，自动解锁
            self.unlock_account()
            return False
        
        return True
    
    def __repr__(self) -> str:
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"
    
    @classmethod
    async def create_default_admin(cls, session) -> None:
        """
        创建默认管理员账号
        
        在系统初始化时，如果不存在管理员账号，则创建一个默认的管理员账号
        
        Args:
            session: 数据库会话
        """
        from sqlalchemy import select
        from loguru import logger
        from app.auth.password import PasswordHandler
        
        # 检查是否已存在管理员账号
        result = await session.execute(select(cls).where(cls.role == "super_admin"))
        admin = result.scalars().first()
        
        if admin:
            logger.info("已存在管理员账号，跳过创建默认管理员")
            return
        
        # 使用密码处理器生成正确的密码哈希
        password_handler = PasswordHandler()
        password_hash = password_handler.hash_password("Admin123!@#")
        
        # 创建默认管理员账号
        default_admin = cls(
            username="admin",
            email="<EMAIL>",
            password_hash=password_hash,
            full_name="系统管理员",
            role="super_admin",
            is_verified=True,
            is_active=True,
            timezone="UTC",
            language="zh-CN"
        )
        
        session.add(default_admin)
        await session.commit()
        logger.info("✅ 已创建默认管理员账号: admin (密码: Admin123!@#)")
        await session.refresh(default_admin)
        return default_admin


class UserToken(BaseModel):
    """
    用户令牌模型
    
    存储用户的JWT令牌信息
    """
    
    __tablename__ = "user_tokens"
    
    # 用户关联
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="用户ID"
    )
    
    # 会话信息
    session_id = Column(
        String(36),
        nullable=False,
        unique=True,
        comment="会话ID"
    )
    
    # 令牌信息
    access_token = Column(
        Text,
        nullable=True,
        comment="访问令牌"
    )
    
    refresh_token = Column(
        Text,
        nullable=False,
        comment="刷新令牌"
    )
    
    # 过期时间
    access_expires_at = Column(
        DateTime,
        nullable=True,
        comment="访问令牌过期时间"
    )
    
    refresh_expires_at = Column(
        DateTime,
        nullable=False,
        comment="刷新令牌过期时间"
    )
    
    # 状态信息
    is_revoked = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否已撤销"
    )
    
    revoked_at = Column(
        DateTime,
        nullable=True,
        comment="撤销时间"
    )
    
    # 设备信息
    device_info = Column(
        Text,
        nullable=True,
        comment="设备信息（JSON格式）"
    )
    
    ip_address = Column(
        String(45),
        nullable=True,
        comment="IP地址"
    )
    
    user_agent = Column(
        String(500),
        nullable=True,
        comment="用户代理"
    )
    
    # 关联关系
    user = relationship("User", back_populates="tokens")
    
    # 索引
    __table_args__ = (
        Index("idx_user_tokens_user_id", "user_id"),
        Index("idx_user_tokens_session_id", "session_id"),
        Index("idx_user_tokens_access_expires_at", "access_expires_at"),
        Index("idx_user_tokens_refresh_expires_at", "refresh_expires_at"),
        Index("idx_user_tokens_is_revoked", "is_revoked"),
    )
    
    def set_device_info(self, device_info: Dict[str, Any]) -> None:
        """
        设置设备信息
        
        Args:
            device_info: 设备信息字典
        """
        import json
        self.device_info = json.dumps(device_info, ensure_ascii=False)
    
    def get_device_info(self) -> Dict[str, Any]:
        """
        获取设备信息
        
        Returns:
            Dict[str, Any]: 设备信息字典
        """
        if not self.device_info:
            return {}
        
        try:
            import json
            return json.loads(self.device_info)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    @property
    def is_access_token_expired(self) -> bool:
        """
        检查访问令牌是否过期
        
        Returns:
            bool: 是否过期
        """
        if not self.access_expires_at:
            return True
        return datetime.now() > self.access_expires_at
    
    @property
    def is_refresh_token_expired(self) -> bool:
        """
        检查刷新令牌是否过期
        
        Returns:
            bool: 是否过期
        """
        return datetime.now() > self.refresh_expires_at
    
    def revoke(self) -> None:
        """
        撤销令牌
        """
        self.is_revoked = True
        self.revoked_at = datetime.now()
    
    def __repr__(self) -> str:
        return f"<UserToken(id={self.id}, user_id={self.user_id}, session_id='{self.session_id}')>"


class UserPermission(BaseModel):
    """
    用户权限模型
    
    存储用户的自定义权限
    """
    
    __tablename__ = "user_permissions"
    
    # 用户关联
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="用户ID"
    )
    
    # 权限信息
    permission_name = Column(
        String(100),
        nullable=False,
        comment="权限名称"
    )
    
    # 状态信息
    is_granted = Column(
        Boolean,
        nullable=False,
        default=True,
        comment="是否授予"
    )
    
    # 时间信息
    granted_at = Column(
        DateTime,
        nullable=True,
        comment="授予时间"
    )
    
    revoked_at = Column(
        DateTime,
        nullable=True,
        comment="撤销时间"
    )
    
    expires_at = Column(
        DateTime,
        nullable=True,
        comment="过期时间"
    )
    
    # 授予者信息
    granted_by = Column(
        Integer,
        nullable=True,
        comment="授予者ID"
    )
    
    # 备注
    notes = Column(
        Text,
        nullable=True,
        comment="备注"
    )
    
    # 关联关系
    user = relationship("User", back_populates="permissions")
    
    # 索引
    __table_args__ = (
        Index("idx_user_permissions_user_id", "user_id"),
        Index("idx_user_permissions_permission_name", "permission_name"),
        Index("idx_user_permissions_is_granted", "is_granted"),
        Index("idx_user_permissions_expires_at", "expires_at"),
        # 复合唯一索引
        Index("idx_user_permissions_unique", "user_id", "permission_name", unique=True),
    )
    
    @property
    def is_expired(self) -> bool:
        """
        检查权限是否过期
        
        Returns:
            bool: 是否过期
        """
        if not self.expires_at:
            return False
        return datetime.now() > self.expires_at
    
    @property
    def is_valid(self) -> bool:
        """
        检查权限是否有效
        
        Returns:
            bool: 是否有效
        """
        return self.is_granted and not self.is_expired
    
    def revoke(self, revoked_by: Optional[int] = None) -> None:
        """
        撤销权限
        
        Args:
            revoked_by: 撤销者ID
        """
        self.is_granted = False
        self.revoked_at = datetime.now()
        if revoked_by:
            self.notes = f"权限被用户 {revoked_by} 撤销"
    
    def __repr__(self) -> str:
        return f"<UserPermission(id={self.id}, user_id={self.user_id}, permission='{self.permission_name}')>"


class UserActivityLog(BaseModel):
    """
    用户活动日志模型
    
    记录用户的操作活动
    """
    
    __tablename__ = "user_activity_logs"
    
    # 用户关联
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="用户ID"
    )
    
    # 活动信息
    action = Column(
        String(100),
        nullable=False,
        comment="操作动作"
    )
    
    resource_type = Column(
        String(50),
        nullable=True,
        comment="资源类型"
    )
    
    resource_id = Column(
        Integer,
        nullable=True,
        comment="资源ID"
    )
    
    # 请求信息
    ip_address = Column(
        String(45),
        nullable=True,
        comment="IP地址"
    )
    
    user_agent = Column(
        String(500),
        nullable=True,
        comment="用户代理"
    )
    
    # 详细信息
    details = Column(
        Text,
        nullable=True,
        comment="详细信息（JSON格式）"
    )
    
    # 结果信息
    status = Column(
        String(20),
        nullable=False,
        default="success",
        comment="操作状态"
    )
    
    error_message = Column(
        Text,
        nullable=True,
        comment="错误信息"
    )
    
    # 时间信息
    timestamp = Column(
        DateTime,
        nullable=False,
        default=func.now(),
        comment="时间戳"
    )
    
    # 关联关系
    user = relationship("User", back_populates="activity_logs")
    
    # 索引
    __table_args__ = (
        Index("idx_user_activity_logs_user_id", "user_id"),
        Index("idx_user_activity_logs_action", "action"),
        Index("idx_user_activity_logs_timestamp", "timestamp"),
        Index("idx_user_activity_logs_status", "status"),
        Index("idx_user_activity_logs_resource", "resource_type", "resource_id"),
    )
    
    def set_details(self, details: Dict[str, Any]) -> None:
        """
        设置详细信息
        
        Args:
            details: 详细信息字典
        """
        import json
        self.details = json.dumps(details, ensure_ascii=False)
    
    def get_details(self) -> Dict[str, Any]:
        """
        获取详细信息
        
        Returns:
            Dict[str, Any]: 详细信息字典
        """
        if not self.details:
            return {}
        
        try:
            import json
            return json.loads(self.details)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def __repr__(self) -> str:
        return f"<UserActivityLog(id={self.id}, user_id={self.user_id}, action='{self.action}')>"