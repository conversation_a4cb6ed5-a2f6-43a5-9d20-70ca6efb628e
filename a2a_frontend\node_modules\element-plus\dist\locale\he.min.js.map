{"version": 3, "file": "he.min.js", "sources": ["../../../../packages/locale/lang/he.ts"], "sourcesContent": ["export default {\n  name: 'he',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'אישור',\n      clear: 'נקה',\n    },\n    datepicker: {\n      now: 'כעת',\n      today: 'היום',\n      cancel: 'בטל',\n      clear: 'נקה',\n      confirm: 'אישור',\n      selectDate: 'בחר תאריך',\n      selectTime: 'בחר זמן',\n      startDate: 'תאריך התחלה',\n      startTime: 'זמן התחלה',\n      endDate: 'תאריך סיום',\n      endTime: 'זמן סיום',\n      prevYear: 'שנה קודמת',\n      nextYear: 'שנה הבאה',\n      prevMonth: 'חודש קודם',\n      nextMonth: 'חודש הבא',\n      year: 'שנה',\n      month1: 'ינואר',\n      month2: 'פברואר',\n      month3: 'מרץ',\n      month4: 'אפריל',\n      month5: 'מאי',\n      month6: 'יוני',\n      month7: 'יולי',\n      month8: 'אוגוסט',\n      month9: 'ספטמבר',\n      month10: 'אוקטובר',\n      month11: 'נובמבר',\n      month12: 'דצמבר',\n      week: 'שבוע',\n      weeks: {\n        sun: 'א׳',\n        mon: 'ב׳',\n        tue: 'ג׳',\n        wed: 'ד׳',\n        thu: 'ה׳',\n        fri: 'ו׳',\n        sat: 'שבת',\n      },\n      months: {\n        jan: 'ינואר',\n        feb: 'פברואר',\n        mar: 'מרץ',\n        apr: 'אפריל',\n        may: 'מאי',\n        jun: 'יוני',\n        jul: 'יולי',\n        aug: 'אוגוסט',\n        sep: 'ספטמבר',\n        oct: 'אוקטובר',\n        nov: 'נובמבר',\n        dec: 'דצמבר',\n      },\n    },\n    select: {\n      loading: 'טוען',\n      noMatch: 'לא נמצאה התאמה',\n      noData: 'אין נתונים',\n      placeholder: 'שומר מקום',\n    },\n    mention: {\n      loading: 'טוען',\n    },\n    cascader: {\n      noMatch: 'לא נמצאה התאמה',\n      loading: 'טוען',\n      placeholder: 'שומר מקום',\n      noData: 'אין נתונים',\n    },\n    pagination: {\n      goto: 'עבור ל',\n      pagesize: '/עמוד',\n      total: 'כולל {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'הודעה',\n      confirm: 'אישור',\n      cancel: 'בטל',\n      error: 'קלט לא תקין',\n    },\n    upload: {\n      deleteTip: 'לחץ כדי למחוק',\n      delete: 'מחק',\n      preview: 'תצוגה מקדימה',\n      continue: 'המשך',\n    },\n    table: {\n      emptyText: 'אין נתונים',\n      confirmFilter: 'אישור',\n      resetFilter: 'נקה',\n      clearFilter: 'הכל',\n      sumText: 'סך הכל',\n    },\n    tree: {\n      emptyText: 'אין נתונים',\n    },\n    transfer: {\n      noMatch: 'לא נמצאה התאמה',\n      noData: 'אין נתונים',\n      titles: ['רשימה 1', 'רשימה 2'],\n      filterPlaceholder: 'סנן לפי...',\n      noCheckedFormat: 'פריטים {total}',\n      hasCheckedFormat: ' נבחרו {checked}/{total}',\n    },\n    image: {\n      error: 'שגיאה',\n    },\n    pageHeader: {\n      title: 'חזרה',\n    },\n    popconfirm: {\n      confirmButtonText: 'כן',\n      cancelButtonText: 'לא',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,KAAK,CAAC,0BAA0B,CAAC,MAAM,CAAC,oBAAoB,CAAC,KAAK,CAAC,oBAAoB,CAAC,OAAO,CAAC,gCAAgC,CAAC,UAAU,CAAC,mDAAmD,CAAC,UAAU,CAAC,uCAAuC,CAAC,SAAS,CAAC,+DAA+D,CAAC,SAAS,CAAC,mDAAmD,CAAC,OAAO,CAAC,yDAAyD,CAAC,OAAO,CAAC,6CAA6C,CAAC,QAAQ,CAAC,mDAAmD,CAAC,QAAQ,CAAC,6CAA6C,CAAC,SAAS,CAAC,mDAAmD,CAAC,SAAS,CAAC,6CAA6C,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,sCAAsC,CAAC,OAAO,CAAC,4CAA4C,CAAC,OAAO,CAAC,sCAAsC,CAAC,OAAO,CAAC,gCAAgC,CAAC,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,4CAA4C,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,0BAA0B,CAAC,OAAO,CAAC,4EAA4E,CAAC,MAAM,CAAC,yDAAyD,CAAC,WAAW,CAAC,mDAAmD,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,4EAA4E,CAAC,OAAO,CAAC,0BAA0B,CAAC,WAAW,CAAC,mDAAmD,CAAC,MAAM,CAAC,yDAAyD,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,iCAAiC,CAAC,QAAQ,CAAC,2BAA2B,CAAC,KAAK,CAAC,kCAAkC,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,gCAAgC,CAAC,OAAO,CAAC,gCAAgC,CAAC,MAAM,CAAC,oBAAoB,CAAC,KAAK,CAAC,0DAA0D,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,sEAAsE,CAAC,MAAM,CAAC,oBAAoB,CAAC,OAAO,CAAC,qEAAqE,CAAC,QAAQ,CAAC,0BAA0B,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,yDAAyD,CAAC,aAAa,CAAC,gCAAgC,CAAC,WAAW,CAAC,oBAAoB,CAAC,WAAW,CAAC,oBAAoB,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,yDAAyD,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,4EAA4E,CAAC,MAAM,CAAC,yDAAyD,CAAC,MAAM,CAAC,CAAC,kCAAkC,CAAC,kCAAkC,CAAC,CAAC,iBAAiB,CAAC,0CAA0C,CAAC,eAAe,CAAC,8CAA8C,CAAC,gBAAgB,CAAC,mDAAmD,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,cAAc,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;;;;;"}