# -*- coding: utf-8 -*-
"""
A2A多智能体系统用户管理服务

提供用户注册、登录、更新、删除等功能
"""

import json
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
from sqlalchemy import and_, or_, desc, func, select
from sqlalchemy.orm import Session, selectinload
from loguru import logger

from app.core.database import get_database_manager
from app.models.user import User, UserToken, UserPermission, UserActivityLog
from app.auth.password import PasswordHandler
from app.auth.jwt_handler import JWTHandler
from app.schemas.user import UserCreate, UserUpdate, UserResponse
from app.core.config import get_settings


class UserService:
    """
    用户管理服务
    
    提供用户的完整生命周期管理功能
    """
    
    def __init__(self):
        self.db_manager = get_database_manager()
        self.password_handler = PasswordHandler()
        self.jwt_handler = JWTHandler()
        self.settings = get_settings()
    
    async def create_user(self, user_data: UserCreate, created_by: Optional[int] = None) -> Dict[str, Any]:
        """
        创建新用户
        
        Args:
            user_data: 用户创建数据
            created_by: 创建者用户ID
            
        Returns:
            Dict[str, Any]: 创建结果
            
        Raises:
            ValueError: 用户名或邮箱已存在
            Exception: 创建失败
        """
        async with self.db_manager.get_session() as session:
            try:
                # 检查用户名是否已存在
                stmt = select(User).where(
                    or_(
                        User.username == user_data.username,
                        User.email == user_data.email
                    )
                )
                existing_user = await session.execute(stmt)
                if existing_user.scalar_one_or_none():
                    raise ValueError("用户名或邮箱已存在")
                
                # 加密密码
                password_hash = self.password_handler.hash_password(user_data.password)
                
                # 创建用户对象
                user = User(
                    username=user_data.username,
                    email=user_data.email,
                    password_hash=password_hash,
                    full_name=user_data.full_name,
                    phone=user_data.phone,
                    role=user_data.role or "user",
                    timezone=user_data.timezone or "UTC",
                    language=user_data.language or "zh-CN",
                    created_by=created_by
                )
                
                # 设置用户偏好
                if user_data.preferences:
                    user.set_preferences(user_data.preferences)
                
                session.add(user)
                await session.flush()  # 获取用户ID
                
                # 记录操作日志
                await self._log_user_activity(
                    session=session,
                    user_id=user.id,
                    action="user_created",
                    details={
                        "username": user.username,
                        "email": user.email,
                        "role": user.role,
                        "created_by": created_by
                    }
                )
                
                await session.commit()
                
                logger.info(f"用户创建成功: {user.username}", extra={
                    "user_id": user.id,
                    "username": user.username,
                    "email": user.email
                })
                
                return {
                    "success": True,
                    "message": "用户创建成功",
                    "data": {
                        "user_id": user.id,
                        "username": user.username,
                        "email": user.email,
                        "full_name": user.full_name,
                        "role": user.role,
                        "created_at": user.created_at.isoformat()
                    }
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"用户创建失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"用户创建异常: {str(e)}")
                raise Exception(f"用户创建失败: {str(e)}")
    
    async def authenticate_user(
        self, 
        username: str, 
        password: str, 
        ip_address: Optional[str] = None,
        device_info: Optional[Dict[str, Any]] = None,
        user_agent: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        用户认证
        
        Args:
            username: 用户名或邮箱
            password: 密码
            ip_address: 登录IP地址
            device_info: 设备信息
            user_agent: 用户代理
            
        Returns:
            Dict[str, Any]: 认证结果
            
        Raises:
            ValueError: 认证失败
        """
        async with self.db_manager.get_session() as session:
            try:
                # 查找用户
                stmt = select(User).where(
                    and_(
                        or_(
                            User.username == username,
                            User.email == username
                        ),
                        User.is_active == True,
                        User.deleted_at.is_(None)
                    )
                )
                result = await session.execute(stmt)
                user = result.scalar_one_or_none()
                
                # 调试日志：打印查找到的用户信息
                if user:
                    logger.info(f"找到用户 - ID: {user.id}, 用户名: {user.username}, 邮箱: {user.email}, 密码哈希: {user.password_hash[:20]}...")
                else:
                    logger.info(f"未找到用户 - 查询用户名: {username}")
                
                if not user:
                    await self._log_failed_login(session, username, ip_address, "用户不存在")
                    raise ValueError("用户名或密码错误")
                
                # 检查用户是否被锁定
                if user.is_locked or (user.locked_until and user.locked_until > datetime.utcnow()):
                    await self._log_failed_login(session, username, ip_address, "账户被锁定")
                    raise ValueError("账户已被锁定，请稍后再试")
                
                # 调试日志：打印密码验证信息
                logger.info(f"密码验证 - 输入密码: {password}, 存储的密码哈希: {user.password_hash}")
                password_valid = self.password_handler.verify_password(password, user.password_hash)
                logger.info(f"密码验证结果: {password_valid}")
                
                # 验证密码
                if not password_valid:
                    # 增加失败登录次数
                    user.failed_login_attempts += 1
                    
                    # 检查是否需要锁定账户
                    if user.failed_login_attempts >= self.settings.max_login_attempts:
                        user.is_locked = True
                        user.locked_until = datetime.utcnow() + timedelta(minutes=self.settings.account_lockout_duration_minutes)
                    
                    await session.commit()
                    await self._log_failed_login(session, username, ip_address, "密码错误")
                    raise ValueError("用户名或密码错误")
                
                # 重置失败登录次数
                user.failed_login_attempts = 0
                user.is_locked = False
                user.locked_until = None
                
                # 更新登录信息
                user.update_login_info(ip_address or "unknown")
                
                # 生成JWT令牌
                tokens = await self.jwt_handler.generate_tokens(
                    user_id=user.id,
                    ip_address=ip_address,
                    device_info=device_info,
                    user_agent=user_agent
                )
                
                # 记录登录日志
                await self._log_user_activity(
                    session=session,
                    user_id=user.id,
                    action="user_login",
                    details={
                        "ip_address": ip_address,
                        "session_id": tokens["session_id"]
                    },
                    ip_address=ip_address
                )
                
                await session.commit()
                
                logger.info(f"用户登录成功: {user.username}", extra={
                    "user_id": user.id,
                    "username": user.username,
                    "ip_address": ip_address
                })
                
                return {
                    "success": True,
                    "message": "登录成功",
                    "data": {
                        "access_token": tokens["access_token"],
                        "refresh_token": tokens["refresh_token"],
                        "token_type": tokens["token_type"],
                        "expires_in": tokens["expires_in"],
                        "user_info": {
                            "user_id": user.id,
                            "username": user.username,
                            "email": user.email,
                            "full_name": user.full_name,
                            "role": user.role,
                            "avatar_url": user.avatar_url
                        }
                    }
                }
                
            except ValueError as e:
                logger.warning(f"用户认证失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"用户认证异常: {str(e)}")
                raise Exception(f"认证失败: {str(e)}")
    
    async def get_user_by_id(self, user_id: int, include_permissions: bool = False) -> Optional[Dict[str, Any]]:
        """
        根据ID获取用户信息
        
        Args:
            user_id: 用户ID
            include_permissions: 是否包含权限信息
            
        Returns:
            Optional[Dict[str, Any]]: 用户信息
        """
        async with self.db_manager.get_session() as session:
            try:
                stmt = select(User).where(
                    and_(
                        User.id == user_id,
                        User.is_active == True,
                        User.deleted_at.is_(None)
                    )
                )
                
                if include_permissions:
                    stmt = stmt.options(selectinload(User.permissions))

                result = await session.execute(stmt)
                user = result.scalar_one_or_none()
                
                if not user:
                    return None
                
                user_data = {
                    "user_id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "full_name": user.full_name,
                    "avatar_url": user.avatar_url,
                    "phone": user.phone,
                    "role": user.role,
                    "is_verified": user.is_verified,
                    "timezone": user.timezone,
                    "language": user.language,
                    "preferences": user.get_preferences(),
                    "last_login_at": user.last_login_at.isoformat() if user.last_login_at else None,
                    "created_at": user.created_at.isoformat(),
                    "updated_at": user.updated_at.isoformat()
                }
                
                if include_permissions:
                    user_data["permissions"] = [
                        {
                            "resource_type": perm.resource_type,
                            "resource_id": perm.resource_id,
                            "permission": perm.permission,
                            "granted_at": perm.granted_at.isoformat()
                        }
                        for perm in user.permissions
                    ]
                
                return user_data
                
            except Exception as e:
                logger.error(f"获取用户信息异常: {str(e)}")
                raise Exception(f"获取用户信息失败: {str(e)}")
    
    async def update_user(self, user_id: int, user_data: UserUpdate, updated_by: Optional[int] = None) -> Dict[str, Any]:
        """
        更新用户信息
        
        Args:
            user_id: 用户ID
            user_data: 更新数据
            updated_by: 更新者用户ID
            
        Returns:
            Dict[str, Any]: 更新结果
            
        Raises:
            ValueError: 用户不存在或数据冲突
            Exception: 更新失败
        """
        async with self.db_manager.get_session() as session:
            try:
                # 查找用户
                stmt = select(User).where(
                    and_(
                        User.id == user_id,
                        User.is_active == True,
                        User.deleted_at.is_(None)
                    )
                )
                result = await session.execute(stmt)
                user = result.scalar_one_or_none()
                
                if not user:
                    raise ValueError("用户不存在")
                
                # 检查邮箱是否冲突
                if user_data.email and user_data.email != user.email:
                    stmt = select(User).where(
                        and_(
                            User.email == user_data.email,
                            User.id != user_id,
                            User.is_active == True,
                            User.deleted_at.is_(None)
                        )
                    )
                    existing_email = await session.execute(stmt)
                    if existing_email.scalar_one_or_none():
                        raise ValueError("邮箱已被其他用户使用")
                
                # 记录更新前的数据
                old_data = {
                    "email": user.email,
                    "full_name": user.full_name,
                    "phone": user.phone,
                    "timezone": user.timezone,
                    "language": user.language
                }
                
                # 更新用户信息
                if user_data.email is not None:
                    user.email = user_data.email
                if user_data.full_name is not None:
                    user.full_name = user_data.full_name
                if user_data.phone is not None:
                    user.phone = user_data.phone
                if user_data.avatar_url is not None:
                    user.avatar_url = user_data.avatar_url
                if user_data.timezone is not None:
                    user.timezone = user_data.timezone
                if user_data.language is not None:
                    user.language = user_data.language
                if user_data.preferences is not None:
                    user.set_preferences(user_data.preferences)
                
                user.updated_by = updated_by
                user.updated_at = datetime.utcnow()
                
                # 记录操作日志
                await self._log_user_activity(
                    session=session,
                    user_id=user.id,
                    action="user_updated",
                    details={
                        "old_data": old_data,
                        "new_data": {
                            "email": user.email,
                            "full_name": user.full_name,
                            "phone": user.phone,
                            "timezone": user.timezone,
                            "language": user.language
                        },
                        "updated_by": updated_by
                    }
                )
                
                await session.commit()
                
                logger.info(f"用户信息更新成功: {user.username}", extra={
                    "user_id": user.id,
                    "updated_by": updated_by
                })
                
                return {
                    "success": True,
                    "message": "用户信息更新成功",
                    "data": {
                        "user_id": user.id,
                        "username": user.username,
                        "email": user.email,
                        "full_name": user.full_name,
                        "updated_at": user.updated_at.isoformat()
                    }
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"用户更新失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"用户更新异常: {str(e)}")
                raise Exception(f"用户更新失败: {str(e)}")
    
    async def delete_user(self, user_id: int, deleted_by: Optional[int] = None, hard_delete: bool = False) -> Dict[str, Any]:
        """
        删除用户
        
        Args:
            user_id: 用户ID
            deleted_by: 删除者用户ID
            hard_delete: 是否硬删除
            
        Returns:
            Dict[str, Any]: 删除结果
            
        Raises:
            ValueError: 用户不存在
            Exception: 删除失败
        """
        async with self.db_manager.get_session() as session:
            try:
                # 查找用户
                stmt = select(User).where(User.id == user_id)
                result = await session.execute(stmt)
                user = result.scalar_one_or_none()
                
                if not user:
                    raise ValueError("用户不存在")
                
                if hard_delete:
                    # 硬删除：物理删除用户记录
                    await session.delete(user)
                    action = "user_hard_deleted"
                else:
                    # 软删除：标记为已删除
                    user.is_active = False
                    user.deleted_at = datetime.utcnow()
                    user.deleted_by = deleted_by
                    action = "user_soft_deleted"
                
                # 记录操作日志
                await self._log_user_activity(
                    session=session,
                    user_id=user.id,
                    action=action,
                    details={
                        "username": user.username,
                        "email": user.email,
                        "deleted_by": deleted_by,
                        "hard_delete": hard_delete
                    }
                )
                
                await session.commit()
                
                logger.info(f"用户删除成功: {user.username}", extra={
                    "user_id": user.id,
                    "deleted_by": deleted_by,
                    "hard_delete": hard_delete
                })
                
                return {
                    "success": True,
                    "message": "用户删除成功"
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"用户删除失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"用户删除异常: {str(e)}")
                raise Exception(f"用户删除失败: {str(e)}")
    
    async def change_password(self, user_id: int, old_password: str, new_password: str) -> Dict[str, Any]:
        """
        修改用户密码
        
        Args:
            user_id: 用户ID
            old_password: 旧密码
            new_password: 新密码
            
        Returns:
            Dict[str, Any]: 修改结果
            
        Raises:
            ValueError: 旧密码错误或用户不存在
            Exception: 修改失败
        """
        async with self.db_manager.get_session() as session:
            try:
                # 查找用户
                stmt = select(User).where(
                    and_(
                        User.id == user_id,
                        User.is_active == True,
                        User.deleted_at.is_(None)
                    )
                )
                result = await session.execute(stmt)
                user = result.scalar_one_or_none()
                
                if not user:
                    raise ValueError("用户不存在")
                
                # 验证旧密码
                if not self.password_handler.verify_password(old_password, user.password_hash):
                    raise ValueError("旧密码错误")
                
                # 加密新密码
                new_password_hash = self.password_handler.hash_password(new_password)
                
                # 更新密码
                user.password_hash = new_password_hash
                user.password_changed_at = datetime.utcnow()
                user.updated_at = datetime.utcnow()
                
                # 记录操作日志
                await self._log_user_activity(
                    session=session,
                    user_id=user.id,
                    action="password_changed",
                    details={"changed_at": user.password_changed_at.isoformat()}
                )
                
                await session.commit()
                
                logger.info(f"用户密码修改成功: {user.username}", extra={"user_id": user.id})
                
                return {
                    "success": True,
                    "message": "密码修改成功"
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"密码修改失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"密码修改异常: {str(e)}")
                raise Exception(f"密码修改失败: {str(e)}")
    
    async def grant_permission(self, user_id: int, resource_type: str, resource_id: Optional[str], 
                              permission: str, granted_by: int, expires_at: Optional[datetime] = None) -> Dict[str, Any]:
        """
        授予用户权限
        
        Args:
            user_id: 用户ID
            resource_type: 资源类型
            resource_id: 资源ID
            permission: 权限名称
            granted_by: 授权者用户ID
            expires_at: 过期时间
            
        Returns:
            Dict[str, Any]: 授权结果
        """
        async with self.db_manager.get_session() as session:
            try:
                # 检查用户是否存在
                stmt = select(User).where(
                    and_(
                        User.id == user_id,
                        User.is_active == True,
                        User.deleted_at.is_(None)
                    )
                )
                user_exists = await session.execute(stmt)
                if not user_exists.scalar_one_or_none():
                    raise ValueError("用户不存在")
                
                # 检查权限是否已存在
                stmt = select(UserPermission).where(
                    and_(
                        UserPermission.user_id == user_id,
                        UserPermission.resource_type == resource_type,
                        UserPermission.resource_id == resource_id,
                        UserPermission.permission == permission
                    )
                )
                existing_permission = await session.execute(stmt)
                
                if existing_permission.scalar_one_or_none():
                    raise ValueError("权限已存在")
                
                # 创建权限记录
                user_permission = UserPermission(
                    user_id=user_id,
                    resource_type=resource_type,
                    resource_id=resource_id,
                    permission=permission,
                    granted_by=granted_by,
                    expires_at=expires_at
                )
                
                session.add(user_permission)
                
                # 记录操作日志
                await self._log_user_activity(
                    session=session,
                    user_id=user_id,
                    action="permission_granted",
                    details={
                        "resource_type": resource_type,
                        "resource_id": resource_id,
                        "permission": permission,
                        "granted_by": granted_by,
                        "expires_at": expires_at.isoformat() if expires_at else None
                    }
                )
                
                await session.commit()
                
                logger.info(f"用户权限授予成功", extra={
                    "user_id": user_id,
                    "resource_type": resource_type,
                    "permission": permission,
                    "granted_by": granted_by
                })
                
                return {
                    "success": True,
                    "message": "权限授予成功"
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"权限授予失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"权限授予异常: {str(e)}")
                raise Exception(f"权限授予失败: {str(e)}")
    
    async def revoke_permission(self, user_id: int, resource_type: str, resource_id: Optional[str], 
                               permission: str, revoked_by: int) -> Dict[str, Any]:
        """
        撤销用户权限
        
        Args:
            user_id: 用户ID
            resource_type: 资源类型
            resource_id: 资源ID
            permission: 权限名称
            revoked_by: 撤销者用户ID
            
        Returns:
            Dict[str, Any]: 撤销结果
        """
        async with self.db_manager.get_session() as session:
            try:
                # 查找权限记录
                stmt = select(UserPermission).where(
                    and_(
                        UserPermission.user_id == user_id,
                        UserPermission.resource_type == resource_type,
                        UserPermission.resource_id == resource_id,
                        UserPermission.permission == permission
                    )
                )
                result = await session.execute(stmt)
                user_permission = result.scalar_one_or_none()
                
                if not user_permission:
                    raise ValueError("权限不存在")
                
                # 删除权限记录
                await session.delete(user_permission)
                
                # 记录操作日志
                await self._log_user_activity(
                    session=session,
                    user_id=user_id,
                    action="permission_revoked",
                    details={
                        "resource_type": resource_type,
                        "resource_id": resource_id,
                        "permission": permission,
                        "revoked_by": revoked_by
                    }
                )
                
                await session.commit()
                
                logger.info(f"用户权限撤销成功", extra={
                    "user_id": user_id,
                    "resource_type": resource_type,
                    "permission": permission,
                    "revoked_by": revoked_by
                })
                
                return {
                    "success": True,
                    "message": "权限撤销成功"
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"权限撤销失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"权限撤销异常: {str(e)}")
                raise Exception(f"权限撤销失败: {str(e)}")
    
    async def get_user_permissions(self, user_id: int) -> List[Dict[str, Any]]:
        """
        获取用户权限列表
        
        Args:
            user_id: 用户ID
            
        Returns:
            List[Dict[str, Any]]: 权限列表
        """
        async with self.db_manager.get_session() as session:
            try:
                stmt = select(UserPermission).where(
                    and_(
                        UserPermission.user_id == user_id,
                        or_(
                            UserPermission.expires_at.is_(None),
                            UserPermission.expires_at > datetime.utcnow()
                        )
                    )
                ).order_by(UserPermission.granted_at.desc())
                result = await session.execute(stmt)
                permissions = result.scalars().all()
                
                return [
                    {
                        "permission_id": perm.id,
                        "resource_type": perm.resource_type,
                        "resource_id": perm.resource_id,
                        "permission": perm.permission,
                        "granted_at": perm.granted_at.isoformat(),
                        "expires_at": perm.expires_at.isoformat() if perm.expires_at else None,
                        "granted_by": perm.granted_by
                    }
                    for perm in permissions
                ]
                
            except Exception as e:
                logger.error(f"获取用户权限异常: {str(e)}")
                raise Exception(f"获取用户权限失败: {str(e)}")
    
    async def get_user_activity_logs(self, user_id: int, page: int = 1, size: int = 20, 
                                   start_date: Optional[datetime] = None, 
                                   end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """
        获取用户操作日志
        
        Args:
            user_id: 用户ID
            page: 页码
            size: 每页数量
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            Dict[str, Any]: 日志列表和分页信息
        """
        async with self.db_manager.get_session() as session:
            try:
                # 构建查询条件
                conditions = [UserActivityLog.user_id == user_id]
                
                if start_date:
                    conditions.append(UserActivityLog.created_at >= start_date)
                if end_date:
                    conditions.append(UserActivityLog.created_at <= end_date)
                
                # 查询总数
                count_stmt = select(func.count(UserActivityLog.id)).where(and_(*conditions))
                total_result = await session.execute(count_stmt)
                total = total_result.scalar()

                # 查询日志列表
                offset = (page - 1) * size
                stmt = select(UserActivityLog).where(and_(*conditions)).order_by(desc(UserActivityLog.created_at)).offset(offset).limit(size)
                result = await session.execute(stmt)
                logs = result.scalars().all()
                
                return {
                    "logs": [
                        {
                            "log_id": log.id,
                            "action": log.action,
                            "resource_type": log.resource_type,
                            "resource_id": log.resource_id,
                            "details": json.loads(log.details) if log.details else {},
                            "ip_address": log.ip_address,
                            "user_agent": log.user_agent,
                            "status": log.status,
                            "created_at": log.created_at.isoformat()
                        }
                        for log in logs
                    ],
                    "pagination": {
                        "page": page,
                        "size": size,
                        "total": total,
                        "pages": (total + size - 1) // size
                    }
                }
                
            except Exception as e:
                logger.error(f"获取用户操作日志异常: {str(e)}")
                raise Exception(f"获取用户操作日志失败: {str(e)}")
    
    async def _log_user_activity(self, session, user_id: int, action: str, 
                                details: Optional[Dict[str, Any]] = None,
                                resource_type: Optional[str] = None,
                                resource_id: Optional[str] = None,
                                ip_address: Optional[str] = None,
                                user_agent: Optional[str] = None,
                                status: str = "success") -> None:
        """
        记录用户操作日志
        
        Args:
            session: 数据库会话
            user_id: 用户ID
            action: 操作类型
            details: 操作详情
            resource_type: 资源类型
            resource_id: 资源ID
            ip_address: IP地址
            user_agent: 用户代理
            status: 操作状态
        """
        try:
            activity_log = UserActivityLog(
                user_id=user_id,
                action=action,
                resource_type=resource_type,
                resource_id=resource_id,
                details=json.dumps(details, ensure_ascii=False) if details else None,
                ip_address=ip_address,
                user_agent=user_agent,
                status=status
            )
            
            session.add(activity_log)
            
        except Exception as e:
            logger.error(f"记录用户操作日志异常: {str(e)}")
    
    async def _log_failed_login(self, session, username: str, ip_address: Optional[str], reason: str) -> None:
        """
        记录失败登录日志
        
        Args:
            session: 数据库会话
            username: 用户名
            ip_address: IP地址
            reason: 失败原因
        """
        try:
            activity_log = UserActivityLog(
                user_id=None,  # 登录失败时可能没有用户ID
                action="login_failed",
                details=json.dumps({
                    "username": username,
                    "reason": reason
                }, ensure_ascii=False),
                ip_address=ip_address,
                status="failed"
            )
            
            session.add(activity_log)
            
        except Exception as e:
            logger.error(f"记录失败登录日志异常: {str(e)}")