# -*- coding: utf-8 -*-
"""
A2A多智能体系统WebSocket接口

基于Google ADK的WebSocket双向实时通信接口
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, Set
from enum import Enum

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException, Query
from fastapi.websockets import WebSocketState
from pydantic import BaseModel, Field, ValidationError
from loguru import logger
import jwt

from ...core.database import get_db
from ...core.config import settings
from ...auth.dependencies import get_current_user_from_token, get_current_user
from ...models import User
from ...services.stream_service import (
    stream_service,
    StreamConfig,
    StreamType,
    StreamStatus,
    CompressionType,
    StreamChunk
)
from ...services.user_service import UserService
from ...services.session_service import SessionService
from ...services.agent_service import AgentService


router = APIRouter(prefix="/ws", tags=["WebSocket通信"])


class WSMessageType(str, Enum):
    """WebSocket消息类型枚举"""
    CONNECT = "connect"
    DISCONNECT = "disconnect"
    PING = "ping"
    PONG = "pong"
    DATA = "data"
    ERROR = "error"
    STATUS = "status"
    SUBSCRIBE = "subscribe"
    UNSUBSCRIBE = "unsubscribe"
    BROADCAST = "broadcast"
    PRIVATE = "private"
    SYSTEM = "system"


class WSConnectionStatus(str, Enum):
    """WebSocket连接状态枚举"""
    CONNECTING = "connecting"
    CONNECTED = "connected"
    AUTHENTICATED = "authenticated"
    SUBSCRIBED = "subscribed"
    DISCONNECTING = "disconnecting"
    DISCONNECTED = "disconnected"
    ERROR = "error"


class WSMessage(BaseModel):
    """WebSocket消息模型"""
    type: WSMessageType = Field(..., description="消息类型")
    data: Any = Field(None, description="消息数据")
    target: Optional[str] = Field(None, description="目标标识")
    source: Optional[str] = Field(None, description="源标识")
    timestamp: Optional[datetime] = Field(None, description="时间戳")
    message_id: Optional[str] = Field(None, description="消息ID")
    reply_to: Optional[str] = Field(None, description="回复消息ID")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    
    def __init__(self, **data):
        if 'timestamp' not in data:
            data['timestamp'] = datetime.utcnow()
        super().__init__(**data)


class WSConnectionInfo:
    """WebSocket连接信息"""
    
    def __init__(
        self,
        websocket: WebSocket,
        connection_id: str,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None,
        agent_id: Optional[str] = None
    ):
        self.websocket = websocket
        self.connection_id = connection_id
        self.user_id = user_id
        self.session_id = session_id
        self.agent_id = agent_id
        self.status = WSConnectionStatus.CONNECTING
        self.created_at = datetime.utcnow()
        self.last_activity = datetime.utcnow()
        self.subscriptions: Set[str] = set()
        self.metadata: Dict[str, Any] = {}
        self.message_count = 0
        self.error_count = 0
        self.stream_connection_id: Optional[str] = None
    
    def update_activity(self):
        """更新活动时间"""
        self.last_activity = datetime.utcnow()
    
    def is_authenticated(self) -> bool:
        """检查是否已认证"""
        return self.user_id is not None and self.status in [
            WSConnectionStatus.AUTHENTICATED,
            WSConnectionStatus.SUBSCRIBED
        ]
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return (
            self.websocket.client_state == WebSocketState.CONNECTED and
            self.status not in [WSConnectionStatus.DISCONNECTING, WSConnectionStatus.DISCONNECTED]
        )
    
    async def send_message(self, message: WSMessage) -> bool:
        """发送消息"""
        try:
            if not self.is_connected():
                return False
            
            message_dict = message.dict()
            await self.websocket.send_text(json.dumps(message_dict, default=str, ensure_ascii=False))
            self.message_count += 1
            self.update_activity()
            return True
            
        except Exception as e:
            logger.error(f"发送WebSocket消息失败: {e}")
            self.error_count += 1
            return False
    
    async def close(self, code: int = 1000, reason: str = "Normal closure"):
        """关闭连接"""
        try:
            if self.is_connected():
                self.status = WSConnectionStatus.DISCONNECTING
                await self.websocket.close(code=code, reason=reason)
            self.status = WSConnectionStatus.DISCONNECTED
        except Exception as e:
            logger.error(f"关闭WebSocket连接失败: {e}")


class WebSocketManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 连接管理
        self.connections: Dict[str, WSConnectionInfo] = {}
        self.user_connections: Dict[int, Set[str]] = {}
        self.session_connections: Dict[str, Set[str]] = {}
        self.agent_connections: Dict[str, Set[str]] = {}
        self.subscription_connections: Dict[str, Set[str]] = {}
        
        # 统计信息
        self.total_connections = 0
        self.total_messages = 0
        self.total_errors = 0
        
        # 服务依赖
        self.user_service = UserService()
        self.session_service = SessionService()
        self.agent_service = AgentService()
        
        logger.info("WebSocket管理器初始化完成")
    
    async def connect(
        self,
        websocket: WebSocket,
        connection_id: str,
        token: Optional[str] = None,
        session_id: Optional[str] = None,
        agent_id: Optional[str] = None
    ) -> WSConnectionInfo:
        """
        建立WebSocket连接
        
        Args:
            websocket: WebSocket对象
            connection_id: 连接ID
            token: 认证令牌
            session_id: 会话ID
            agent_id: 智能体ID
        
        Returns:
            WSConnectionInfo: 连接信息
        """
        try:
            # 接受连接
            await websocket.accept()
            
            # 创建连接信息
            connection = WSConnectionInfo(
                websocket=websocket,
                connection_id=connection_id,
                session_id=session_id,
                agent_id=agent_id
            )
            
            # 存储连接
            self.connections[connection_id] = connection
            self.total_connections += 1
            
            # 更新连接状态
            connection.status = WSConnectionStatus.CONNECTED
            
            # 如果提供了令牌，进行认证
            if token:
                await self._authenticate_connection(connection, token)
            
            # 发送连接确认消息
            await connection.send_message(WSMessage(
                type=WSMessageType.CONNECT,
                data={
                    "connection_id": connection_id,
                    "status": connection.status.value,
                    "timestamp": datetime.utcnow().isoformat()
                }
            ))
            
            logger.info(f"WebSocket连接建立: {connection_id}")
            return connection
            
        except Exception as e:
            logger.error(f"建立WebSocket连接失败: {e}")
            raise
    
    async def disconnect(self, connection_id: str, code: int = 1000, reason: str = "Normal closure"):
        """
        断开WebSocket连接
        
        Args:
            connection_id: 连接ID
            code: 关闭代码
            reason: 关闭原因
        """
        try:
            connection = self.connections.get(connection_id)
            if not connection:
                return
            
            # 清理订阅
            await self._cleanup_subscriptions(connection)
            
            # 清理流式连接
            if connection.stream_connection_id:
                await stream_service.disconnect_stream(connection.stream_connection_id)
            
            # 清理索引
            await self._cleanup_connection_indexes(connection)
            
            # 关闭连接
            await connection.close(code, reason)
            
            # 删除连接
            del self.connections[connection_id]
            
            logger.info(f"WebSocket连接断开: {connection_id}")
            
        except Exception as e:
            logger.error(f"断开WebSocket连接失败: {e}")
    
    async def send_to_connection(
        self,
        connection_id: str,
        message: WSMessage
    ) -> bool:
        """
        发送消息到指定连接
        
        Args:
            connection_id: 连接ID
            message: 消息
        
        Returns:
            bool: 是否发送成功
        """
        try:
            connection = self.connections.get(connection_id)
            if not connection:
                return False
            
            success = await connection.send_message(message)
            if success:
                self.total_messages += 1
            else:
                self.total_errors += 1
            
            return success
            
        except Exception as e:
            logger.error(f"发送WebSocket消息失败: {e}")
            self.total_errors += 1
            return False
    
    async def send_to_user(
        self,
        user_id: int,
        message: WSMessage,
        exclude_connection: Optional[str] = None
    ) -> int:
        """
        发送消息到用户的所有连接
        
        Args:
            user_id: 用户ID
            message: 消息
            exclude_connection: 排除的连接ID
        
        Returns:
            int: 成功发送的连接数
        """
        try:
            connection_ids = self.user_connections.get(user_id, set())
            if exclude_connection:
                connection_ids = connection_ids - {exclude_connection}
            
            success_count = 0
            for connection_id in connection_ids:
                if await self.send_to_connection(connection_id, message):
                    success_count += 1
            
            return success_count
            
        except Exception as e:
            logger.error(f"发送用户WebSocket消息失败: {e}")
            return 0
    
    async def send_to_session(
        self,
        session_id: str,
        message: WSMessage,
        exclude_connection: Optional[str] = None
    ) -> int:
        """
        发送消息到会话的所有连接
        
        Args:
            session_id: 会话ID
            message: 消息
            exclude_connection: 排除的连接ID
        
        Returns:
            int: 成功发送的连接数
        """
        try:
            connection_ids = self.session_connections.get(session_id, set())
            if exclude_connection:
                connection_ids = connection_ids - {exclude_connection}
            
            success_count = 0
            for connection_id in connection_ids:
                if await self.send_to_connection(connection_id, message):
                    success_count += 1
            
            return success_count
            
        except Exception as e:
            logger.error(f"发送会话WebSocket消息失败: {e}")
            return 0
    
    async def broadcast(
        self,
        message: WSMessage,
        filter_func: Optional[callable] = None
    ) -> int:
        """
        广播消息到所有连接
        
        Args:
            message: 消息
            filter_func: 过滤函数
        
        Returns:
            int: 成功发送的连接数
        """
        try:
            success_count = 0
            for connection in self.connections.values():
                if filter_func and not filter_func(connection):
                    continue
                
                if await connection.send_message(message):
                    success_count += 1
                    self.total_messages += 1
                else:
                    self.total_errors += 1
            
            return success_count
            
        except Exception as e:
            logger.error(f"广播WebSocket消息失败: {e}")
            return 0
    
    async def subscribe(
        self,
        connection_id: str,
        topic: str
    ) -> bool:
        """
        订阅主题
        
        Args:
            connection_id: 连接ID
            topic: 主题
        
        Returns:
            bool: 是否订阅成功
        """
        try:
            connection = self.connections.get(connection_id)
            if not connection or not connection.is_authenticated():
                return False
            
            # 添加订阅
            connection.subscriptions.add(topic)
            
            # 更新索引
            if topic not in self.subscription_connections:
                self.subscription_connections[topic] = set()
            self.subscription_connections[topic].add(connection_id)
            
            # 更新连接状态
            if connection.status == WSConnectionStatus.AUTHENTICATED:
                connection.status = WSConnectionStatus.SUBSCRIBED
            
            logger.info(f"WebSocket订阅成功: {connection_id} -> {topic}")
            return True
            
        except Exception as e:
            logger.error(f"WebSocket订阅失败: {e}")
            return False
    
    async def unsubscribe(
        self,
        connection_id: str,
        topic: str
    ) -> bool:
        """
        取消订阅主题
        
        Args:
            connection_id: 连接ID
            topic: 主题
        
        Returns:
            bool: 是否取消成功
        """
        try:
            connection = self.connections.get(connection_id)
            if not connection:
                return False
            
            # 移除订阅
            connection.subscriptions.discard(topic)
            
            # 更新索引
            if topic in self.subscription_connections:
                self.subscription_connections[topic].discard(connection_id)
                if not self.subscription_connections[topic]:
                    del self.subscription_connections[topic]
            
            logger.info(f"WebSocket取消订阅成功: {connection_id} -> {topic}")
            return True
            
        except Exception as e:
            logger.error(f"WebSocket取消订阅失败: {e}")
            return False
    
    async def publish_to_topic(
        self,
        topic: str,
        message: WSMessage,
        exclude_connection: Optional[str] = None
    ) -> int:
        """
        发布消息到主题
        
        Args:
            topic: 主题
            message: 消息
            exclude_connection: 排除的连接ID
        
        Returns:
            int: 成功发送的连接数
        """
        try:
            connection_ids = self.subscription_connections.get(topic, set())
            if exclude_connection:
                connection_ids = connection_ids - {exclude_connection}
            
            success_count = 0
            for connection_id in connection_ids:
                if await self.send_to_connection(connection_id, message):
                    success_count += 1
            
            return success_count
            
        except Exception as e:
            logger.error(f"发布主题WebSocket消息失败: {e}")
            return 0
    
    def get_connection_info(self, connection_id: str) -> Optional[WSConnectionInfo]:
        """
        获取连接信息
        
        Args:
            connection_id: 连接ID
        
        Returns:
            Optional[WSConnectionInfo]: 连接信息
        """
        return self.connections.get(connection_id)
    
    def get_user_connections(self, user_id: int) -> List[WSConnectionInfo]:
        """
        获取用户的所有连接
        
        Args:
            user_id: 用户ID
        
        Returns:
            List[WSConnectionInfo]: 连接列表
        """
        connection_ids = self.user_connections.get(user_id, set())
        return [self.connections[cid] for cid in connection_ids if cid in self.connections]
    
    def get_metrics(self) -> Dict[str, Any]:
        """
        获取统计指标
        
        Returns:
            Dict[str, Any]: 统计指标
        """
        active_connections = len(self.connections)
        authenticated_connections = sum(
            1 for conn in self.connections.values() if conn.is_authenticated()
        )
        
        return {
            "total_connections": self.total_connections,
            "active_connections": active_connections,
            "authenticated_connections": authenticated_connections,
            "total_messages": self.total_messages,
            "total_errors": self.total_errors,
            "total_subscriptions": len(self.subscription_connections),
            "connections_by_status": self._get_connections_by_status(),
            "timestamp": datetime.utcnow().isoformat()
        }
    
    # 私有方法
    
    async def _authenticate_connection(self, connection: WSConnectionInfo, token: str):
        """
        认证连接
        
        Args:
            connection: 连接信息
            token: 认证令牌
        """
        try:
            # 验证令牌
            user = await get_current_user_from_token(token)
            if not user:
                raise ValueError("无效的认证令牌")
            
            # 更新连接信息
            connection.user_id = user.user_id
            connection.status = WSConnectionStatus.AUTHENTICATED
            
            # 更新索引
            if user.user_id not in self.user_connections:
                self.user_connections[user.user_id] = set()
            self.user_connections[user.user_id].add(connection.connection_id)
            
            if connection.session_id:
                if connection.session_id not in self.session_connections:
                    self.session_connections[connection.session_id] = set()
                self.session_connections[connection.session_id].add(connection.connection_id)
            
            if connection.agent_id:
                if connection.agent_id not in self.agent_connections:
                    self.agent_connections[connection.agent_id] = set()
                self.agent_connections[connection.agent_id].add(connection.connection_id)
            
            # 创建流式连接
            stream_config = StreamConfig(
                user_id=user.user_id,
                session_id=connection.session_id,
                agent_id=connection.agent_id,
                enable_persistence=True
            )
            
            stream_connection_id = await stream_service.create_stream(
                user_id=user.user_id,
                config=stream_config
            )
            connection.stream_connection_id = stream_connection_id
            
            # 注册流式数据回调
            await stream_service.register_data_callback(
                stream_connection_id,
                lambda chunk: self._handle_stream_data(connection.connection_id, chunk)
            )
            
            logger.info(f"WebSocket连接认证成功: {connection.connection_id} (用户: {user.user_id})")
            
        except Exception as e:
            logger.error(f"WebSocket连接认证失败: {e}")
            connection.status = WSConnectionStatus.ERROR
            raise
    
    async def _handle_stream_data(self, connection_id: str, chunk: StreamChunk):
        """
        处理流式数据
        
        Args:
            connection_id: 连接ID
            chunk: 数据块
        """
        try:
            # 构建WebSocket消息
            message = WSMessage(
                type=WSMessageType.DATA,
                data=chunk.to_dict(),
                source="stream",
                metadata={"stream_id": chunk.stream_id}
            )
            
            # 发送到WebSocket连接
            await self.send_to_connection(connection_id, message)
            
        except Exception as e:
            logger.error(f"处理流式数据失败: {e}")
    
    async def _cleanup_subscriptions(self, connection: WSConnectionInfo):
        """
        清理连接的订阅
        
        Args:
            connection: 连接信息
        """
        try:
            for topic in list(connection.subscriptions):
                await self.unsubscribe(connection.connection_id, topic)
                
        except Exception as e:
            logger.error(f"清理WebSocket订阅失败: {e}")
    
    async def _cleanup_connection_indexes(self, connection: WSConnectionInfo):
        """
        清理连接索引
        
        Args:
            connection: 连接信息
        """
        try:
            # 清理用户索引
            if connection.user_id and connection.user_id in self.user_connections:
                self.user_connections[connection.user_id].discard(connection.connection_id)
                if not self.user_connections[connection.user_id]:
                    del self.user_connections[connection.user_id]
            
            # 清理会话索引
            if connection.session_id and connection.session_id in self.session_connections:
                self.session_connections[connection.session_id].discard(connection.connection_id)
                if not self.session_connections[connection.session_id]:
                    del self.session_connections[connection.session_id]
            
            # 清理智能体索引
            if connection.agent_id and connection.agent_id in self.agent_connections:
                self.agent_connections[connection.agent_id].discard(connection.connection_id)
                if not self.agent_connections[connection.agent_id]:
                    del self.agent_connections[connection.agent_id]
                    
        except Exception as e:
            logger.error(f"清理WebSocket连接索引失败: {e}")
    
    def _get_connections_by_status(self) -> Dict[str, int]:
        """
        获取按状态分组的连接数
        
        Returns:
            Dict[str, int]: 状态连接数
        """
        status_counts = {}
        for connection in self.connections.values():
            status = connection.status.value
            status_counts[status] = status_counts.get(status, 0) + 1
        return status_counts


# 全局WebSocket管理器
ws_manager = WebSocketManager()


# WebSocket端点

@router.websocket("/connect")
async def websocket_endpoint(
    websocket: WebSocket,
    token: Optional[str] = Query(None, description="认证令牌"),
    session_id: Optional[str] = Query(None, description="会话ID"),
    agent_id: Optional[str] = Query(None, description="智能体ID")
):
    """
    WebSocket连接端点
    
    建立WebSocket连接，支持认证、会话绑定和智能体绑定。
    """
    import uuid
    connection_id = str(uuid.uuid4())
    
    try:
        # 建立连接
        connection = await ws_manager.connect(
            websocket=websocket,
            connection_id=connection_id,
            token=token,
            session_id=session_id,
            agent_id=agent_id
        )
        
        # 消息处理循环
        while connection.is_connected():
            try:
                # 接收消息
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                # 验证消息格式
                try:
                    message = WSMessage(**message_data)
                except ValidationError as e:
                    await connection.send_message(WSMessage(
                        type=WSMessageType.ERROR,
                        data={"error": "消息格式错误", "details": str(e)}
                    ))
                    continue
                
                # 处理消息
                await _handle_websocket_message(connection, message)
                
                # 更新活动时间
                connection.update_activity()
                
            except WebSocketDisconnect:
                logger.info(f"WebSocket客户端断开连接: {connection_id}")
                break
            except json.JSONDecodeError:
                await connection.send_message(WSMessage(
                    type=WSMessageType.ERROR,
                    data={"error": "JSON格式错误"}
                ))
            except Exception as e:
                logger.error(f"WebSocket消息处理错误: {e}")
                await connection.send_message(WSMessage(
                    type=WSMessageType.ERROR,
                    data={"error": str(e)}
                ))
                connection.error_count += 1
    
    except Exception as e:
        logger.error(f"WebSocket连接错误: {e}")
    
    finally:
        # 断开连接
        await ws_manager.disconnect(connection_id)


async def _handle_websocket_message(connection: WSConnectionInfo, message: WSMessage):
    """
    处理WebSocket消息
    
    Args:
        connection: 连接信息
        message: 消息
    """
    try:
        message_type = message.type
        
        if message_type == WSMessageType.PING:
            # 处理心跳
            await connection.send_message(WSMessage(
                type=WSMessageType.PONG,
                data={"timestamp": datetime.utcnow().isoformat()}
            ))
        
        elif message_type == WSMessageType.SUBSCRIBE:
            # 处理订阅
            if not connection.is_authenticated():
                await connection.send_message(WSMessage(
                    type=WSMessageType.ERROR,
                    data={"error": "需要认证才能订阅"}
                ))
                return
            
            topic = message.data.get("topic")
            if not topic:
                await connection.send_message(WSMessage(
                    type=WSMessageType.ERROR,
                    data={"error": "缺少订阅主题"}
                ))
                return
            
            success = await ws_manager.subscribe(connection.connection_id, topic)
            await connection.send_message(WSMessage(
                type=WSMessageType.STATUS,
                data={
                    "action": "subscribe",
                    "topic": topic,
                    "success": success
                }
            ))
        
        elif message_type == WSMessageType.UNSUBSCRIBE:
            # 处理取消订阅
            topic = message.data.get("topic")
            if not topic:
                await connection.send_message(WSMessage(
                    type=WSMessageType.ERROR,
                    data={"error": "缺少取消订阅主题"}
                ))
                return
            
            success = await ws_manager.unsubscribe(connection.connection_id, topic)
            await connection.send_message(WSMessage(
                type=WSMessageType.STATUS,
                data={
                    "action": "unsubscribe",
                    "topic": topic,
                    "success": success
                }
            ))
        
        elif message_type == WSMessageType.PRIVATE:
            # 处理私有消息
            if not connection.is_authenticated():
                await connection.send_message(WSMessage(
                    type=WSMessageType.ERROR,
                    data={"error": "需要认证才能发送私有消息"}
                ))
                return
            
            target_user_id = message.data.get("target_user_id")
            if not target_user_id:
                await connection.send_message(WSMessage(
                    type=WSMessageType.ERROR,
                    data={"error": "缺少目标用户ID"}
                ))
                return
            
            # 转发私有消息
            forward_message = WSMessage(
                type=WSMessageType.PRIVATE,
                data=message.data.get("content"),
                source=str(connection.user_id),
                target=str(target_user_id),
                metadata=message.metadata
            )
            
            success_count = await ws_manager.send_to_user(
                target_user_id,
                forward_message,
                exclude_connection=connection.connection_id
            )
            
            await connection.send_message(WSMessage(
                type=WSMessageType.STATUS,
                data={
                    "action": "private_message",
                    "target_user_id": target_user_id,
                    "delivered": success_count > 0,
                    "delivery_count": success_count
                }
            ))
        
        elif message_type == WSMessageType.BROADCAST:
            # 处理广播消息
            if not connection.is_authenticated():
                await connection.send_message(WSMessage(
                    type=WSMessageType.ERROR,
                    data={"error": "需要认证才能广播消息"}
                ))
                return
            
            # 检查广播权限（可以根据需要添加更严格的权限检查）
            topic = message.data.get("topic")
            if topic:
                # 发布到主题
                broadcast_message = WSMessage(
                    type=WSMessageType.BROADCAST,
                    data=message.data.get("content"),
                    source=str(connection.user_id),
                    metadata=message.metadata
                )
                
                success_count = await ws_manager.publish_to_topic(
                    topic,
                    broadcast_message,
                    exclude_connection=connection.connection_id
                )
                
                await connection.send_message(WSMessage(
                    type=WSMessageType.STATUS,
                    data={
                        "action": "broadcast",
                        "topic": topic,
                        "delivery_count": success_count
                    }
                ))
        
        elif message_type == WSMessageType.DATA:
            # 处理数据消息（发送到流式服务）
            if not connection.is_authenticated() or not connection.stream_connection_id:
                await connection.send_message(WSMessage(
                    type=WSMessageType.ERROR,
                    data={"error": "需要认证和流式连接才能发送数据"}
                ))
                return
            
            # 发送到流式服务
            success = await stream_service.send_chunk(
                connection_id=connection.stream_connection_id,
                data=message.data,
                chunk_type=StreamType.TEXT,
                metadata=message.metadata
            )
            
            await connection.send_message(WSMessage(
                type=WSMessageType.STATUS,
                data={
                    "action": "data_sent",
                    "success": success
                }
            ))
        
        else:
            # 未知消息类型
            await connection.send_message(WSMessage(
                type=WSMessageType.ERROR,
                data={"error": f"未知消息类型: {message_type}"}
            ))
    
    except Exception as e:
        logger.error(f"处理WebSocket消息失败: {e}")
        await connection.send_message(WSMessage(
            type=WSMessageType.ERROR,
            data={"error": str(e)}
        ))


# WebSocket管理API

@router.get("/connections", summary="获取WebSocket连接列表")
async def get_websocket_connections(
    current_user: User = Depends(get_current_user)
):
    """
    获取当前用户的WebSocket连接列表
    """
    try:
        connections = ws_manager.get_user_connections(current_user.user_id)
        
        result = []
        for connection in connections:
            result.append({
                "connection_id": connection.connection_id,
                "status": connection.status.value,
                "session_id": connection.session_id,
                "agent_id": connection.agent_id,
                "created_at": connection.created_at.isoformat(),
                "last_activity": connection.last_activity.isoformat(),
                "message_count": connection.message_count,
                "error_count": connection.error_count,
                "subscriptions": list(connection.subscriptions),
                "is_connected": connection.is_connected()
            })
        
        return result
        
    except Exception as e:
        logger.error(f"获取WebSocket连接列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取WebSocket连接列表失败")


@router.get("/metrics", summary="获取WebSocket服务指标")
async def get_websocket_metrics(
    current_user: User = Depends(get_current_user)
):
    """
    获取WebSocket服务指标
    
    需要管理员权限。
    """
    try:
        # 检查管理员权限
        if not current_user.is_admin:
            raise HTTPException(status_code=403, detail="需要管理员权限")
        
        return ws_manager.get_metrics()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取WebSocket服务指标失败: {e}")
        raise HTTPException(status_code=500, detail="获取WebSocket服务指标失败")