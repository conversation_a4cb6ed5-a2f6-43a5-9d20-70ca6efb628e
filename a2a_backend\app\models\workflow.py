#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统工作流模型

定义工作流相关的数据模型
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Index, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from .base import BaseModel


class Workflow(BaseModel):
    """
    工作流模型
    
    存储多智能体协作的工作流信息
    """
    
    __tablename__ = "workflows"
    
    # 用户和智能体关联
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="创建者用户ID"
    )
    
    owner_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="拥有者用户ID"
    )
    
    agent_id = Column(
        Integer,
        ForeignKey("agents.id", ondelete="CASCADE"),
        nullable=True,
        comment="主智能体ID"
    )
    
    # 工作流基本信息
    workflow_id = Column(
        String(36),
        nullable=False,
        unique=True,
        comment="工作流唯一标识"
    )
    
    name = Column(
        String(200),
        nullable=False,
        comment="工作流名称"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="工作流描述"
    )
    
    # 工作流类型和版本
    workflow_type = Column(
        String(50),
        nullable=False,
        default="sequential",
        comment="工作流类型"
    )
    
    version = Column(
        String(20),
        nullable=False,
        default="1.0.0",
        comment="版本号"
    )
    
    # 工作流状态
    status = Column(
        String(20),
        nullable=False,
        default="draft",
        comment="工作流状态"
    )
    
    # 工作流定义
    definition = Column(
        Text,
        nullable=False,
        comment="工作流定义（JSON格式）"
    )
    
    # 配置信息
    config = Column(
        Text,
        nullable=True,
        comment="工作流配置（JSON格式）"
    )
    
    # 输入输出定义
    input_schema = Column(
        Text,
        nullable=True,
        comment="输入数据结构定义（JSON Schema）"
    )
    
    output_schema = Column(
        Text,
        nullable=True,
        comment="输出数据结构定义（JSON Schema）"
    )
    
    # 状态信息
    is_template = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否为模板"
    )
    
    is_public = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否公开"
    )
    
    # 统计信息
    execution_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="执行次数"
    )
    
    success_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="成功次数"
    )
    
    average_duration = Column(
        Float,
        nullable=False,
        default=0.0,
        comment="平均执行时长（秒）"
    )
    
    # 时间信息
    last_executed_at = Column(
        DateTime,
        nullable=True,
        comment="最后执行时间"
    )
    
    # 关联关系
    user = relationship("User", foreign_keys=[user_id], back_populates="workflows")
    owner = relationship("User", foreign_keys=[owner_id])
    agent = relationship("Agent", back_populates="workflows")
    executions = relationship("WorkflowExecution", back_populates="workflow", cascade="all, delete-orphan")
    steps = relationship("WorkflowStep", back_populates="workflow", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index("idx_workflows_user_id", "user_id"),
        Index("idx_workflows_agent_id", "agent_id"),
        Index("idx_workflows_workflow_id", "workflow_id"),
        Index("idx_workflows_name", "name"),
        Index("idx_workflows_type", "workflow_type"),
        Index("idx_workflows_status", "status"),
        Index("idx_workflows_is_template", "is_template"),
        Index("idx_workflows_is_public", "is_public"),
        Index("idx_workflows_is_active", "is_active"),
    )
    
    def set_definition(self, definition: Dict[str, Any]) -> None:
        """
        设置工作流定义
        
        Args:
            definition: 工作流定义字典
        """
        import json
        self.definition = json.dumps(definition, ensure_ascii=False)
    
    def get_definition(self) -> Dict[str, Any]:
        """
        获取工作流定义
        
        Returns:
            Dict[str, Any]: 工作流定义字典
        """
        if not self.definition:
            return {}
        
        try:
            import json
            return json.loads(self.definition)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_config(self, config: Dict[str, Any]) -> None:
        """
        设置工作流配置
        
        Args:
            config: 配置字典
        """
        import json
        self.config = json.dumps(config, ensure_ascii=False)
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取工作流配置
        
        Returns:
            Dict[str, Any]: 配置字典
        """
        if not self.config:
            return {}
        
        try:
            import json
            return json.loads(self.config)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def publish(self) -> None:
        """
        发布工作流
        """
        self.status = "published"
    
    def archive(self) -> None:
        """
        归档工作流
        """
        self.status = "archived"
    
    def update_execution_stats(self, duration: float, success: bool = True) -> None:
        """
        更新执行统计
        
        Args:
            duration: 执行时长（秒）
            success: 是否成功
        """
        self.execution_count += 1
        if success:
            self.success_count += 1
        
        # 更新平均执行时长
        if self.execution_count == 1:
            self.average_duration = duration
        else:
            self.average_duration = (
                (self.average_duration * (self.execution_count - 1) + duration) / self.execution_count
            )
        
        self.last_executed_at = datetime.now()
    
    @property
    def success_rate(self) -> float:
        """
        获取成功率
        
        Returns:
            float: 成功率
        """
        if self.execution_count == 0:
            return 0.0
        return self.success_count / self.execution_count
    
    def __repr__(self) -> str:
        return f"<Workflow(id={self.id}, workflow_id='{self.workflow_id}', name='{self.name}')>"


class WorkflowStep(BaseModel):
    """
    工作流步骤模型
    
    定义工作流中的各个步骤
    """
    
    __tablename__ = "workflow_steps"
    
    # 工作流关联
    workflow_id = Column(
        Integer,
        ForeignKey("workflows.id", ondelete="CASCADE"),
        nullable=False,
        comment="工作流ID"
    )
    
    # 用户关联
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="创建者用户ID"
    )
    
    owner_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="拥有者用户ID"
    )
    
    # 步骤信息
    step_id = Column(
        String(36),
        nullable=False,
        comment="步骤唯一标识"
    )
    
    name = Column(
        String(200),
        nullable=False,
        comment="步骤名称"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="步骤描述"
    )
    
    # 步骤类型和顺序
    step_type = Column(
        String(50),
        nullable=False,
        comment="步骤类型"
    )
    
    order_index = Column(
        Integer,
        nullable=False,
        comment="执行顺序"
    )
    
    # 步骤配置
    config = Column(
        Text,
        nullable=True,
        comment="步骤配置（JSON格式）"
    )
    
    # 输入输出映射
    input_mapping = Column(
        Text,
        nullable=True,
        comment="输入映射（JSON格式）"
    )
    
    output_mapping = Column(
        Text,
        nullable=True,
        comment="输出映射（JSON格式）"
    )
    
    # 条件和依赖
    conditions = Column(
        Text,
        nullable=True,
        comment="执行条件（JSON格式）"
    )
    
    dependencies = Column(
        Text,
        nullable=True,
        comment="依赖步骤（JSON格式）"
    )
    
    # 错误处理
    error_handling = Column(
        Text,
        nullable=True,
        comment="错误处理配置（JSON格式）"
    )
    
    # 超时设置
    timeout_seconds = Column(
        Integer,
        nullable=True,
        comment="超时时间（秒）"
    )
    
    # 重试配置
    max_retries = Column(
        Integer,
        nullable=False,
        default=0,
        comment="最大重试次数"
    )
    
    retry_delay = Column(
        Integer,
        nullable=False,
        default=0,
        comment="重试延迟（秒）"
    )
    
    # 关联关系
    workflow = relationship("Workflow", back_populates="steps")
    
    # 索引
    __table_args__ = (
        Index("idx_workflow_steps_workflow_id", "workflow_id"),
        Index("idx_workflow_steps_step_id", "step_id"),
        Index("idx_workflow_steps_order", "order_index"),
        Index("idx_workflow_steps_type", "step_type"),
        # 复合唯一索引
        Index("idx_workflow_steps_unique", "workflow_id", "step_id", unique=True),
    )
    
    def set_config(self, config: Dict[str, Any]) -> None:
        """
        设置步骤配置
        
        Args:
            config: 配置字典
        """
        import json
        self.config = json.dumps(config, ensure_ascii=False)
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取步骤配置
        
        Returns:
            Dict[str, Any]: 配置字典
        """
        if not self.config:
            return {}
        
        try:
            import json
            return json.loads(self.config)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_input_mapping(self, mapping: Dict[str, Any]) -> None:
        """
        设置输入映射
        
        Args:
            mapping: 映射字典
        """
        import json
        self.input_mapping = json.dumps(mapping, ensure_ascii=False)
    
    def get_input_mapping(self) -> Dict[str, Any]:
        """
        获取输入映射
        
        Returns:
            Dict[str, Any]: 映射字典
        """
        if not self.input_mapping:
            return {}
        
        try:
            import json
            return json.loads(self.input_mapping)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_output_mapping(self, mapping: Dict[str, Any]) -> None:
        """
        设置输出映射
        
        Args:
            mapping: 映射字典
        """
        import json
        self.output_mapping = json.dumps(mapping, ensure_ascii=False)
    
    def get_output_mapping(self) -> Dict[str, Any]:
        """
        获取输出映射
        
        Returns:
            Dict[str, Any]: 映射字典
        """
        if not self.output_mapping:
            return {}
        
        try:
            import json
            return json.loads(self.output_mapping)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_conditions(self, conditions: List[Dict[str, Any]]) -> None:
        """
        设置执行条件
        
        Args:
            conditions: 条件列表
        """
        import json
        self.conditions = json.dumps(conditions, ensure_ascii=False)
    
    def get_conditions(self) -> List[Dict[str, Any]]:
        """
        获取执行条件
        
        Returns:
            List[Dict[str, Any]]: 条件列表
        """
        if not self.conditions:
            return []
        
        try:
            import json
            return json.loads(self.conditions)
        except (json.JSONDecodeError, TypeError):
            return []
    
    def set_dependencies(self, dependencies: List[str]) -> None:
        """
        设置依赖步骤
        
        Args:
            dependencies: 依赖步骤ID列表
        """
        import json
        self.dependencies = json.dumps(dependencies, ensure_ascii=False)
    
    def get_dependencies(self) -> List[str]:
        """
        获取依赖步骤
        
        Returns:
            List[str]: 依赖步骤ID列表
        """
        if not self.dependencies:
            return []
        
        try:
            import json
            return json.loads(self.dependencies)
        except (json.JSONDecodeError, TypeError):
            return []
    
    def __repr__(self) -> str:
        return f"<WorkflowStep(id={self.id}, step_id='{self.step_id}', name='{self.name}')>"


class WorkflowExecution(BaseModel):
    """
    工作流执行记录模型
    
    记录工作流的执行历史
    """
    
    __tablename__ = "workflow_executions"
    
    # 工作流关联
    workflow_id = Column(
        Integer,
        ForeignKey("workflows.id", ondelete="CASCADE"),
        nullable=False,
        comment="工作流ID"
    )
    
    # 执行信息
    execution_id = Column(
        String(36),
        nullable=False,
        unique=True,
        comment="执行唯一标识"
    )
    
    # 触发信息
    trigger_type = Column(
        String(50),
        nullable=False,
        default="manual",
        comment="触发类型"
    )
    
    triggered_by = Column(
        Integer,
        nullable=True,
        comment="触发者ID"
    )
    
    # 执行状态
    status = Column(
        String(20),
        nullable=False,
        default="running",
        comment="执行状态"
    )
    
    current_step = Column(
        String(36),
        nullable=True,
        comment="当前执行步骤ID"
    )
    
    # 输入输出数据
    input_data = Column(
        Text,
        nullable=True,
        comment="输入数据（JSON格式）"
    )
    
    output_data = Column(
        Text,
        nullable=True,
        comment="输出数据（JSON格式）"
    )
    
    # 执行日志
    execution_log = Column(
        Text,
        nullable=True,
        comment="执行日志（JSON格式）"
    )
    
    # 错误信息
    error_message = Column(
        Text,
        nullable=True,
        comment="错误信息"
    )
    
    error_step = Column(
        String(36),
        nullable=True,
        comment="出错步骤ID"
    )
    
    # 时间信息
    started_at = Column(
        DateTime,
        nullable=False,
        default=func.now(),
        comment="开始时间"
    )
    
    completed_at = Column(
        DateTime,
        nullable=True,
        comment="完成时间"
    )
    
    # 性能指标
    duration = Column(
        Float,
        nullable=True,
        comment="执行时长（秒）"
    )
    
    steps_completed = Column(
        Integer,
        nullable=False,
        default=0,
        comment="已完成步骤数"
    )
    
    total_steps = Column(
        Integer,
        nullable=False,
        default=0,
        comment="总步骤数"
    )
    
    # 资源使用
    tokens_used = Column(
        Integer,
        nullable=False,
        default=0,
        comment="使用的token数"
    )
    
    cost = Column(
        Float,
        nullable=False,
        default=0.0,
        comment="执行费用"
    )
    
    # 关联关系
    workflow = relationship("Workflow", back_populates="executions")
    step_executions = relationship("WorkflowStepExecution", back_populates="workflow_execution", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index("idx_workflow_executions_workflow_id", "workflow_id"),
        Index("idx_workflow_executions_execution_id", "execution_id"),
        Index("idx_workflow_executions_status", "status"),
        Index("idx_workflow_executions_trigger_type", "trigger_type"),
        Index("idx_workflow_executions_triggered_by", "triggered_by"),
        Index("idx_workflow_executions_started_at", "started_at"),
    )
    
    def set_input_data(self, data: Dict[str, Any]) -> None:
        """
        设置输入数据
        
        Args:
            data: 输入数据字典
        """
        import json
        self.input_data = json.dumps(data, ensure_ascii=False)
    
    def get_input_data(self) -> Dict[str, Any]:
        """
        获取输入数据
        
        Returns:
            Dict[str, Any]: 输入数据字典
        """
        if not self.input_data:
            return {}
        
        try:
            import json
            return json.loads(self.input_data)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_output_data(self, data: Dict[str, Any]) -> None:
        """
        设置输出数据
        
        Args:
            data: 输出数据字典
        """
        import json
        self.output_data = json.dumps(data, ensure_ascii=False)
    
    def get_output_data(self) -> Dict[str, Any]:
        """
        获取输出数据
        
        Returns:
            Dict[str, Any]: 输出数据字典
        """
        if not self.output_data:
            return {}
        
        try:
            import json
            return json.loads(self.output_data)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_execution_log(self, log: List[Dict[str, Any]]) -> None:
        """
        设置执行日志
        
        Args:
            log: 执行日志列表
        """
        import json
        self.execution_log = json.dumps(log, ensure_ascii=False)
    
    def get_execution_log(self) -> List[Dict[str, Any]]:
        """
        获取执行日志
        
        Returns:
            List[Dict[str, Any]]: 执行日志列表
        """
        if not self.execution_log:
            return []
        
        try:
            import json
            return json.loads(self.execution_log)
        except (json.JSONDecodeError, TypeError):
            return []
    
    def add_log_entry(self, entry: Dict[str, Any]) -> None:
        """
        添加日志条目
        
        Args:
            entry: 日志条目
        """
        log = self.get_execution_log()
        entry["timestamp"] = datetime.now().isoformat()
        log.append(entry)
        self.set_execution_log(log)
    
    def complete_execution(self, output_data: Dict[str, Any] = None) -> None:
        """
        完成执行
        
        Args:
            output_data: 输出数据
        """
        self.status = "completed"
        self.completed_at = datetime.now()
        self.duration = (self.completed_at - self.started_at).total_seconds()
        
        if output_data:
            self.set_output_data(output_data)
        
        self.add_log_entry({
            "action": "complete",
            "message": "工作流执行完成"
        })
    
    def fail_execution(self, error_message: str, error_step: str = None) -> None:
        """
        执行失败
        
        Args:
            error_message: 错误信息
            error_step: 出错步骤ID
        """
        self.status = "failed"
        self.error_message = error_message
        self.error_step = error_step
        self.completed_at = datetime.now()
        self.duration = (self.completed_at - self.started_at).total_seconds()
        
        self.add_log_entry({
            "action": "fail",
            "message": f"工作流执行失败: {error_message}",
            "step": error_step
        })
    
    def update_progress(self, current_step: str, steps_completed: int) -> None:
        """
        更新执行进度
        
        Args:
            current_step: 当前步骤ID
            steps_completed: 已完成步骤数
        """
        self.current_step = current_step
        self.steps_completed = steps_completed
        
        self.add_log_entry({
            "action": "progress",
            "message": f"执行进度: {steps_completed}/{self.total_steps}",
            "step": current_step
        })
    
    @property
    def progress_percentage(self) -> float:
        """
        获取进度百分比
        
        Returns:
            float: 进度百分比
        """
        if self.total_steps == 0:
            return 0.0
        return (self.steps_completed / self.total_steps) * 100
    
    def __repr__(self) -> str:
        return f"<WorkflowExecution(id={self.id}, execution_id='{self.execution_id}', status='{self.status}')>"


class WorkflowStepExecution(BaseModel):
    """
    工作流步骤执行记录模型
    
    记录工作流中每个步骤的执行详情
    """
    
    __tablename__ = "workflow_step_executions"
    
    # 工作流执行关联
    workflow_execution_id = Column(
        Integer,
        ForeignKey("workflow_executions.id", ondelete="CASCADE"),
        nullable=False,
        comment="工作流执行ID"
    )
    
    # 步骤信息
    step_id = Column(
        String(36),
        nullable=False,
        comment="步骤ID"
    )
    
    step_name = Column(
        String(200),
        nullable=False,
        comment="步骤名称"
    )
    
    # 执行状态
    status = Column(
        String(20),
        nullable=False,
        default="pending",
        comment="执行状态"
    )
    
    # 输入输出数据
    input_data = Column(
        Text,
        nullable=True,
        comment="输入数据（JSON格式）"
    )
    
    output_data = Column(
        Text,
        nullable=True,
        comment="输出数据（JSON格式）"
    )
    
    # 错误信息
    error_message = Column(
        Text,
        nullable=True,
        comment="错误信息"
    )
    
    # 时间信息
    started_at = Column(
        DateTime,
        nullable=True,
        comment="开始时间"
    )
    
    completed_at = Column(
        DateTime,
        nullable=True,
        comment="完成时间"
    )
    
    duration = Column(
        Float,
        nullable=True,
        comment="执行时长（秒）"
    )
    
    # 重试信息
    retry_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="重试次数"
    )
    
    # 关联关系
    workflow_execution = relationship("WorkflowExecution", back_populates="step_executions")
    
    # 索引
    __table_args__ = (
        Index("idx_workflow_step_executions_workflow_execution_id", "workflow_execution_id"),
        Index("idx_workflow_step_executions_step_id", "step_id"),
        Index("idx_workflow_step_executions_status", "status"),
        Index("idx_workflow_step_executions_started_at", "started_at"),
    )
    
    def set_input_data(self, data: Dict[str, Any]) -> None:
        """
        设置输入数据
        
        Args:
            data: 输入数据字典
        """
        import json
        self.input_data = json.dumps(data, ensure_ascii=False)
    
    def get_input_data(self) -> Dict[str, Any]:
        """
        获取输入数据
        
        Returns:
            Dict[str, Any]: 输入数据字典
        """
        if not self.input_data:
            return {}
        
        try:
            import json
            return json.loads(self.input_data)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_output_data(self, data: Dict[str, Any]) -> None:
        """
        设置输出数据
        
        Args:
            data: 输出数据字典
        """
        import json
        self.output_data = json.dumps(data, ensure_ascii=False)
    
    def get_output_data(self) -> Dict[str, Any]:
        """
        获取输出数据
        
        Returns:
            Dict[str, Any]: 输出数据字典
        """
        if not self.output_data:
            return {}
        
        try:
            import json
            return json.loads(self.output_data)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def start_execution(self, input_data: Dict[str, Any] = None) -> None:
        """
        开始执行步骤
        
        Args:
            input_data: 输入数据
        """
        self.status = "running"
        self.started_at = datetime.now()
        
        if input_data:
            self.set_input_data(input_data)
    
    def complete_execution(self, output_data: Dict[str, Any] = None) -> None:
        """
        完成步骤执行
        
        Args:
            output_data: 输出数据
        """
        self.status = "completed"
        self.completed_at = datetime.now()
        
        if self.started_at:
            self.duration = (self.completed_at - self.started_at).total_seconds()
        
        if output_data:
            self.set_output_data(output_data)
    
    def fail_execution(self, error_message: str) -> None:
        """
        步骤执行失败
        
        Args:
            error_message: 错误信息
        """
        self.status = "failed"
        self.error_message = error_message
        self.completed_at = datetime.now()
        
        if self.started_at:
            self.duration = (self.completed_at - self.started_at).total_seconds()
    
    def retry_execution(self) -> None:
        """
        重试执行
        """
        self.retry_count += 1
        self.status = "retrying"
        self.error_message = None
    
    def __repr__(self) -> str:
        return f"<WorkflowStepExecution(id={self.id}, step_id='{self.step_id}', status='{self.status}')>"