#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统认证中间件

处理JWT令牌验证和用户认证
"""

import json
from typing import Optional, Dict, Any
from datetime import datetime

from fastapi import Request, Response, status
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi.responses import JSONResponse
from starlette.middleware.base import RequestResponseEndpoint
from loguru import logger
from sqlalchemy import text

from app.core.database import get_database_manager
from app.auth.jwt_handler import JWTHandler
from app.auth.permissions import PermissionChecker


class AuthMiddleware(BaseHTTPMiddleware):
    """
    用户认证中间件
    
    验证JWT令牌并设置用户上下文
    """
    
    def __init__(self, app, exclude_paths: Optional[list] = None):
        """
        初始化认证中间件
        
        Args:
            app: FastAPI应用实例
            exclude_paths: 排除认证的路径列表
        """
        super().__init__(app)
        self.exclude_paths = exclude_paths or [
            "/health", "/metrics", "/docs", "/redoc", "/openapi.json",
            "/api/v1/auth/login", "/api/v1/auth/register", "/api/v1/auth/refresh"
        ]
        self.jwt_handler = JWTHandler()
        self.permission_checker = PermissionChecker()
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """
        处理用户认证
        
        Args:
            request: HTTP请求
            call_next: 下一个中间件或路由处理器
            
        Returns:
            Response: HTTP响应
        """
        path = request.url.path
        
        # 跳过排除的路径
        if path in self.exclude_paths or path.startswith("/static/"):
            return await call_next(request)
        
        # 获取Authorization头
        authorization = request.headers.get("Authorization")
        if not authorization:
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={
                    "error": {
                        "code": 401,
                        "message": "缺少认证令牌",
                        "type": "authentication_required"
                    }
                }
            )
        
        # 验证Bearer令牌格式
        if not authorization.startswith("Bearer "):
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={
                    "error": {
                        "code": 401,
                        "message": "无效的认证令牌格式",
                        "type": "invalid_token_format"
                    }
                }
            )
        
        # 提取令牌
        token = authorization[7:]  # 移除"Bearer "前缀
        
        try:
            # 验证JWT令牌
            payload = await self.jwt_handler.verify_token(token)
            user_id = payload.get("user_id")
            session_id = payload.get("session_id")
            
            if not user_id:
                raise ValueError("令牌中缺少用户ID")
            
            # 验证用户状态
            user_info = await self._get_user_info(user_id)
            if not user_info:
                raise ValueError("用户不存在")
            
            if not user_info["is_active"]:
                raise ValueError("用户已被禁用")
            
            # 设置请求上下文
            request.state.user_id = user_id
            request.state.session_id = session_id
            request.state.user_info = user_info
            
            # 检查权限（如果需要）
            if hasattr(request.state, "required_permissions"):
                has_permission = await self.permission_checker.check_permissions(
                    user_id, request.state.required_permissions
                )
                if not has_permission:
                    return JSONResponse(
                        status_code=status.HTTP_403_FORBIDDEN,
                        content={
                            "error": {
                                "code": 403,
                                "message": "权限不足",
                                "type": "insufficient_permissions"
                            }
                        }
                    )
            
            # 记录用户活动
            await self._log_user_activity(user_id, request)
            
            return await call_next(request)
        
        except Exception as e:
            logger.warning(f"认证失败: {str(e)}", extra={"token": token[:20] + "..." if len(token) > 20 else token})
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={
                    "error": {
                        "code": 401,
                        "message": "认证失败",
                        "detail": str(e),
                        "type": "authentication_failed"
                    }
                }
            )
    
    async def _get_user_info(self, user_id: int) -> Optional[Dict[str, Any]]:
        """
        获取用户信息
        
        Args:
            user_id: 用户ID
            
        Returns:
            Optional[Dict[str, Any]]: 用户信息
        """
        try:
            db_manager = get_database_manager()
            engine = db_manager.get_engine()
            
            sql = """
                SELECT id, username, email, is_active, role, created_at
                FROM users
                WHERE id = :user_id AND deleted_at IS NULL
            """
            
            async with engine.begin() as conn:
                result = await conn.execute(text(sql), {"user_id": user_id})
                row = result.fetchone()
                
                if row:
                    return {
                        "id": row[0],
                        "username": row[1],
                        "email": row[2],
                        "is_active": row[3],
                        "role": row[4],
                        "created_at": row[5]
                    }
                
                return None
        
        except Exception as e:
            logger.error(f"获取用户信息失败: {str(e)}")
            return None
    
    async def _log_user_activity(self, user_id: int, request: Request) -> None:
        """
        记录用户活动
        
        Args:
            user_id: 用户ID
            request: HTTP请求
        """
        try:
            db_manager = get_database_manager()
            engine = db_manager.get_engine()
            
            activity_data = {
                "action": f"{request.method} {request.url.path}",
                "ip_address": request.client.host if request.client else "unknown",
                "user_agent": request.headers.get("user-agent", ""),
                "request_id": getattr(request.state, "request_id", None)
            }
            
            sql = """
                INSERT INTO user_activity_logs (
                    user_id, action, ip_address, user_agent, details, timestamp
                ) VALUES (
                    :user_id, :action, :ip_address, :user_agent, :details, :timestamp
                )
            """
            
            async with engine.begin() as conn:
                await conn.execute(text(sql), {
                    "user_id": user_id,
                    "action": activity_data["action"],
                    "ip_address": activity_data["ip_address"],
                    "user_agent": activity_data["user_agent"][:200],
                    "details": json.dumps(activity_data, ensure_ascii=False),
                    "timestamp": datetime.now()
                })
        
        except Exception as e:
            logger.error(f"记录用户活动失败: {str(e)}")