echo 'Terminal capability test'
conda activate a2a_backend && python -m py_compile a2a_backend/main.py
conda activate a2a_backend; python -m py_compile a2a_backend/main.py
conda activate a2a_backend; pip install flake8 mypy
conda activate a2a_backend; cd a2a_backend; flake8 --max-line-length=120 --ignore=E501,W503 .
echo 'Terminal capability test'
cd a2a_backend && conda activate a2a_backend && python main.py
cd a2a_backend
conda activate a2a_backend
pip install -r requirements.txt
pip install --user -r requirements.txt
conda
conda env list
conda activate a2a_backend
python main.py
