{"version": 3, "file": "ky.min.js", "sources": ["../../../../packages/locale/lang/ky.ts"], "sourcesContent": ["export default {\n  name: 'ky',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'Мурунку',\n      clear: 'ачык',\n    },\n    datepicker: {\n      now: 'азыр',\n      today: 'бүгүн',\n      cancel: 'жокко чыгарылды',\n      clear: 'ачык',\n      confirm: 'белгилөө',\n      selectDate: 'дата',\n      selectTime: 'тандоо убактысы',\n      startDate: 'Башталган датасы',\n      startTime: 'Start убакыт',\n      endDate: 'Бүткөн датасы',\n      endTime: 'End убакыт',\n      prevYear: 'өткөн жылы',\n      nextYear: 'бир жылдан кийин',\n      prevMonth: 'Өткөн айда',\n      nextMonth: 'Кийинки ай',\n      year: 'жыл',\n      month1: 'биринчи ай',\n      month2: 'Экин<PERSON>и айда',\n      month3: 'Үчүнчү айда',\n      month4: 'Төртүнчү айда',\n      month5: 'бешинчи айда',\n      month6: 'Алгач<PERSON>ы алты ай',\n      month7: 'жетинчи айда',\n      month8: 'сегизинчи ай',\n      month9: 'Алгачкы тогуз ай',\n      month10: 'онунчу айда',\n      month11: 'он биринчи ай',\n      month12: 'он экинчи айда',\n      // week: '周次',\n      weeks: {\n        sun: 'жети жума',\n        mon: 'дүйшөмбү',\n        tue: 'шейшемби',\n        wed: 'шаршемби',\n        thu: 'бейшемби',\n        fri: 'жума',\n        sat: 'ишемби',\n      },\n      months: {\n        jan: 'биринчи ай',\n        feb: 'Экинчи айда',\n        mar: 'Үчүнчү айда',\n        apr: 'Төртүнчү айда',\n        may: 'бешинчи айда',\n        jun: 'Алгачкы алты ай',\n        jul: 'жетинчи айда',\n        aug: 'сегизинчи ай',\n        sep: 'Алгачкы тогуз ай',\n        oct: 'онунчу айда',\n        nov: 'он биринчи ай',\n        dec: 'он экинчи айда',\n      },\n    },\n    select: {\n      loading: 'Жүктөлүүдө',\n      noMatch: 'Дал келген маалыматтар',\n      noData: 'маалымат жок',\n      placeholder: 'тандоо',\n    },\n    mention: {\n      loading: 'Жүктөлүүдө',\n    },\n    cascader: {\n      noMatch: 'Дал келген маалыматтар',\n      loading: 'Жүктөлүүдө',\n      placeholder: 'тандоо',\n      noData: 'маалымат жок',\n    },\n    pagination: {\n      goto: 'Мурунку',\n      pagesize: 'бир',\n      total: 'бүтүндөй {total} сан ',\n      pageClassifier: 'бет',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'тез',\n      confirm: 'белгилөө',\n      cancel: 'жокко чыгарылды',\n      error: 'Маалыматтарды киргизүү мыйзамдуу эмес!',\n    },\n    upload: {\n      deleteTip: 'Жок кылуу баскычын басуу жок',\n      delete: 'жок кылуу',\n      preview: 'ЖМКнын картинки',\n      continue: 'жүктөп бер',\n    },\n    table: {\n      emptyText: 'маалымат жок',\n      confirmFilter: 'чыпка',\n      resetFilter: 'кайра орнотуу',\n      clearFilter: 'бүткөн',\n      sumText: 'Бардыгы болуп',\n    },\n    tree: {\n      emptyText: 'маалымат жок',\n    },\n    transfer: {\n      noMatch: 'Дал келген маалыматтар',\n      noData: 'маалымат жок',\n      titles: ['1 тизмеси', '2 тизмеси'],\n      filterPlaceholder: 'Сураныч, издөө кирет',\n      noCheckedFormat: 'бүтүндөй {total} сан',\n      hasCheckedFormat: 'Тандалган {checked}/{total} сан',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,0BAA0B,CAAC,KAAK,CAAC,gCAAgC,CAAC,MAAM,CAAC,uFAAuF,CAAC,KAAK,CAAC,0BAA0B,CAAC,OAAO,CAAC,kDAAkD,CAAC,UAAU,CAAC,0BAA0B,CAAC,UAAU,CAAC,uFAAuF,CAAC,SAAS,CAAC,6FAA6F,CAAC,SAAS,CAAC,4CAA4C,CAAC,OAAO,CAAC,2EAA2E,CAAC,OAAO,CAAC,0CAA0C,CAAC,QAAQ,CAAC,yDAAyD,CAAC,QAAQ,CAAC,wFAAwF,CAAC,SAAS,CAAC,yDAAyD,CAAC,SAAS,CAAC,yDAAyD,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,yDAAyD,CAAC,MAAM,CAAC,+DAA+D,CAAC,MAAM,CAAC,+DAA+D,CAAC,MAAM,CAAC,2EAA2E,CAAC,MAAM,CAAC,qEAAqE,CAAC,MAAM,CAAC,kFAAkF,CAAC,MAAM,CAAC,qEAAqE,CAAC,MAAM,CAAC,qEAAqE,CAAC,MAAM,CAAC,wFAAwF,CAAC,OAAO,CAAC,+DAA+D,CAAC,OAAO,CAAC,sEAAsE,CAAC,OAAO,CAAC,4EAA4E,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,mDAAmD,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,yDAAyD,CAAC,GAAG,CAAC,+DAA+D,CAAC,GAAG,CAAC,+DAA+D,CAAC,GAAG,CAAC,2EAA2E,CAAC,GAAG,CAAC,qEAAqE,CAAC,GAAG,CAAC,kFAAkF,CAAC,GAAG,CAAC,qEAAqE,CAAC,GAAG,CAAC,qEAAqE,CAAC,GAAG,CAAC,wFAAwF,CAAC,GAAG,CAAC,+DAA+D,CAAC,GAAG,CAAC,sEAAsE,CAAC,GAAG,CAAC,4EAA4E,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,8DAA8D,CAAC,OAAO,CAAC,4HAA4H,CAAC,MAAM,CAAC,qEAAqE,CAAC,WAAW,CAAC,sCAAsC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,8DAA8D,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,4HAA4H,CAAC,OAAO,CAAC,8DAA8D,CAAC,WAAW,CAAC,sCAAsC,CAAC,MAAM,CAAC,qEAAqE,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,4CAA4C,CAAC,QAAQ,CAAC,oBAAoB,CAAC,KAAK,CAAC,8EAA8E,CAAC,cAAc,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,oBAAoB,CAAC,OAAO,CAAC,kDAAkD,CAAC,MAAM,CAAC,uFAAuF,CAAC,KAAK,CAAC,kNAAkN,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,sJAAsJ,CAAC,MAAM,CAAC,mDAAmD,CAAC,OAAO,CAAC,uFAAuF,CAAC,QAAQ,CAAC,yDAAyD,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,qEAAqE,CAAC,aAAa,CAAC,gCAAgC,CAAC,WAAW,CAAC,2EAA2E,CAAC,WAAW,CAAC,sCAAsC,CAAC,OAAO,CAAC,2EAA2E,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,qEAAqE,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,4HAA4H,CAAC,MAAM,CAAC,qEAAqE,CAAC,MAAM,CAAC,CAAC,8CAA8C,CAAC,8CAA8C,CAAC,CAAC,iBAAiB,CAAC,2GAA2G,CAAC,eAAe,CAAC,6EAA6E,CAAC,gBAAgB,CAAC,6FAA6F,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;;;;;"}