<template>
  <div class="system-monitor">
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card title="系统状态">
          <template #header>
            <span>系统状态</span>
          </template>
          <div class="status-grid">
            <div class="status-item">
              <span class="label">CPU使用率:</span>
              <el-progress :percentage="systemStatus.cpu_usage || 0" />
            </div>
            <div class="status-item">
              <span class="label">内存使用率:</span>
              <el-progress :percentage="systemStatus.memory_usage || 0" />
            </div>
            <div class="status-item">
              <span class="label">磁盘使用率:</span>
              <el-progress :percentage="systemStatus.disk_usage || 0" />
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card title="实时日志">
          <template #header>
            <span>实时日志</span>
          </template>
          <div class="logs-container">
            <div v-for="log in recentLogs" :key="log.id" class="log-item">
              <span class="log-time">{{ formatTime(log.timestamp) }}</span>
              <span :class="['log-level', `log-${log.level}`]">{{ log.level }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useAdminStore } from '@/stores/admin'
import dayjs from 'dayjs'

const adminStore = useAdminStore()

const systemStatus = ref({
  cpu_usage: 0,
  memory_usage: 0,
  disk_usage: 0,
  uptime: 0,
  active_connections: 0
})

const recentLogs = ref<any[]>([])
const loading = ref(false)
let refreshInterval: NodeJS.Timeout | null = null

const formatTime = (time: Date | string) => {
  return dayjs(time).format('HH:mm:ss')
}

/**
 * 加载系统状态
 */
const loadSystemStatus = async () => {
  try {
    loading.value = true
    await adminStore.fetchSystemStatus()
    systemStatus.value = adminStore.systemStatus
  } catch (error) {
    console.error('加载系统状态失败:', error)
    // 使用模拟数据作为后备
    systemStatus.value = {
      cpu_usage: Math.floor(Math.random() * 100),
      memory_usage: Math.floor(Math.random() * 100),
      disk_usage: Math.floor(Math.random() * 100),
      uptime: Math.floor(Math.random() * 86400),
      active_connections: Math.floor(Math.random() * 100)
    }
  } finally {
    loading.value = false
  }
}

/**
 * 加载最近日志
 */
const loadRecentLogs = async () => {
  try {
    await adminStore.fetchLogs({ size: 10 })
    recentLogs.value = adminStore.logs
  } catch (error) {
    console.error('加载日志失败:', error)
    // 使用模拟数据作为后备
    recentLogs.value = [
      { id: 1, timestamp: new Date(), level: 'info', message: '系统启动完成' },
      { id: 2, timestamp: new Date(), level: 'warning', message: '内存使用率较高' },
      { id: 3, timestamp: new Date(), level: 'error', message: '数据库连接失败' }
    ]
  }
}

/**
 * 刷新数据
 */
const refreshData = async () => {
  await Promise.all([
    loadSystemStatus(),
    loadRecentLogs()
  ])
}

onMounted(async () => {
  await refreshData()

  // 每30秒刷新一次数据
  refreshInterval = setInterval(refreshData, 30000)
})

onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
})
</script>

<style scoped>
.system-monitor {
  padding: 0;
}

.status-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.label {
  min-width: 80px;
  font-weight: 500;
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  border-bottom: 1px solid #f0f0f0;
}

.log-time {
  font-size: 12px;
  color: #909399;
  min-width: 60px;
}

.log-level {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  min-width: 50px;
  text-align: center;
}

.log-info {
  background-color: #e1f3ff;
  color: #409eff;
}

.log-warning {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.log-error {
  background-color: #fef0f0;
  color: #f56c6c;
}

.log-message {
  flex: 1;
  font-size: 14px;
}
</style>
