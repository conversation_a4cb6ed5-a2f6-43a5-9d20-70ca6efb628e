{"types": {"problem": [], "suggestion": [], "layout": []}, "deprecated": [], "removed": [{"removed": "generator-star", "replacedBy": ["generator-star-spacing"]}, {"removed": "global-strict", "replacedBy": ["strict"]}, {"removed": "no-arrow-condition", "replacedBy": ["no-confusing-arrow", "no-constant-condition"]}, {"removed": "no-comma-dangle", "replacedBy": ["comma-dangle"]}, {"removed": "no-empty-class", "replacedBy": ["no-empty-character-class"]}, {"removed": "no-empty-label", "replacedBy": ["no-labels"]}, {"removed": "no-extra-strict", "replacedBy": ["strict"]}, {"removed": "no-reserved-keys", "replacedBy": ["quote-props"]}, {"removed": "no-space-before-semi", "replacedBy": ["semi-spacing"]}, {"removed": "no-wrap-func", "replacedBy": ["no-extra-parens"]}, {"removed": "space-after-function-name", "replacedBy": ["space-before-function-paren"]}, {"removed": "space-after-keywords", "replacedBy": ["keyword-spacing"]}, {"removed": "space-before-function-parentheses", "replacedBy": ["space-before-function-paren"]}, {"removed": "space-before-keywords", "replacedBy": ["keyword-spacing"]}, {"removed": "space-in-brackets", "replacedBy": ["object-curly-spacing", "array-bracket-spacing"]}, {"removed": "space-return-throw-case", "replacedBy": ["keyword-spacing"]}, {"removed": "space-unary-word-ops", "replacedBy": ["space-unary-ops"]}, {"removed": "spaced-line-comment", "replacedBy": ["spaced-comment"]}]}