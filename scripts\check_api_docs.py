#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统API文档完整性检查工具

功能：
1. 检查API文档是否包含所有模块
2. 验证接口描述的完整性
3. 对比实际API路由文件
"""

import re
import os
from pathlib import Path


def check_api_modules():
    """
    检查API模块文件
    
    Returns:
        list: API模块列表
    """
    api_dir = Path("a2a_backend/app/api/v1")
    if not api_dir.exists():
        return []
    
    modules = []
    for file in api_dir.glob("*.py"):
        if file.name != "__init__.py":
            modules.append(file.stem)
    
    return sorted(modules)


def check_doc_sections():
    """
    检查文档章节
    
    Returns:
        list: 文档章节列表
    """
    doc_file = Path("docs/API接口文档.md")
    if not doc_file.exists():
        return []
    
    with open(doc_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找所有主要章节
    sections = re.findall(r'^## ([^#\n]+)', content, re.MULTILINE)
    
    return sections


def check_interface_completeness():
    """
    检查接口完整性
    
    Returns:
        dict: 检查结果
    """
    doc_file = Path("docs/API接口文档.md")
    if not doc_file.exists():
        return {"error": "API文档文件不存在"}
    
    with open(doc_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    results = {
        "total_sections": 0,
        "interface_sections": 0,
        "total_interfaces": 0,
        "sections": [],
        "missing_modules": [],
        "issues": []
    }
    
    # 统计章节
    sections = re.findall(r'^## ([^#\n]+)', content, re.MULTILINE)
    results["total_sections"] = len(sections)
    results["sections"] = sections
    
    # 统计接口章节
    interface_sections = [s for s in sections if "接口" in s and s not in ["概述", "目录", "通用响应格式", "错误代码说明", "使用示例", "注意事项"]]
    results["interface_sections"] = len(interface_sections)
    
    # 统计具体接口
    interfaces = re.findall(r'^### \d+\. ([^#\n]+)', content, re.MULTILINE)
    results["total_interfaces"] = len(interfaces)
    
    # 检查必需的接口模块
    required_modules = [
        "认证接口", "用户管理接口", "智能体管理接口", "会话管理接口", 
        "消息管理接口", "任务管理接口", "工作流管理接口", "流式输出接口",
        "WebSocket接口", "配置管理接口", "工件管理接口", "监控管理接口"
    ]
    
    for module in required_modules:
        if module not in sections:
            results["missing_modules"].append(module)
    
    # 检查具体问题
    if "## 认证接口" not in content:
        results["issues"].append("缺少认证接口章节")
    
    if "POST /auth/login" not in content:
        results["issues"].append("缺少登录接口")
    
    if "POST /agents" not in content:
        results["issues"].append("缺少创建智能体接口")
    
    if "WebSocket" not in content:
        results["issues"].append("缺少WebSocket接口说明")
    
    # 检查响应格式示例
    if "```json" not in content:
        results["issues"].append("缺少JSON格式示例")
    
    # 检查错误代码
    if "错误代码" not in content:
        results["issues"].append("缺少错误代码说明")
    
    # 检查使用示例
    if "Python示例" not in content:
        results["issues"].append("缺少Python使用示例")
    
    return results


def check_missing_interfaces():
    """
    检查缺失的接口
    
    Returns:
        dict: 缺失的接口信息
    """
    doc_file = Path("docs/API接口文档.md")
    if not doc_file.exists():
        return {"error": "API文档文件不存在"}
    
    with open(doc_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 应该包含的重要接口
    important_interfaces = {
        "认证接口": [
            "POST /auth/register", "POST /auth/login", "POST /auth/refresh",
            "POST /auth/logout", "GET /auth/verify"
        ],
        "用户管理接口": [
            "GET /users/me", "PUT /users/me", "GET /users",
            "GET /users/{user_id}", "PUT /users/{user_id}/role"
        ],
        "智能体管理接口": [
            "POST /agents", "GET /agents", "GET /agents/{agent_id}",
            "PUT /agents/{agent_id}", "DELETE /agents/{agent_id}",
            "POST /agents/{agent_id}/execute"
        ],
        "会话管理接口": [
            "POST /sessions", "GET /sessions", "GET /sessions/{session_id}",
            "PUT /sessions/{session_id}", "DELETE /sessions/{session_id}"
        ],
        "任务管理接口": [
            "POST /tasks", "GET /tasks", "GET /tasks/{task_id}",
            "POST /tasks/{task_id}/execute", "POST /tasks/{task_id}/cancel"
        ],
        "工作流管理接口": [
            "POST /workflows", "GET /workflows", "GET /workflows/{workflow_id}",
            "POST /workflows/{workflow_id}/execute"
        ],
        "配置管理接口": [
            "GET /configs/system", "PUT /configs/system",
            "GET /configs/user", "PUT /configs/user"
        ],
        "工件管理接口": [
            "POST /artifacts", "GET /artifacts", "GET /artifacts/{artifact_id}",
            "GET /artifacts/{artifact_id}/download", "DELETE /artifacts/{artifact_id}"
        ],
        "监控管理接口": [
            "GET /monitoring/status", "GET /monitoring/metrics",
            "GET /monitoring/performance", "GET /monitoring/errors"
        ]
    }
    
    missing = {}
    for section, interfaces in important_interfaces.items():
        missing_interfaces = []
        for interface in interfaces:
            if interface not in content:
                missing_interfaces.append(interface)
        
        if missing_interfaces:
            missing[section] = missing_interfaces
    
    return missing


def print_check_results():
    """
    打印检查结果
    """
    print("=" * 60)
    print("A2A API文档完整性检查")
    print("=" * 60)
    
    # 检查API模块
    print("📁 API模块文件:")
    modules = check_api_modules()
    if modules:
        for module in modules:
            print(f"  ✅ {module}.py")
    else:
        print("  ❌ 未找到API模块文件")
    print()
    
    # 检查文档章节
    print("📖 文档章节:")
    sections = check_doc_sections()
    if sections:
        for section in sections:
            print(f"  📄 {section}")
    else:
        print("  ❌ 未找到文档章节")
    print()
    
    # 检查接口完整性
    print("🔍 接口完整性检查:")
    completeness = check_interface_completeness()
    
    if "error" in completeness:
        print(f"  ❌ {completeness['error']}")
        return
    
    print(f"  📊 总章节数: {completeness['total_sections']}")
    print(f"  🔗 接口章节数: {completeness['interface_sections']}")
    print(f"  📝 具体接口数: {completeness['total_interfaces']}")
    
    if completeness['missing_modules']:
        print("  ❌ 缺失的模块:")
        for module in completeness['missing_modules']:
            print(f"    - {module}")
    else:
        print("  ✅ 所有主要模块都已包含")
    
    if completeness['issues']:
        print("  ⚠️ 发现的问题:")
        for issue in completeness['issues']:
            print(f"    - {issue}")
    else:
        print("  ✅ 未发现明显问题")
    print()
    
    # 检查缺失的接口
    print("🔍 缺失接口检查:")
    missing = check_missing_interfaces()
    
    if "error" in missing:
        print(f"  ❌ {missing['error']}")
        return
    
    if missing:
        print("  ❌ 发现缺失的接口:")
        for section, interfaces in missing.items():
            print(f"    📂 {section}:")
            for interface in interfaces:
                print(f"      - {interface}")
    else:
        print("  ✅ 所有重要接口都已包含")
    print()
    
    # 总结
    total_issues = len(completeness.get('issues', [])) + len(completeness.get('missing_modules', [])) + len(missing)
    
    if total_issues == 0:
        print("🎉 API文档检查通过！")
        print("✅ 文档结构完整")
        print("✅ 包含所有主要接口")
        print("✅ 格式规范正确")
    else:
        print(f"⚠️ 发现 {total_issues} 个问题需要修复")
        print("建议补充缺失的接口和章节")


if __name__ == "__main__":
    print_check_results()
