{"version": 3, "file": "tree-node.mjs", "sources": ["../../../../../../packages/components/tree-v2/src/tree-node.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"node$\"\n    :class=\"[\n      ns.b('node'),\n      ns.is('expanded', expanded),\n      ns.is('current', current),\n      ns.is('focusable', !disabled),\n      ns.is('checked', !disabled && checked),\n      getNodeClass(node),\n    ]\"\n    role=\"treeitem\"\n    tabindex=\"-1\"\n    :aria-expanded=\"expanded\"\n    :aria-disabled=\"disabled\"\n    :aria-checked=\"checked\"\n    :data-key=\"node?.key\"\n    @click.stop=\"handleClick\"\n    @contextmenu=\"handleContextMenu\"\n    @dragover.prevent\n    @dragenter.prevent\n    @drop.stop=\"handleDrop\"\n  >\n    <div\n      :class=\"ns.be('node', 'content')\"\n      :style=\"{\n        paddingLeft: `${(node.level - 1) * indent}px`,\n        height: itemSize + 'px',\n      }\"\n    >\n      <el-icon\n        v-if=\"icon\"\n        :class=\"[\n          ns.is('leaf', !!node?.isLeaf),\n          ns.is('hidden', hiddenExpandIcon),\n          {\n            expanded: !node?.isLeaf && expanded,\n          },\n          ns.be('node', 'expand-icon'),\n        ]\"\n        @click.stop=\"handleExpandIconClick\"\n      >\n        <component :is=\"icon\" />\n      </el-icon>\n      <el-checkbox\n        v-if=\"showCheckbox\"\n        :model-value=\"checked\"\n        :indeterminate=\"indeterminate\"\n        :disabled=\"disabled\"\n        @change=\"handleCheckChange\"\n        @click.stop\n      />\n      <el-node-content :node=\"node\" />\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject } from 'vue'\nimport ElIcon from '@element-plus/components/icon'\nimport { CaretRight } from '@element-plus/icons-vue'\nimport ElCheckbox from '@element-plus/components/checkbox'\nimport { useNamespace } from '@element-plus/hooks'\nimport { isFunction, isString } from '@element-plus/utils'\nimport ElNodeContent from './tree-node-content'\nimport {\n  NODE_CONTEXTMENU,\n  ROOT_TREE_INJECTION_KEY,\n  treeNodeEmits,\n  treeNodeProps,\n} from './virtual-tree'\nimport type { CheckboxValueType } from '@element-plus/components/checkbox'\nimport type { TreeNode } from './types'\n\ndefineOptions({\n  name: 'ElTreeNode',\n})\n\nconst props = defineProps(treeNodeProps)\nconst emit = defineEmits(treeNodeEmits)\n\nconst tree = inject(ROOT_TREE_INJECTION_KEY)\nconst ns = useNamespace('tree')\n\nconst indent = computed(() => tree?.props.indent ?? 16)\nconst icon = computed(() => tree?.props.icon ?? CaretRight)\n\nconst getNodeClass = (node: TreeNode) => {\n  const nodeClassFunc = tree?.props.props.class\n  if (!nodeClassFunc) return {}\n\n  let className\n  if (isFunction(nodeClassFunc)) {\n    const { data } = node\n    className = nodeClassFunc(data, node)\n  } else {\n    className = nodeClassFunc\n  }\n\n  return isString(className) ? { [className]: true } : className\n}\n\nconst handleClick = (e: MouseEvent) => {\n  emit('click', props.node, e)\n}\nconst handleDrop = (e: DragEvent) => {\n  emit('drop', props.node, e)\n}\nconst handleExpandIconClick = () => {\n  emit('toggle', props.node)\n}\nconst handleCheckChange = (value: CheckboxValueType) => {\n  emit('check', props.node, value)\n}\n\nconst handleContextMenu = (event: Event) => {\n  if (tree?.instance?.vnode?.props?.['onNodeContextmenu']) {\n    event.stopPropagation()\n    event.preventDefault()\n  }\n  tree?.ctx.emit(NODE_CONTEXTMENU, event, props.node?.data, props.node)\n}\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;mCA0Ec,CAAA;AAAA,EACZ,IAAM,EAAA,YAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,IAAA,GAAO,OAAO,uBAAuB,CAAA,CAAA;AAC3C,IAAM,MAAA,EAAA,GAAK,aAAa,MAAM,CAAA,CAAA;AAE9B,IAAA,MAAM,SAAS,QAAS,CAAA,MAAM;AAC9B,MAAA,IAAM;AAEN,MAAM,OAAA,CAAA,EAAA,GAAA,IAAA,IAAmC,IAAA,GAAA,KAAA,CAAA,GAAA,IAAA,CAAA,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,EAAA,GAAA,EAAA,CAAA;AACvC,KAAM,CAAA,CAAA;AACN,IAAI,MAAA,IAAgB,GAAA,QAAA,CAAA,MAAA;AAEpB,MAAI,IAAA,EAAA,CAAA;AACJ,MAAI,OAAA,CAAA,EAAA,GAAA,eAAwB,KAAG,CAAA,GAAA,IAAA,CAAA,KAAA,CAAA,IAAA,KAAA,IAAA,GAAA,EAAA,GAAA,UAAA,CAAA;AAC7B,KAAM,CAAA,CAAA;AACN,IAAY,MAAA,YAAA,GAAA,CAAA,IAAA;AAAwB,MACtC,MAAO,aAAA,GAAA,IAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,IAAA,CAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA;AACL,MAAY,IAAA,CAAA,aAAA;AAAA,QACd,OAAA,EAAA,CAAA;AAEA,MAAO,IAAA,SAAA,CAAA;AAA8C,MACvD,IAAA,UAAA,CAAA,aAAA,CAAA,EAAA;AAEA,QAAM,MAAA,EAAA,IAAA,EAAA,GAAe,IAAkB,CAAA;AACrC,QAAK,SAAA,GAAS,aAAa,CAAA,IAAA,EAAA,IAAA,CAAA,CAAA;AAAA,OAC7B,MAAA;AACA,QAAM,SAAA,GAAA,aAA+B,CAAA;AACnC,OAAK;AAAqB,MAC5B,OAAA,QAAA,CAAA,SAAA,CAAA,GAAA,EAAA,CAAA,SAAA,GAAA,IAAA,EAAA,GAAA,SAAA,CAAA;AACA,KAAA,CAAA;AACE,IAAK,MAAA,WAAU,UAAU;AAAA,MAC3B,IAAA,CAAA,OAAA,EAAA,KAAA,CAAA,IAAA,EAAA,CAAA,CAAA,CAAA;AACA,KAAM,CAAA;AACJ,IAAK,MAAA,UAAS,GAAM,CAAA,CAAA,KAAA;AAAW,MACjC,IAAA,CAAA,MAAA,EAAA,KAAA,CAAA,IAAA,EAAA,CAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAA,MAAI,qBAAuB,GAAA,MAAA;AACzB,MAAA,IAAA,CAAA,QAAsB,EAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AACtB,KAAA,CAAA;AAAqB,IACvB,MAAA,iBAAA,GAAA,CAAA,KAAA,KAAA;AACA,MAAM,IAAA,CAAA,cAA2B,CAAA,IAAA,EAAA,KAAA,CAAA,CAAA;AAAmC,KACtE,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}