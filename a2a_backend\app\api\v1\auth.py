# -*- coding: utf-8 -*-
"""
A2A多智能体系统认证API接口

提供用户注册、登录、令牌刷新、登出和密码重置功能
"""

from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Request, Response
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr, Field, validator
from loguru import logger

from app.services.user_service import UserService
from app.services.auth_service import AuthService
from app.core.config import get_settings
from app.core.rate_limiter import RateLimiter
from app.core.security import get_client_ip


# 创建路由器
router = APIRouter(prefix="/auth", tags=["认证"])

# 安全依赖
security = HTTPBearer()

# 服务实例
user_service = UserService()
auth_service = AuthService()
settings = get_settings()
rate_limiter = RateLimiter()


# 请求模型
class UserRegisterRequest(BaseModel):
    """用户注册请求模型"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="邮箱地址")
    password: str = Field(..., min_length=8, max_length=128, description="密码")
    full_name: Optional[str] = Field(None, max_length=100, description="全名")
    phone: Optional[str] = Field(None, max_length=20, description="手机号")
    invite_code: Optional[str] = Field(None, description="邀请码")
    
    @validator('username')
    def validate_username(cls, v):
        """验证用户名格式"""
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('用户名只能包含字母、数字、下划线和连字符')
        return v
    
    @validator('password')
    def validate_password(cls, v):
        """验证密码强度"""
        if not any(c.isupper() for c in v):
            raise ValueError('密码必须包含至少一个大写字母')
        if not any(c.islower() for c in v):
            raise ValueError('密码必须包含至少一个小写字母')
        if not any(c.isdigit() for c in v):
            raise ValueError('密码必须包含至少一个数字')
        if not any(c in '!@#$%^&*()_+-=[]{}|;:,.<>?' for c in v):
            raise ValueError('密码必须包含至少一个特殊字符')
        return v


class UserLoginRequest(BaseModel):
    """用户登录请求模型"""
    username: str = Field(..., description="用户名或邮箱")
    password: str = Field(..., description="密码")
    remember_me: bool = Field(False, description="记住我")
    device_info: Optional[Dict[str, Any]] = Field(None, description="设备信息")


class TokenRefreshRequest(BaseModel):
    """令牌刷新请求模型"""
    refresh_token: str = Field(..., description="刷新令牌")


class PasswordResetRequest(BaseModel):
    """密码重置请求模型"""
    email: EmailStr = Field(..., description="邮箱地址")


class PasswordResetConfirmRequest(BaseModel):
    """密码重置确认请求模型"""
    token: str = Field(..., description="重置令牌")
    new_password: str = Field(..., min_length=8, max_length=128, description="新密码")
    
    @validator('new_password')
    def validate_password(cls, v):
        """验证密码强度"""
        if not any(c.isupper() for c in v):
            raise ValueError('密码必须包含至少一个大写字母')
        if not any(c.islower() for c in v):
            raise ValueError('密码必须包含至少一个小写字母')
        if not any(c.isdigit() for c in v):
            raise ValueError('密码必须包含至少一个数字')
        if not any(c in '!@#$%^&*()_+-=[]{}|;:,.<>?' for c in v):
            raise ValueError('密码必须包含至少一个特殊字符')
        return v


class ChangePasswordRequest(BaseModel):
    """修改密码请求模型"""
    current_password: str = Field(..., description="当前密码")
    new_password: str = Field(..., min_length=8, max_length=128, description="新密码")
    
    @validator('new_password')
    def validate_password(cls, v):
        """验证密码强度"""
        if not any(c.isupper() for c in v):
            raise ValueError('密码必须包含至少一个大写字母')
        if not any(c.islower() for c in v):
            raise ValueError('密码必须包含至少一个小写字母')
        if not any(c.isdigit() for c in v):
            raise ValueError('密码必须包含至少一个数字')
        if not any(c in '!@#$%^&*()_+-=[]{}|;:,.<>?' for c in v):
            raise ValueError('密码必须包含至少一个特殊字符')
        return v


# 响应模型
class TokenResponse(BaseModel):
    """令牌响应模型"""
    access_token: str = Field(..., description="访问令牌")
    refresh_token: str = Field(..., description="刷新令牌")
    token_type: str = Field("bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间（秒）")
    user_info: Dict[str, Any] = Field(..., description="用户信息")


class MessageResponse(BaseModel):
    """消息响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")


# 依赖函数
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """获取当前用户"""
    try:
        token = credentials.credentials
        user_info = await auth_service.verify_token(token)
        return user_info
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的访问令牌",
            headers={"WWW-Authenticate": "Bearer"}
        )


# API接口
@router.post("/register", response_model=MessageResponse, summary="用户注册")
async def register_user(
    request: UserRegisterRequest,
    http_request: Request,
    response: Response
):
    """
    用户注册
    
    - **username**: 用户名（3-50字符，只能包含字母、数字、下划线和连字符）
    - **email**: 邮箱地址
    - **password**: 密码（8-128字符，必须包含大小写字母、数字和特殊字符）
    - **full_name**: 全名（可选）
    - **phone**: 手机号（可选）
    - **invite_code**: 邀请码（可选）
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 速率限制检查
        rate_limiter.check_rate_limit(
            client_id=f"register:{client_ip}",
            limit=5  # 每分钟最多5次注册尝试
        )
        
        # 准备用户数据
        user_data = {
            "username": request.username,
            "email": request.email,
            "password": request.password,
            "full_name": request.full_name,
            "phone": request.phone,
            "role": "user",  # 默认角色
            "is_verified": False,  # 需要邮箱验证
            "registration_ip": client_ip,
            "invite_code": request.invite_code
        }
        
        # 创建用户
        result = await user_service.create_user(user_data)
        
        if result["success"]:
            # 设置响应头
            response.headers["X-Registration-Success"] = "true"
            
            logger.info(f"用户注册成功: {request.username}", extra={
                "username": request.username,
                "email": request.email,
                "client_ip": client_ip
            })
            
            return MessageResponse(
                success=True,
                message="注册成功，请查收邮箱验证邮件",
                data={
                    "user_id": result["data"]["user_id"],
                    "username": request.username,
                    "email": request.email,
                    "verification_required": True
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except ValueError as e:
        logger.warning(f"用户注册失败: {str(e)}", extra={
            "username": request.username,
            "client_ip": get_client_ip(http_request)
        })
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"用户注册异常: {str(e)}", extra={
            "username": request.username,
            "client_ip": get_client_ip(http_request)
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="注册失败，请稍后重试"
        )


@router.post("/login", response_model=TokenResponse, summary="用户登录")
async def login_user(
    request: UserLoginRequest,
    http_request: Request,
    response: Response
):
    """
    用户登录
    
    - **username**: 用户名或邮箱
    - **password**: 密码
    - **remember_me**: 是否记住登录状态
    - **device_info**: 设备信息（可选）
    """
    try:
        # 调试日志：打印接收到的登录参数
        logger.info(f"登录请求参数 - 用户名: {request.username}, 密码: {request.password}, 记住我: {request.remember_me}")
        
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 速率限制检查
        rate_limiter.check_rate_limit(
            client_id=f"login:{client_ip}",
            limit=10  # 每分钟最多10次登录尝试
        )
        
        # 用户认证
        result = await user_service.authenticate_user(
            username=request.username,
            password=request.password,
            ip_address=client_ip,
            device_info=request.device_info,
            user_agent=http_request.headers.get("User-Agent", "")
        )
        
        if result["success"]:
            data = result["data"]
            user_info = data["user_info"]

            # 设置响应头
            response.headers["X-Login-Success"] = "true"
            response.headers["X-User-ID"] = str(user_info["user_id"])

            # 如果是记住我，设置更长的过期时间
            if request.remember_me:
                response.set_cookie(
                    key="remember_token",
                    value=data["refresh_token"],
                    max_age=30 * 24 * 3600,  # 30天
                    httponly=True,
                    secure=True,
                    samesite="strict"
                )

            logger.info(f"用户登录成功: {request.username}", extra={
                "user_id": user_info["user_id"],
                "username": request.username,
                "client_ip": client_ip
            })

            return TokenResponse(
                access_token=data["access_token"],
                refresh_token=data["refresh_token"],
                token_type="bearer",
                expires_in=data["expires_in"],
                user_info=user_info
            )
        else:
            # 登录失败，记录失败尝试
            # 注意：当前RateLimiter实现会在check_rate_limit时自动记录请求
            # 这里可以选择重置限制或者让自然的速率限制生效
            
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=result["message"]
            )
            
    except ValueError as e:
        logger.warning(f"用户登录失败: {str(e)}", extra={
            "username": request.username,
            "client_ip": get_client_ip(http_request)
        })
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"用户登录异常: {str(e)}", extra={
            "username": request.username,
            "client_ip": get_client_ip(http_request)
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录失败，请稍后重试"
        )


@router.post("/refresh", response_model=TokenResponse, summary="刷新令牌")
async def refresh_token(
    request: TokenRefreshRequest,
    http_request: Request
):
    """
    刷新访问令牌
    
    - **refresh_token**: 刷新令牌
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 速率限制检查
        rate_limiter.check_rate_limit(
            client_id=f"refresh:{client_ip}",
            limit=20  # 每分钟最多20次刷新
        )
        
        # 刷新令牌
        result = await auth_service.refresh_token(request.refresh_token, client_ip)
        
        if result["success"]:
            token_data = result["data"]
            
            logger.info(f"令牌刷新成功", extra={
                "user_id": token_data["user_id"],
                "client_ip": client_ip
            })
            
            return TokenResponse(
                access_token=token_data["access_token"],
                refresh_token=token_data["refresh_token"],
                token_type="bearer",
                expires_in=token_data["expires_in"],
                user_info=token_data["user_info"]
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=result["message"]
            )
            
    except ValueError as e:
        logger.warning(f"令牌刷新失败: {str(e)}", extra={
            "client_ip": get_client_ip(http_request)
        })
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"令牌刷新异常: {str(e)}", extra={
            "client_ip": get_client_ip(http_request)
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="令牌刷新失败，请重新登录"
        )


@router.post("/logout", response_model=MessageResponse, summary="用户登出")
async def logout_user(
    http_request: Request,
    response: Response,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    用户登出
    
    需要提供有效的访问令牌
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 获取令牌
        authorization = http_request.headers.get("Authorization", "")
        if authorization.startswith("Bearer "):
            token = authorization[7:]
        else:
            raise ValueError("无效的令牌格式")
        
        # 用户登出
        result = await auth_service.logout_user(current_user["user_id"], token, client_ip)
        
        if result["success"]:
            # 清除记住我的Cookie
            response.delete_cookie("remember_token")
            
            # 设置响应头
            response.headers["X-Logout-Success"] = "true"
            
            logger.info(f"用户登出成功", extra={
                "user_id": current_user["user_id"],
                "username": current_user["username"],
                "client_ip": client_ip
            })
            
            return MessageResponse(
                success=True,
                message="登出成功"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except ValueError as e:
        logger.warning(f"用户登出失败: {str(e)}", extra={
            "user_id": current_user.get("user_id"),
            "client_ip": get_client_ip(http_request)
        })
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"用户登出异常: {str(e)}", extra={
            "user_id": current_user.get("user_id"),
            "client_ip": get_client_ip(http_request)
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登出失败，请稍后重试"
        )


@router.post("/password/reset", response_model=MessageResponse, summary="请求密码重置")
async def request_password_reset(
    request: PasswordResetRequest,
    http_request: Request
):
    """
    请求密码重置
    
    - **email**: 邮箱地址
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 速率限制检查
        rate_limiter.check_rate_limit(
            client_id=f"password_reset:{client_ip}",
            limit=3  # 每分钟最多3次密码重置请求
        )
        
        # 请求密码重置
        result = await auth_service.request_password_reset(request.email, client_ip)
        
        # 无论邮箱是否存在，都返回成功消息（安全考虑）
        logger.info(f"密码重置请求", extra={
            "email": request.email,
            "client_ip": client_ip
        })
        
        return MessageResponse(
            success=True,
            message="如果邮箱存在，重置链接已发送到您的邮箱",
            data={
                "email": request.email,
                "expires_in": 3600  # 1小时过期
            }
        )
        
    except Exception as e:
        logger.error(f"密码重置请求异常: {str(e)}", extra={
            "email": request.email,
            "client_ip": get_client_ip(http_request)
        })
        # 即使出错也返回成功消息（安全考虑）
        return MessageResponse(
            success=True,
            message="如果邮箱存在，重置链接已发送到您的邮箱"
        )


@router.post("/password/reset/confirm", response_model=MessageResponse, summary="确认密码重置")
async def confirm_password_reset(
    request: PasswordResetConfirmRequest,
    http_request: Request
):
    """
    确认密码重置
    
    - **token**: 重置令牌
    - **new_password**: 新密码（8-128字符，必须包含大小写字母、数字和特殊字符）
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 速率限制检查
        rate_limiter.check_rate_limit(
            client_id=f"password_reset_confirm:{client_ip}",
            limit=5  # 每分钟最多5次确认尝试
        )
        
        # 确认密码重置
        result = await auth_service.confirm_password_reset(
            request.token,
            request.new_password,
            client_ip
        )
        
        if result["success"]:
            logger.info(f"密码重置成功", extra={
                "user_id": result["data"]["user_id"],
                "client_ip": client_ip
            })
            
            return MessageResponse(
                success=True,
                message="密码重置成功，请使用新密码登录",
                data={
                    "user_id": result["data"]["user_id"],
                    "reset_at": datetime.utcnow().isoformat()
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except ValueError as e:
        logger.warning(f"密码重置确认失败: {str(e)}", extra={
            "client_ip": get_client_ip(http_request)
        })
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"密码重置确认异常: {str(e)}", extra={
            "client_ip": get_client_ip(http_request)
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="密码重置失败，请稍后重试"
        )


@router.post("/password/change", response_model=MessageResponse, summary="修改密码")
async def change_password(
    request: ChangePasswordRequest,
    http_request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    修改密码
    
    - **current_password**: 当前密码
    - **new_password**: 新密码（8-128字符，必须包含大小写字母、数字和特殊字符）
    
    需要提供有效的访问令牌
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 速率限制检查
        rate_limiter.check_rate_limit(
            client_id=f"password_change:{current_user['user_id']}",
            limit=5  # 每分钟最多5次修改密码尝试
        )
        
        # 修改密码
        result = await user_service.change_password(
            current_user["user_id"],
            request.current_password,
            request.new_password,
            client_ip
        )
        
        if result["success"]:
            logger.info(f"密码修改成功", extra={
                "user_id": current_user["user_id"],
                "username": current_user["username"],
                "client_ip": client_ip
            })
            
            return MessageResponse(
                success=True,
                message="密码修改成功",
                data={
                    "user_id": current_user["user_id"],
                    "changed_at": datetime.utcnow().isoformat()
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except ValueError as e:
        logger.warning(f"密码修改失败: {str(e)}", extra={
            "user_id": current_user["user_id"],
            "client_ip": get_client_ip(http_request)
        })
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"密码修改异常: {str(e)}", extra={
            "user_id": current_user["user_id"],
            "client_ip": get_client_ip(http_request)
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="密码修改失败，请稍后重试"
        )


@router.get("/verify", response_model=MessageResponse, summary="验证令牌")
async def verify_token(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    验证访问令牌
    
    需要提供有效的访问令牌
    """
    try:
        return MessageResponse(
            success=True,
            message="令牌有效",
            data={
                "user_id": current_user["user_id"],
                "username": current_user["username"],
                "email": current_user["email"],
                "role": current_user["role"],
                "is_verified": current_user["is_verified"],
                "verified_at": datetime.utcnow().isoformat()
            }
        )
        
    except Exception as e:
        logger.error(f"令牌验证异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="令牌验证失败"
        )


@router.get("/sessions", response_model=Dict[str, Any], summary="获取活跃会话")
async def get_active_sessions(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取用户的活跃会话列表
    
    需要提供有效的访问令牌
    """
    try:
        # 获取活跃会话
        sessions = await auth_service.get_user_active_sessions(current_user["user_id"])
        
        return {
            "success": True,
            "message": "获取活跃会话成功",
            "data": {
                "user_id": current_user["user_id"],
                "sessions": sessions,
                "total_sessions": len(sessions)
            }
        }
        
    except Exception as e:
        logger.error(f"获取活跃会话异常: {str(e)}", extra={
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取活跃会话失败"
        )


@router.delete("/sessions/{session_id}", response_model=MessageResponse, summary="撤销会话")
async def revoke_session(
    session_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    撤销指定会话
    
    - **session_id**: 会话ID
    
    需要提供有效的访问令牌
    """
    try:
        # 撤销会话
        result = await auth_service.revoke_user_session(current_user["user_id"], session_id)
        
        if result["success"]:
            logger.info(f"会话撤销成功", extra={
                "user_id": current_user["user_id"],
                "session_id": session_id
            })
            
            return MessageResponse(
                success=True,
                message="会话撤销成功",
                data={
                    "session_id": session_id,
                    "revoked_at": datetime.utcnow().isoformat()
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except ValueError as e:
        logger.warning(f"会话撤销失败: {str(e)}", extra={
            "user_id": current_user["user_id"],
            "session_id": session_id
        })
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"会话撤销异常: {str(e)}", extra={
            "user_id": current_user["user_id"],
            "session_id": session_id
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="会话撤销失败，请稍后重试"
        )