{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/affix/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Affix from './src/affix.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElAffix: SFCWithInstall<typeof Affix> = withInstall(Affix)\nexport default ElAffix\n\nexport * from './src/affix'\n"], "names": ["withInstall", "Affix"], "mappings": ";;;;;;;;AAEY,MAAC,OAAO,GAAGA,mBAAW,CAACC,kBAAK;;;;;;;"}