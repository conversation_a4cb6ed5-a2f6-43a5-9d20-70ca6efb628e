mkdir docs
mkdir scripts
conda activate a2a_backend; python -c "`
import re`
`
# 读取文件`
with open('scripts/db.sql', 'r', encoding='utf-8') as f:`
    content = f.read()`
`
# 替换BOOLEAN为TINYINT(1)`
content = re.sub(r'BOOLEAN NOT NULL DEFAULT FALSE', 'TINYINT(1) NOT NULL DEFAULT 0', content)`
content = re.sub(r'BOOLEAN NOT NULL DEFAULT TRUE', 'TINYINT(1) NOT NULL DEFAULT 1', content)`
content = re.sub(r'BOOLEAN DEFAULT NULL', 'TINYINT(1) DEFAULT NULL', content)`
content = re.sub(r'BOOLEAN NOT NULL', 'TINYINT(1) NOT NULL', content)`
`
# 替换DATETIME为TIMESTAMP（对于created_at和updated_at字段）`
content = re.sub(r'created_at\` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP', 'created_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP', content)`
content = re.sub(r'updated_at\` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP', 'updated_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP', content)`
`
# 写回文件`
with open('scripts/db.sql', 'w', encoding='utf-8') as f:`
    f.write(content)`
`
print('MySQL 5.7兼容性修复完成')`
"
conda activate a2a_backend; python -c "`
import re`
import os`
`
# 修改工作目录`
os.chdir('..')`
`
# 读取文件`
with open('scripts/db.sql', 'r', encoding='utf-8') as f:`
    content = f.read()`
`
# 替换BOOLEAN为TINYINT(1)`
content = re.sub(r'BOOLEAN NOT NULL DEFAULT FALSE', 'TINYINT(1) NOT NULL DEFAULT 0', content)`
content = re.sub(r'BOOLEAN NOT NULL DEFAULT TRUE', 'TINYINT(1) NOT NULL DEFAULT 1', content)`
content = re.sub(r'BOOLEAN DEFAULT NULL', 'TINYINT(1) DEFAULT NULL', content)`
content = re.sub(r'BOOLEAN NOT NULL', 'TINYINT(1) NOT NULL', content)`
`
# 替换DATETIME为TIMESTAMP（对于created_at和updated_at字段）`
content = re.sub(r'created_at\` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP', 'created_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP', content)`
content = re.sub(r'updated_at\` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP', 'updated_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP', content)`
`
# 写回文件`
with open('scripts/db.sql', 'w', encoding='utf-8') as f:`
    f.write(content)`
`
print('MySQL 5.7兼容性修复完成')`
"
conda activate a2a_backend; cd .. && python scripts/validate_db.py
conda activate a2a_backend; cd ..; python scripts/validate_db.py
conda activate a2a_backend; python scripts/check_api_docs.py
python main.py
