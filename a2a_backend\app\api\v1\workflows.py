# -*- coding: utf-8 -*-
"""
A2A多智能体系统工作流API接口

提供工作流定义管理、执行、状态查询和性能分析接口
"""

import logging
import json
import uuid
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path, Body
from fastapi.responses import StreamingResponse
from sqlalchemy import and_, or_, desc, func

from app.core.database import get_db
from app.core.logging import get_logger
from app.auth.dependencies import get_current_user
from app.models.user import User
from app.models.workflow import Workflow, WorkflowExecution
from app.schemas.workflow import (
    WorkflowCreate, WorkflowUpdate, WorkflowResponse,
    WorkflowExecutionCreate, WorkflowExecutionResponse,
    WorkflowStatus, WorkflowExecutionStatus, WorkflowType,
    WorkflowPriority, WorkflowNodeType
)
from app.services.workflow_service import WorkflowService
from app.workflow.engine import WorkflowEngine
from app.workflow.scheduler import WorkflowScheduler, TaskPriority
from app.workflow.context_manager import WorkflowContextManager

# 创建路由器
router = APIRouter(prefix="/workflows", tags=["工作流"])
logger = get_logger("workflow_api")

# 全局服务实例（在实际应用中应该通过依赖注入管理）
workflow_service: Optional[WorkflowService] = None
workflow_engine: Optional[WorkflowEngine] = None
workflow_scheduler: Optional[WorkflowScheduler] = None
context_manager: Optional[WorkflowContextManager] = None


def get_workflow_service(db = Depends(get_db)):
    """获取工作流服务实例"""
    global workflow_service
    if workflow_service is None:
        workflow_service = WorkflowService(db)
    return workflow_service


def get_workflow_engine(db = Depends(get_db)):
    """获取工作流引擎实例"""
    global workflow_engine
    if workflow_engine is None:
        workflow_engine = WorkflowEngine(db)
    return workflow_engine


def get_workflow_scheduler(db = Depends(get_db)):
    """获取工作流调度器实例"""
    global workflow_scheduler
    if workflow_scheduler is None:
        workflow_scheduler = WorkflowScheduler(db)
    return workflow_scheduler


def get_context_manager(db = Depends(get_db)):
    """获取上下文管理器实例"""
    global context_manager
    if context_manager is None:
        context_manager = WorkflowContextManager(db)
    return context_manager


@router.post("/", response_model=WorkflowResponse, status_code=status.HTTP_201_CREATED)
async def create_workflow(
    workflow_data: WorkflowCreate,
    current_user: User = Depends(get_current_user),
    service: WorkflowService = Depends(get_workflow_service)
) -> WorkflowResponse:
    """
    创建新的工作流
    
    Args:
        workflow_data: 工作流创建数据
        current_user: 当前用户
        service: 工作流服务
        
    Returns:
        WorkflowResponse: 创建的工作流信息
        
    Raises:
        HTTPException: 创建失败时抛出异常
    """
    try:
        logger.info(f"用户 {current_user.username} 创建工作流: {workflow_data.name}")
        
        workflow = await service.create_workflow(
            user_id=current_user.id,
            workflow_data=workflow_data
        )
        
        if not workflow:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="工作流创建失败"
            )
        
        return WorkflowResponse.from_orm(workflow)
    except Exception as e:
        logger.error(f"创建工作流错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建工作流失败: {str(e)}"
        )


@router.get("/", response_model=List[WorkflowResponse])
async def list_workflows(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    workflow_type: Optional[WorkflowType] = Query(None, description="工作流类型过滤"),
    status: Optional[WorkflowStatus] = Query(None, description="状态过滤"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    current_user: User = Depends(get_current_user),
    service: WorkflowService = Depends(get_workflow_service)
) -> List[WorkflowResponse]:
    """
    获取工作流列表
    
    Args:
        skip: 跳过的记录数
        limit: 返回的记录数
        workflow_type: 工作流类型过滤
        status: 状态过滤
        search: 搜索关键词
        current_user: 当前用户
        service: 工作流服务
        
    Returns:
        List[WorkflowResponse]: 工作流列表
    """
    try:
        workflows = await service.get_user_workflows(
            user_id=current_user.id,
            skip=skip,
            limit=limit,
            workflow_type=workflow_type,
            status=status,
            search=search
        )
        
        return [WorkflowResponse.from_orm(workflow) for workflow in workflows]
    except Exception as e:
        logger.error(f"获取工作流列表错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取工作流列表失败: {str(e)}"
        )


@router.get("/{workflow_id}", response_model=WorkflowResponse)
async def get_workflow(
    workflow_id: int = Path(..., description="工作流ID"),
    current_user: User = Depends(get_current_user),
    service: WorkflowService = Depends(get_workflow_service)
) -> WorkflowResponse:
    """
    获取工作流详情
    
    Args:
        workflow_id: 工作流ID
        current_user: 当前用户
        service: 工作流服务
        
    Returns:
        WorkflowResponse: 工作流详情
        
    Raises:
        HTTPException: 工作流不存在或无权限访问时抛出异常
    """
    try:
        workflow = await service.get_workflow(
            workflow_id=workflow_id,
            user_id=current_user.id
        )
        
        if not workflow:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="工作流不存在或无权限访问"
            )
        
        return WorkflowResponse.from_orm(workflow)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取工作流详情错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取工作流详情失败: {str(e)}"
        )


@router.put("/{workflow_id}", response_model=WorkflowResponse)
async def update_workflow(
    workflow_id: int = Path(..., description="工作流ID"),
    workflow_data: WorkflowUpdate = Body(..., description="工作流更新数据"),
    current_user: User = Depends(get_current_user),
    service: WorkflowService = Depends(get_workflow_service)
) -> WorkflowResponse:
    """
    更新工作流
    
    Args:
        workflow_id: 工作流ID
        workflow_data: 工作流更新数据
        current_user: 当前用户
        service: 工作流服务
        
    Returns:
        WorkflowResponse: 更新后的工作流信息
        
    Raises:
        HTTPException: 更新失败时抛出异常
    """
    try:
        workflow = await service.update_workflow(
            workflow_id=workflow_id,
            user_id=current_user.id,
            workflow_data=workflow_data
        )
        
        if not workflow:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="工作流不存在或无权限修改"
            )
        
        return WorkflowResponse.from_orm(workflow)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新工作流错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新工作流失败: {str(e)}"
        )


@router.delete("/{workflow_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_workflow(
    workflow_id: int = Path(..., description="工作流ID"),
    current_user: User = Depends(get_current_user),
    service: WorkflowService = Depends(get_workflow_service)
) -> None:
    """
    删除工作流
    
    Args:
        workflow_id: 工作流ID
        current_user: 当前用户
        service: 工作流服务
        
    Raises:
        HTTPException: 删除失败时抛出异常
    """
    try:
        success = await service.delete_workflow(
            workflow_id=workflow_id,
            user_id=current_user.id
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="工作流不存在或无权限删除"
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除工作流错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除工作流失败: {str(e)}"
        )


@router.post("/{workflow_id}/validate")
async def validate_workflow(
    workflow_id: int = Path(..., description="工作流ID"),
    current_user: User = Depends(get_current_user),
    service: WorkflowService = Depends(get_workflow_service)
) -> Dict[str, Any]:
    """
    验证工作流定义
    
    Args:
        workflow_id: 工作流ID
        current_user: 当前用户
        service: 工作流服务
        
    Returns:
        Dict[str, Any]: 验证结果
        
    Raises:
        HTTPException: 验证失败时抛出异常
    """
    try:
        result = await service.validate_workflow(
            workflow_id=workflow_id,
            user_id=current_user.id
        )
        
        if result is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="工作流不存在或无权限访问"
            )
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"验证工作流错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"验证工作流失败: {str(e)}"
        )


@router.post("/{workflow_id}/execute", response_model=WorkflowExecutionResponse)
async def execute_workflow(
    workflow_id: int = Path(..., description="工作流ID"),
    execution_data: WorkflowExecutionCreate = Body(..., description="执行参数"),
    priority: TaskPriority = Query(TaskPriority.NORMAL, description="执行优先级"),
    current_user: User = Depends(get_current_user),
    service: WorkflowService = Depends(get_workflow_service),
    scheduler: WorkflowScheduler = Depends(get_workflow_scheduler)
) -> WorkflowExecutionResponse:
    """
    执行工作流
    
    Args:
        workflow_id: 工作流ID
        execution_data: 执行参数
        priority: 执行优先级
        current_user: 当前用户
        service: 工作流服务
        scheduler: 工作流调度器
        
    Returns:
        WorkflowExecutionResponse: 执行信息
        
    Raises:
        HTTPException: 执行失败时抛出异常
    """
    try:
        # 创建执行记录
        execution = await service.create_execution(
            workflow_id=workflow_id,
            user_id=current_user.id,
            execution_data=execution_data
        )
        
        if not execution:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="创建执行记录失败"
            )
        
        # 调度执行
        scheduled = await scheduler.schedule_workflow(
            execution_id=execution.execution_id,
            workflow_id=workflow_id,
            user_id=current_user.id,
            priority=priority,
            config=execution_data.config
        )
        
        if not scheduled:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="工作流调度失败"
            )
        
        logger.info(f"工作流已调度执行: {execution.execution_id}")
        return WorkflowExecutionResponse.from_orm(execution)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"执行工作流错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"执行工作流失败: {str(e)}"
        )


@router.get("/{workflow_id}/executions", response_model=List[WorkflowExecutionResponse])
async def list_workflow_executions(
    workflow_id: int = Path(..., description="工作流ID"),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    status: Optional[WorkflowExecutionStatus] = Query(None, description="状态过滤"),
    current_user: User = Depends(get_current_user),
    service: WorkflowService = Depends(get_workflow_service)
) -> List[WorkflowExecutionResponse]:
    """
    获取工作流执行历史
    
    Args:
        workflow_id: 工作流ID
        skip: 跳过的记录数
        limit: 返回的记录数
        status: 状态过滤
        current_user: 当前用户
        service: 工作流服务
        
    Returns:
        List[WorkflowExecutionResponse]: 执行历史列表
    """
    try:
        executions = await service.get_workflow_executions(
            workflow_id=workflow_id,
            user_id=current_user.id,
            skip=skip,
            limit=limit,
            status=status
        )
        
        return [WorkflowExecutionResponse.from_orm(execution) for execution in executions]
    except Exception as e:
        logger.error(f"获取执行历史错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取执行历史失败: {str(e)}"
        )


@router.get("/executions/{execution_id}", response_model=WorkflowExecutionResponse)
async def get_execution(
    execution_id: str = Path(..., description="执行ID"),
    current_user: User = Depends(get_current_user),
    service: WorkflowService = Depends(get_workflow_service)
) -> WorkflowExecutionResponse:
    """
    获取执行详情
    
    Args:
        execution_id: 执行ID
        current_user: 当前用户
        service: 工作流服务
        
    Returns:
        WorkflowExecutionResponse: 执行详情
        
    Raises:
        HTTPException: 执行不存在或无权限访问时抛出异常
    """
    try:
        execution = await service.get_execution(
            execution_id=execution_id,
            user_id=current_user.id
        )
        
        if not execution:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="执行记录不存在或无权限访问"
            )
        
        return WorkflowExecutionResponse.from_orm(execution)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取执行详情错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取执行详情失败: {str(e)}"
        )


@router.post("/executions/{execution_id}/cancel")
async def cancel_execution(
    execution_id: str = Path(..., description="执行ID"),
    current_user: User = Depends(get_current_user),
    service: WorkflowService = Depends(get_workflow_service),
    scheduler: WorkflowScheduler = Depends(get_workflow_scheduler)
) -> Dict[str, Any]:
    """
    取消工作流执行
    
    Args:
        execution_id: 执行ID
        current_user: 当前用户
        service: 工作流服务
        scheduler: 工作流调度器
        
    Returns:
        Dict[str, Any]: 取消结果
        
    Raises:
        HTTPException: 取消失败时抛出异常
    """
    try:
        # 检查执行权限
        execution = await service.get_execution(
            execution_id=execution_id,
            user_id=current_user.id
        )
        
        if not execution:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="执行记录不存在或无权限访问"
            )
        
        # 取消调度
        cancelled = await scheduler.cancel_task(execution_id)
        
        if cancelled:
            # 更新执行状态
            await service.update_execution_status(
                execution_id=execution_id,
                status=WorkflowExecutionStatus.CANCELLED,
                user_id=current_user.id
            )
        
        return {
            "success": cancelled,
            "message": "执行已取消" if cancelled else "执行取消失败或已完成"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消执行错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"取消执行失败: {str(e)}"
        )


@router.get("/executions/{execution_id}/status")
async def get_execution_status(
    execution_id: str = Path(..., description="执行ID"),
    current_user: User = Depends(get_current_user),
    service: WorkflowService = Depends(get_workflow_service)
) -> Dict[str, Any]:
    """
    获取执行状态
    
    Args:
        execution_id: 执行ID
        current_user: 当前用户
        service: 工作流服务
        
    Returns:
        Dict[str, Any]: 执行状态信息
    """
    try:
        status_info = await service.get_execution_status(
            execution_id=execution_id,
            user_id=current_user.id
        )
        
        if not status_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="执行记录不存在或无权限访问"
            )
        
        return status_info
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取执行状态错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取执行状态失败: {str(e)}"
        )


@router.get("/executions/{execution_id}/logs")
async def get_execution_logs(
    execution_id: str = Path(..., description="执行ID"),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    level: Optional[str] = Query(None, description="日志级别过滤"),
    current_user: User = Depends(get_current_user),
    service: WorkflowService = Depends(get_workflow_service)
) -> Dict[str, Any]:
    """
    获取执行日志
    
    Args:
        execution_id: 执行ID
        skip: 跳过的记录数
        limit: 返回的记录数
        level: 日志级别过滤
        current_user: 当前用户
        service: 工作流服务
        
    Returns:
        Dict[str, Any]: 执行日志
    """
    try:
        logs = await service.get_execution_logs(
            execution_id=execution_id,
            user_id=current_user.id,
            skip=skip,
            limit=limit,
            level=level
        )
        
        if logs is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="执行记录不存在或无权限访问"
            )
        
        return logs
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取执行日志错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取执行日志失败: {str(e)}"
        )


@router.get("/executions/{execution_id}/stream")
async def stream_execution_logs(
    execution_id: str = Path(..., description="执行ID"),
    current_user: User = Depends(get_current_user),
    service: WorkflowService = Depends(get_workflow_service)
) -> StreamingResponse:
    """
    流式获取执行日志
    
    Args:
        execution_id: 执行ID
        current_user: 当前用户
        service: 工作流服务
        
    Returns:
        StreamingResponse: 流式响应
    """
    try:
        # 检查执行权限
        execution = await service.get_execution(
            execution_id=execution_id,
            user_id=current_user.id
        )
        
        if not execution:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="执行记录不存在或无权限访问"
            )
        
        async def log_generator():
            """日志生成器"""
            try:
                async for log_entry in service.stream_execution_logs(
                    execution_id=execution_id,
                    user_id=current_user.id
                ):
                    yield f"data: {json.dumps(log_entry, ensure_ascii=False)}\n\n"
            except Exception as e:
                yield f"data: {{\"error\": \"{str(e)}\"}}\n\n"
        
        return StreamingResponse(
            log_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive"
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"流式获取日志错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"流式获取日志失败: {str(e)}"
        )


@router.get("/analytics/performance")
async def get_performance_analytics(
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    workflow_id: Optional[int] = Query(None, description="工作流ID过滤"),
    current_user: User = Depends(get_current_user),
    service: WorkflowService = Depends(get_workflow_service)
) -> Dict[str, Any]:
    """
    获取性能分析数据
    
    Args:
        start_date: 开始日期
        end_date: 结束日期
        workflow_id: 工作流ID过滤
        current_user: 当前用户
        service: 工作流服务
        
    Returns:
        Dict[str, Any]: 性能分析数据
    """
    try:
        analytics = await service.get_performance_analytics(
            user_id=current_user.id,
            start_date=start_date,
            end_date=end_date,
            workflow_id=workflow_id
        )
        
        return analytics
    except Exception as e:
        logger.error(f"获取性能分析错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取性能分析失败: {str(e)}"
        )


@router.get("/analytics/usage")
async def get_usage_analytics(
    period: str = Query("7d", description="统计周期 (1d, 7d, 30d, 90d)"),
    current_user: User = Depends(get_current_user),
    service: WorkflowService = Depends(get_workflow_service)
) -> Dict[str, Any]:
    """
    获取使用情况分析
    
    Args:
        period: 统计周期
        current_user: 当前用户
        service: 工作流服务
        
    Returns:
        Dict[str, Any]: 使用情况分析数据
    """
    try:
        analytics = await service.get_usage_analytics(
            user_id=current_user.id,
            period=period
        )
        
        return analytics
    except Exception as e:
        logger.error(f"获取使用分析错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取使用分析失败: {str(e)}"
        )


@router.get("/scheduler/status")
async def get_scheduler_status(
    current_user: User = Depends(get_current_user),
    scheduler: WorkflowScheduler = Depends(get_workflow_scheduler)
) -> Dict[str, Any]:
    """
    获取调度器状态
    
    Args:
        current_user: 当前用户
        scheduler: 工作流调度器
        
    Returns:
        Dict[str, Any]: 调度器状态信息
    """
    try:
        # 检查管理员权限（可选）
        # if not current_user.is_admin:
        #     raise HTTPException(
        #         status_code=status.HTTP_403_FORBIDDEN,
        #         detail="需要管理员权限"
        #     )
        
        status_info = await scheduler.get_scheduler_status()
        return status_info
    except Exception as e:
        logger.error(f"获取调度器状态错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取调度器状态失败: {str(e)}"
        )


@router.get("/context/{execution_id}")
async def get_execution_context(
    execution_id: str = Path(..., description="执行ID"),
    current_user: User = Depends(get_current_user),
    service: WorkflowService = Depends(get_workflow_service),
    context_mgr: WorkflowContextManager = Depends(get_context_manager)
) -> Dict[str, Any]:
    """
    获取执行上下文
    
    Args:
        execution_id: 执行ID
        current_user: 当前用户
        service: 工作流服务
        context_mgr: 上下文管理器
        
    Returns:
        Dict[str, Any]: 执行上下文信息
    """
    try:
        # 检查执行权限
        execution = await service.get_execution(
            execution_id=execution_id,
            user_id=current_user.id
        )
        
        if not execution:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="执行记录不存在或无权限访问"
            )
        
        # 获取上下文ID
        context_id = await context_mgr.get_context_by_execution(execution_id)
        if not context_id:
            return {"context_id": None, "statistics": {}}
        
        # 获取上下文统计信息
        statistics = await context_mgr.get_context_statistics(context_id)
        
        return {
            "context_id": context_id,
            "statistics": statistics
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取执行上下文错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取执行上下文失败: {str(e)}"
        )


@router.get("/context/{execution_id}/snapshots")
async def list_context_snapshots(
    execution_id: str = Path(..., description="执行ID"),
    current_user: User = Depends(get_current_user),
    service: WorkflowService = Depends(get_workflow_service),
    context_mgr: WorkflowContextManager = Depends(get_context_manager)
) -> List[Dict[str, Any]]:
    """
    列出上下文快照
    
    Args:
        execution_id: 执行ID
        current_user: 当前用户
        service: 工作流服务
        context_mgr: 上下文管理器
        
    Returns:
        List[Dict[str, Any]]: 快照列表
    """
    try:
        # 检查执行权限
        execution = await service.get_execution(
            execution_id=execution_id,
            user_id=current_user.id
        )
        
        if not execution:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="执行记录不存在或无权限访问"
            )
        
        # 获取上下文ID
        context_id = await context_mgr.get_context_by_execution(execution_id)
        if not context_id:
            return []
        
        # 列出快照
        snapshots = await context_mgr.list_snapshots(context_id)
        return snapshots
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"列出上下文快照错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"列出上下文快照失败: {str(e)}"
        )


@router.post("/templates")
async def create_workflow_template(
    template_data: Dict[str, Any] = Body(..., description="模板数据"),
    current_user: User = Depends(get_current_user),
    service: WorkflowService = Depends(get_workflow_service)
) -> Dict[str, Any]:
    """
    创建工作流模板
    
    Args:
        template_data: 模板数据
        current_user: 当前用户
        service: 工作流服务
        
    Returns:
        Dict[str, Any]: 创建的模板信息
    """
    try:
        template = await service.create_template(
            user_id=current_user.id,
            template_data=template_data
        )
        
        return template
    except Exception as e:
        logger.error(f"创建工作流模板错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建工作流模板失败: {str(e)}"
        )


@router.get("/templates")
async def list_workflow_templates(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    category: Optional[str] = Query(None, description="分类过滤"),
    current_user: User = Depends(get_current_user),
    service: WorkflowService = Depends(get_workflow_service)
) -> List[Dict[str, Any]]:
    """
    获取工作流模板列表
    
    Args:
        skip: 跳过的记录数
        limit: 返回的记录数
        category: 分类过滤
        current_user: 当前用户
        service: 工作流服务
        
    Returns:
        List[Dict[str, Any]]: 模板列表
    """
    try:
        templates = await service.get_templates(
            user_id=current_user.id,
            skip=skip,
            limit=limit,
            category=category
        )
        
        return templates
    except Exception as e:
        logger.error(f"获取工作流模板错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取工作流模板失败: {str(e)}"
        )