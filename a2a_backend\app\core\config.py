#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统配置管理模块

支持环境变量和数据库配置存储，使用Pydantic Settings
"""

import os
from functools import lru_cache
from typing import List, Optional, Any, Dict
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings
from loguru import logger


class Settings(BaseSettings):
    """
    应用配置类
    
    支持从环境变量和数据库加载配置
    """
    
    # 基础配置
    app_name: str = Field(default="A2A多智能体系统", description="应用名称")
    debug: bool = Field(default=False, description="调试模式")
    host: str = Field(default="0.0.0.0", description="服务器主机")
    port: int = Field(default=8000, description="服务器端口")
    
    # 数据库配置
    database_url: str = Field(
        default="mysql+aiomysql://root:password@localhost:3306/a2a_system",
        description="数据库连接URL"
    )
    database_pool_size: int = Field(default=20, description="数据库连接池大小")
    database_max_overflow: int = Field(default=30, description="数据库连接池最大溢出")
    database_pool_timeout: int = Field(default=30, description="数据库连接池超时时间")
    database_pool_recycle: int = Field(default=3600, description="数据库连接回收时间")
    
    # JWT配置
    jwt_secret_key: str = Field(
        default="your-super-secret-jwt-key-change-in-production",
        description="JWT密钥"
    )
    jwt_algorithm: str = Field(default="HS256", description="JWT算法")
    jwt_access_token_expire_minutes: int = Field(default=30, description="访问令牌过期时间（分钟）")
    jwt_refresh_token_expire_days: int = Field(default=7, description="刷新令牌过期时间（天）")
    
    # 密码加密配置
    password_bcrypt_rounds: int = Field(default=12, description="bcrypt加密轮数")
    password_min_length: int = Field(default=8, description="密码最小长度")
    password_max_length: int = Field(default=128, description="密码最大长度")
    
    # CORS配置
    allowed_origins: List[str] = Field(
        default=["http://localhost:3000", "http://127.0.0.1:3000"],
        description="允许的跨域源"
    )
    
    # Google ADK配置
    google_adk_project_id: Optional[str] = Field(default=None, description="Google ADK项目ID")
    google_adk_credentials_path: Optional[str] = Field(default=None, description="Google ADK凭证文件路径")
    
    # LLM配置
    default_llm_provider: str = Field(default="google", description="默认LLM提供商")
    default_llm_model: str = Field(default="gemini-pro", description="默认LLM模型")
    
    # 工具配置
    tools_enabled: bool = Field(default=True, description="是否启用工具系统")
    mcp_enabled: bool = Field(default=True, description="是否启用MCP工具")
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_to_database: bool = Field(default=True, description="是否将日志存储到数据库")
    log_file_path: Optional[str] = Field(default=None, description="日志文件路径")
    
    # 监控配置
    metrics_enabled: bool = Field(default=True, description="是否启用指标收集")
    prometheus_enabled: bool = Field(default=False, description="是否启用Prometheus")
    
    # 文件存储配置
    file_storage_type: str = Field(default="database", description="文件存储类型")
    file_storage_path: str = Field(default="./storage", description="文件存储路径")
    max_file_size: int = Field(default=100 * 1024 * 1024, description="最大文件大小（字节）")
    
    # 会话配置
    session_timeout_minutes: int = Field(default=60, description="会话超时时间（分钟）")
    max_sessions_per_user: int = Field(default=10, description="每用户最大会话数")
    
    # 工作流配置
    workflow_max_steps: int = Field(default=100, description="工作流最大步骤数")
    workflow_timeout_minutes: int = Field(default=30, description="工作流超时时间（分钟）")
    
    # 任务配置
    max_concurrent_tasks_per_user: int = Field(default=5, description="每用户最大并发任务数")
    task_timeout_minutes: int = Field(default=60, description="任务超时时间（分钟）")
    max_task_duration_minutes: int = Field(default=120, description="任务最大持续时间（分钟）")
    max_task_retries: int = Field(default=3, description="任务最大重试次数")
    max_memory_usage_mb: int = Field(default=1024, description="任务最大内存使用量（MB）")
    
    # 安全配置
    max_login_attempts: int = Field(default=5, description="最大登录尝试次数")
    account_lockout_duration_minutes: int = Field(default=30, description="账户锁定持续时间（分钟）")
    
    @field_validator("allowed_origins", mode="before")
    @classmethod
    def parse_cors_origins(cls, v):
        """
        解析CORS允许的源
        
        Args:
            v: 原始值
            
        Returns:
            List[str]: 解析后的源列表
        """
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @field_validator("database_url")
    @classmethod
    def validate_database_url(cls, v):
        """
        验证数据库URL
        
        Args:
            v: 数据库URL
            
        Returns:
            str: 验证后的数据库URL
        """
        allowed_schemes = ("mysql+aiomysql://", "mysql+asyncmy://", "sqlite+aiosqlite://")
        if not v.startswith(allowed_schemes):
            raise ValueError("数据库URL必须使用支持的异步驱动（MySQL或SQLite）")
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        env_prefix = "A2A_"


class DatabaseConfigManager:
    """
    数据库配置管理器
    
    用于从数据库加载和保存配置
    """
    
    def __init__(self):
        self._cache: Dict[str, Any] = {}
        self._cache_timeout = 300  # 5分钟缓存
    
    async def get_config(self, key: str, default: Any = None) -> Any:
        """
        从数据库获取配置
        
        Args:
            key: 配置键
            default: 默认值
            
        Returns:
            Any: 配置值
        """
        try:
            # 这里应该从数据库查询配置
            # 暂时返回缓存或默认值
            return self._cache.get(key, default)
        except Exception as e:
            logger.error(f"获取数据库配置失败: {e}")
            return default
    
    async def set_config(self, key: str, value: Any) -> bool:
        """
        设置数据库配置
        
        Args:
            key: 配置键
            value: 配置值
            
        Returns:
            bool: 是否设置成功
        """
        try:
            # 这里应该保存到数据库
            # 暂时保存到缓存
            self._cache[key] = value
            logger.info(f"配置已更新: {key}")
            return True
        except Exception as e:
            logger.error(f"设置数据库配置失败: {e}")
            return False
    
    async def get_all_configs(self) -> Dict[str, Any]:
        """
        获取所有数据库配置
        
        Returns:
            Dict[str, Any]: 所有配置
        """
        try:
            # 这里应该从数据库查询所有配置
            return self._cache.copy()
        except Exception as e:
            logger.error(f"获取所有数据库配置失败: {e}")
            return {}


# 全局配置实例
_settings: Optional[Settings] = None
_db_config_manager: Optional[DatabaseConfigManager] = None


@lru_cache()
def get_settings() -> Settings:
    """
    获取应用配置实例（单例模式）
    
    Returns:
        Settings: 配置实例
    """
    global _settings
    if _settings is None:
        _settings = Settings()
        logger.info("配置已加载")
    return _settings


def get_db_config_manager() -> DatabaseConfigManager:
    """
    获取数据库配置管理器实例（单例模式）
    
    Returns:
        DatabaseConfigManager: 数据库配置管理器实例
    """
    global _db_config_manager
    if _db_config_manager is None:
        _db_config_manager = DatabaseConfigManager()
        logger.info("数据库配置管理器已初始化")
    return _db_config_manager


def reload_settings() -> Settings:
    """
    重新加载配置
    
    Returns:
        Settings: 新的配置实例
    """
    global _settings
    get_settings.cache_clear()
    _settings = None
    return get_settings()


# 导出settings实例供其他模块使用
settings = get_settings()