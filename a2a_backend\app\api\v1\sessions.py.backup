# -*- coding: utf-8 -*-
"""
A2A多智能体系统会话API接口

提供会话管理、消息处理、状态管理和历史查询功能
"""

from datetime import datetime
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, status, Request, Query, WebSocket, WebSocketDisconnect
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field, validator
from loguru import logger
import json

from app.services.session_service import SessionService
from app.services.message_service import MessageService
from app.services.auth_service import AuthService
from app.core.config import get_settings
from app.core.rate_limiter import RateLimiter
from app.core.security import get_client_ip


# 创建路由器
router = APIRouter(prefix="/sessions", tags=["会话管理"])

# 安全依赖
security = HTTPBearer()

# 服务实例
session_service = SessionService()
message_service = MessageService()
auth_service = AuthService()
settings = get_settings()
rate_limiter = RateLimiter()


# 请求模型
class SessionCreateRequest(BaseModel):
    """会话创建请求模型"""
    title: str = Field(..., min_length=1, max_length=200, description="会话标题")
    description: Optional[str] = Field(None, max_length=1000, description="会话描述")
    session_type: str = Field(..., description="会话类型")
    agent_ids: Optional[List[int]] = Field(None, description="参与的智能体ID列表")
    participant_ids: Optional[List[int]] = Field(None, description="参与的用户ID列表")
    config: Optional[Dict[str, Any]] = Field(None, description="会话配置")
    is_private: bool = Field(True, description="是否私有会话")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    
    @validator('session_type')
    def validate_session_type(cls, v):
        """验证会话类型"""
        allowed_types = ['chat', 'task', 'workflow', 'collaboration', 'debug']
        if v not in allowed_types:
            raise ValueError(f'会话类型必须是: {", ".join(allowed_types)}')
        return v


class SessionUpdateRequest(BaseModel):
    """会话更新请求模型"""
    title: Optional[str] = Field(None, min_length=1, max_length=200, description="会话标题")
    description: Optional[str] = Field(None, max_length=1000, description="会话描述")
    config: Optional[Dict[str, Any]] = Field(None, description="会话配置")
    is_private: Optional[bool] = Field(None, description="是否私有会话")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    status: Optional[str] = Field(None, description="会话状态")
    
    @validator('status')
    def validate_status(cls, v):
        """验证会话状态"""
        if v is not None:
            allowed_statuses = ['active', 'paused', 'completed', 'archived']
            if v not in allowed_statuses:
                raise ValueError(f'状态必须是: {", ".join(allowed_statuses)}')
        return v


class MessageSendRequest(BaseModel):
    """消息发送请求模型"""
    content: str = Field(..., min_length=1, description="消息内容")
    message_type: str = Field("text", description="消息类型")
    metadata: Optional[Dict[str, Any]] = Field(None, description="消息元数据")
    reply_to_id: Optional[int] = Field(None, description="回复的消息ID")
    agent_id: Optional[int] = Field(None, description="发送消息的智能体ID")
    
    @validator('message_type')
    def validate_message_type(cls, v):
        """验证消息类型"""
        allowed_types = ['text', 'image', 'file', 'code', 'system', 'error']
        if v not in allowed_types:
            raise ValueError(f'消息类型必须是: {", ".join(allowed_types)}')
        return v


class ParticipantRequest(BaseModel):
    """参与者请求模型"""
    user_id: Optional[int] = Field(None, description="用户ID")
    agent_id: Optional[int] = Field(None, description="智能体ID")
    role: str = Field("participant", description="角色")
    
    @validator('role')
    def validate_role(cls, v):
        """验证角色"""
        allowed_roles = ['owner', 'admin', 'participant', 'observer']
        if v not in allowed_roles:
            raise ValueError(f'角色必须是: {", ".join(allowed_roles)}')
        return v
    
    def __init__(self, **data):
        super().__init__(**data)
        # 确保至少指定了用户ID或智能体ID之一
        if not self.user_id and not self.agent_id:
            raise ValueError('必须指定用户ID或智能体ID')


# 响应模型
class SessionResponse(BaseModel):
    """会话响应模型"""
    session_id: int = Field(..., description="会话ID")
    title: str = Field(..., description="会话标题")
    description: Optional[str] = Field(None, description="会话描述")
    session_type: str = Field(..., description="会话类型")
    status: str = Field(..., description="会话状态")
    is_private: bool = Field(..., description="是否私有会话")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    config: Optional[Dict[str, Any]] = Field(None, description="会话配置")
    owner_id: int = Field(..., description="所有者ID")
    participant_count: int = Field(..., description="参与者数量")
    message_count: int = Field(..., description="消息数量")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    last_activity_at: Optional[datetime] = Field(None, description="最后活动时间")


class MessageResponse(BaseModel):
    """消息响应模型"""
    message_id: int = Field(..., description="消息ID")
    session_id: int = Field(..., description="会话ID")
    sender_id: Optional[int] = Field(None, description="发送者用户ID")
    agent_id: Optional[int] = Field(None, description="发送者智能体ID")
    content: str = Field(..., description="消息内容")
    message_type: str = Field(..., description="消息类型")
    metadata: Optional[Dict[str, Any]] = Field(None, description="消息元数据")
    reply_to_id: Optional[int] = Field(None, description="回复的消息ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    reactions: Optional[List[Dict[str, Any]]] = Field(None, description="反应列表")


class ApiResponse(BaseModel):
    """API响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")


# 依赖函数
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """获取当前用户"""
    try:
        token = credentials.credentials
        user_info = await auth_service.verify_token(token)
        return user_info
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的访问令牌",
            headers={"WWW-Authenticate": "Bearer"}
        )


async def check_session_permission(session_id: int, user_id: int, permission: str = "read"):
    """检查会话权限"""
    has_permission = await auth_service.check_resource_permission(
        user_id, "session", str(session_id), permission
    )
    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"没有对会话{session_id}的{permission}权限"
        )


# WebSocket连接管理
class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        self.active_connections: Dict[int, List[WebSocket]] = {}
    
    async def connect(self, websocket: WebSocket, session_id: int):
        """连接WebSocket"""
        await websocket.accept()
        if session_id not in self.active_connections:
            self.active_connections[session_id] = []
        self.active_connections[session_id].append(websocket)
    
    def disconnect(self, websocket: WebSocket, session_id: int):
        """断开WebSocket连接"""
        if session_id in self.active_connections:
            if websocket in self.active_connections[session_id]:
                self.active_connections[session_id].remove(websocket)
            if not self.active_connections[session_id]:
                del self.active_connections[session_id]
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """发送个人消息"""
        await websocket.send_text(message)
    
    async def broadcast_to_session(self, message: str, session_id: int):
        """向会话广播消息"""
        if session_id in self.active_connections:
            for connection in self.active_connections[session_id]:
                try:
                    await connection.send_text(message)
                except:
                    # 连接已断开，移除
                    self.active_connections[session_id].remove(connection)


manager = ConnectionManager()


# API接口
@router.post("/", response_model=SessionResponse, summary="创建会话")
async def create_session(
    request: SessionCreateRequest,
    http_request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    创建新的会话
    
    - **title**: 会话标题
    - **description**: 会话描述（可选）
    - **session_type**: 会话类型（chat/task/workflow/collaboration/debug）
    - **agent_ids**: 参与的智能体ID列表（可选）
    - **participant_ids**: 参与的用户ID列表（可选）
    - **config**: 会话配置（可选）
    - **is_private**: 是否私有会话（默认true）
    - **tags**: 标签列表（可选）
    
    需要提供有效的访问令牌
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 速率限制检查
        await rate_limiter.check_rate_limit(
            key=f"session_create:{current_user['user_id']}",
            limit=50,  # 每小时最多创建50个会话
            window=3600
        )
        
        # 准备会话数据
        session_data = {
            "title": request.title,
            "description": request.description,
            "session_type": request.session_type,
            "is_private": request.is_private,
            "tags": request.tags or [],
            "config": request.config or {},
            "owner_id": current_user["user_id"]
        }
        
        # 创建会话
        result = await session_service.create_session(
            session_data, current_user["user_id"], client_ip
        )
        
        if result["success"]:
            session = result["data"]
            session_id = session["session_id"]
            
            # 添加智能体参与者
            if request.agent_ids:
                for agent_id in request.agent_ids:
                    await session_service.add_participant(
                        session_id, agent_id=agent_id, role="participant",
                        added_by=current_user["user_id"], client_ip=client_ip
                    )
            
            # 添加用户参与者
            if request.participant_ids:
                for user_id in request.participant_ids:
                    await session_service.add_participant(
                        session_id, user_id=user_id, role="participant",
                        added_by=current_user["user_id"], client_ip=client_ip
                    )
            
            logger.info(f"会话创建成功", extra={
                "session_id": session_id,
                "session_title": session["title"],
                "session_type": session["session_type"],
                "owner_id": current_user["user_id"],
                "client_ip": client_ip
            })
            
            return SessionResponse(**session)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except ValueError as e:
        logger.warning(f"会话创建失败: {str(e)}", extra={
            "user_id": current_user["user_id"],
            "client_ip": get_client_ip(http_request)
        })
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"会话创建异常: {str(e)}", extra={
            "user_id": current_user["user_id"],
            "client_ip": get_client_ip(http_request)
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="会话创建失败，请稍后重试"
        )


@router.get("/{session_id}", response_model=SessionResponse, summary="获取会话信息")
async def get_session(
    session_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取指定会话的详细信息
    
    - **session_id**: 会话ID
    
    需要对会话有读取权限
    """
    try:
        # 检查权限
        await check_session_permission(session_id, current_user["user_id"], "read")
        
        # 获取会话信息
        session = await session_service.get_session(session_id, current_user["user_id"])
        
        if session:
            return SessionResponse(**session)
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="会话不存在"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取会话信息异常: {str(e)}", extra={
            "session_id": session_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取会话信息失败"
        )


@router.put("/{session_id}", response_model=SessionResponse, summary="更新会话")
async def update_session(
    session_id: int,
    request: SessionUpdateRequest,
    http_request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    更新会话信息
    
    - **session_id**: 会话ID
    - **title**: 会话标题（可选）
    - **description**: 会话描述（可选）
    - **config**: 会话配置（可选）
    - **is_private**: 是否私有会话（可选）
    - **tags**: 标签列表（可选）
    - **status**: 会话状态（可选）
    
    需要对会话有写入权限
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 检查权限
        await check_session_permission(session_id, current_user["user_id"], "write")
        
        # 准备更新数据
        update_data = {}
        if request.title is not None:
            update_data["title"] = request.title
        if request.description is not None:
            update_data["description"] = request.description
        if request.config is not None:
            update_data["config"] = request.config
        if request.is_private is not None:
            update_data["is_private"] = request.is_private
        if request.tags is not None:
            update_data["tags"] = request.tags
        if request.status is not None:
            update_data["status"] = request.status
        
        # 更新会话
        result = await session_service.update_session(
            session_id, update_data, current_user["user_id"], client_ip
        )
        
        if result["success"]:
            session = result["data"]
            
            logger.info(f"会话更新成功", extra={
                "session_id": session_id,
                "updated_fields": list(update_data.keys()),
                "user_id": current_user["user_id"],
                "client_ip": client_ip
            })
            
            # 广播更新通知
            await manager.broadcast_to_session(
                json.dumps({
                    "type": "session_updated",
                    "session_id": session_id,
                    "updated_fields": list(update_data.keys()),
                    "timestamp": datetime.utcnow().isoformat()
                }),
                session_id
            )
            
            return SessionResponse(**session)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except ValueError as e:
        logger.warning(f"会话更新失败: {str(e)}", extra={
            "session_id": session_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"会话更新异常: {str(e)}", extra={
            "session_id": session_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="会话更新失败，请稍后重试"
        )


@router.delete("/{session_id}", response_model=ApiResponse, summary="删除会话")
async def delete_session(
    session_id: int,
    hard_delete: bool = Query(False, description="是否硬删除"),
    http_request: Request = None,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    删除会话
    
    - **session_id**: 会话ID
    - **hard_delete**: 是否硬删除（默认false，软删除）
    
    需要对会话有删除权限
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 检查权限
        await check_session_permission(session_id, current_user["user_id"], "delete")
        
        # 删除会话
        result = await session_service.delete_session(
            session_id, hard_delete, current_user["user_id"], client_ip
        )
        
        if result["success"]:
            logger.info(f"会话删除成功", extra={
                "session_id": session_id,
                "hard_delete": hard_delete,
                "user_id": current_user["user_id"],
                "client_ip": client_ip
            })
            
            # 广播删除通知
            await manager.broadcast_to_session(
                json.dumps({
                    "type": "session_deleted",
                    "session_id": session_id,
                    "timestamp": datetime.utcnow().isoformat()
                }),
                session_id
            )
            
            return ApiResponse(
                success=True,
                message=result["message"],
                data={
                    "session_id": session_id,
                    "hard_delete": hard_delete,
                    "deleted_at": datetime.utcnow().isoformat()
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"会话删除异常: {str(e)}", extra={
            "session_id": session_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="会话删除失败，请稍后重试"
        )


@router.get("/", response_model=Dict[str, Any], summary="获取会话列表")
async def list_sessions(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="页面大小"),
    session_type: Optional[str] = Query(None, description="会话类型过滤"),
    status: Optional[str] = Query(None, description="状态过滤"),
    is_private: Optional[bool] = Query(None, description="私有状态过滤"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    tags: Optional[str] = Query(None, description="标签过滤（逗号分隔）"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取会话列表
    
    - **page**: 页码（默认1）
    - **page_size**: 页面大小（默认20，最大100）
    - **session_type**: 会话类型过滤
    - **status**: 状态过滤
    - **is_private**: 私有状态过滤
    - **search**: 搜索关键词（标题、描述）
    - **tags**: 标签过滤（逗号分隔）
    
    返回用户有权限访问的会话列表
    """
    try:
        # 构建过滤条件
        filters = {}
        if session_type:
            filters["session_type"] = session_type
        if status:
            filters["status"] = status
        if is_private is not None:
            filters["is_private"] = is_private
        if search:
            filters["search"] = search
        if tags:
            filters["tags"] = [tag.strip() for tag in tags.split(",")]
        
        # 获取会话列表
        result = await session_service.list_sessions(
            current_user["user_id"], filters, page, page_size
        )
        
        return {
            "success": True,
            "message": "获取会话列表成功",
            "data": result
        }
        
    except Exception as e:
        logger.error(f"获取会话列表异常: {str(e)}", extra={
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取会话列表失败"
        )


@router.post("/{session_id}/messages", response_model=MessageResponse, summary="发送消息")
async def send_message(
    session_id: int,
    request: MessageSendRequest,
    http_request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    在会话中发送消息
    
    - **session_id**: 会话ID
    - **content**: 消息内容
    - **message_type**: 消息类型（text/image/file/code/system/error）
    - **metadata**: 消息元数据（可选）
    - **reply_to_id**: 回复的消息ID（可选）
    - **agent_id**: 发送消息的智能体ID（可选）
    
    需要对会话有写入权限
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 检查权限
        await check_session_permission(session_id, current_user["user_id"], "write")
        
        # 速率限制检查
        await rate_limiter.check_rate_limit(
            key=f"message_send:{current_user['user_id']}",
            limit=200,  # 每小时最多发送200条消息
            window=3600
        )
        
        # 准备消息数据
        message_data = {
            "session_id": session_id,
            "sender_id": current_user["user_id"],
            "agent_id": request.agent_id,
            "content": request.content,
            "message_type": request.message_type,
            "metadata": request.metadata or {},
            "reply_to_id": request.reply_to_id
        }
        
        # 发送消息
        result = await message_service.send_message(
            message_data, current_user["user_id"], client_ip
        )
        
        if result["success"]:
            message = result["data"]
            
            logger.info(f"消息发送成功", extra={
                "message_id": message["message_id"],
                "session_id": session_id,
                "message_type": message["message_type"],
                "sender_id": current_user["user_id"],
                "client_ip": client_ip
            })
            
            # 实时广播消息
            await manager.broadcast_to_session(
                json.dumps({
                    "type": "new_message",
                    "message": message,
                    "timestamp": datetime.utcnow().isoformat()
                }),
                session_id
            )
            
            return MessageResponse(**message)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except ValueError as e:
        logger.warning(f"消息发送失败: {str(e)}", extra={
            "session_id": session_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"消息发送异常: {str(e)}", extra={
            "session_id": session_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="消息发送失败，请稍后重试"
        )


@router.get("/{session_id}/messages", response_model=Dict[str, Any], summary="获取会话消息")
async def get_session_messages(
    session_id: int,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(50, ge=1, le=200, description="页面大小"),
    message_type: Optional[str] = Query(None, description="消息类型过滤"),
    sender_id: Optional[int] = Query(None, description="发送者ID过滤"),
    agent_id: Optional[int] = Query(None, description="智能体ID过滤"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取会话消息列表
    
    - **session_id**: 会话ID
    - **page**: 页码（默认1）
    - **page_size**: 页面大小（默认50，最大200）
    - **message_type**: 消息类型过滤
    - **sender_id**: 发送者ID过滤
    - **agent_id**: 智能体ID过滤
    - **start_date**: 开始日期
    - **end_date**: 结束日期
    
    需要对会话有读取权限
    """
    try:
        # 检查权限
        await check_session_permission(session_id, current_user["user_id"], "read")
        
        # 构建过滤条件
        filters = {}
        if message_type:
            filters["message_type"] = message_type
        if sender_id:
            filters["sender_id"] = sender_id
        if agent_id:
            filters["agent_id"] = agent_id
        if start_date:
            filters["start_date"] = start_date.isoformat()
        if end_date:
            filters["end_date"] = end_date.isoformat()
        
        # 获取消息列表
        result = await message_service.list_messages(
            session_id, current_user["user_id"], filters, page, page_size
        )
        
        return {
            "success": True,
            "message": "获取会话消息成功",
            "data": result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取会话消息异常: {str(e)}", extra={
            "session_id": session_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取会话消息失败"
        )


@router.post("/{session_id}/participants", response_model=ApiResponse, summary="添加参与者")
async def add_participant(
    session_id: int,
    request: ParticipantRequest,
    http_request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    添加会话参与者
    
    - **session_id**: 会话ID
    - **user_id**: 用户ID（可选）
    - **agent_id**: 智能体ID（可选）
    - **role**: 角色（owner/admin/participant/observer）
    
    需要对会话有管理权限
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 检查权限
        await check_session_permission(session_id, current_user["user_id"], "manage")
        
        # 添加参与者
        result = await session_service.add_participant(
            session_id,
            user_id=request.user_id,
            agent_id=request.agent_id,
            role=request.role,
            added_by=current_user["user_id"],
            client_ip=client_ip
        )
        
        if result["success"]:
            logger.info(f"参与者添加成功", extra={
                "session_id": session_id,
                "user_id": request.user_id,
                "agent_id": request.agent_id,
                "role": request.role,
                "added_by": current_user["user_id"],
                "client_ip": client_ip
            })
            
            # 广播参与者变更通知
            await manager.broadcast_to_session(
                json.dumps({
                    "type": "participant_added",
                    "session_id": session_id,
                    "user_id": request.user_id,
                    "agent_id": request.agent_id,
                    "role": request.role,
                    "timestamp": datetime.utcnow().isoformat()
                }),
                session_id
            )
            
            return ApiResponse(
                success=True,
                message="参与者添加成功",
                data={
                    "session_id": session_id,
                    "user_id": request.user_id,
                    "agent_id": request.agent_id,
                    "role": request.role,
                    "added_at": datetime.utcnow().isoformat()
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except ValueError as e:
        logger.warning(f"参与者添加失败: {str(e)}", extra={
            "session_id": session_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"参与者添加异常: {str(e)}", extra={
            "session_id": session_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="参与者添加失败，请稍后重试"
        )


@router.delete("/{session_id}/participants", response_model=ApiResponse, summary="移除参与者")
async def remove_participant(
    session_id: int,
    user_id: Optional[int] = Query(None, description="用户ID"),
    agent_id: Optional[int] = Query(None, description="智能体ID"),
    http_request: Request = None,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    移除会话参与者
    
    - **session_id**: 会话ID
    - **user_id**: 用户ID（可选）
    - **agent_id**: 智能体ID（可选）
    
    需要对会话有管理权限
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 检查权限
        await check_session_permission(session_id, current_user["user_id"], "manage")
        
        # 验证参数
        if not user_id and not agent_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="必须指定用户ID或智能体ID"
            )
        
        # 移除参与者
        result = await session_service.remove_participant(
            session_id,
            user_id=user_id,
            agent_id=agent_id,
            removed_by=current_user["user_id"],
            client_ip=client_ip
        )
        
        if result["success"]:
            logger.info(f"参与者移除成功", extra={
                "session_id": session_id,
                "user_id": user_id,
                "agent_id": agent_id,
                "removed_by": current_user["user_id"],
                "client_ip": client_ip
            })
            
            # 广播参与者变更通知
            await manager.broadcast_to_session(
                json.dumps({
                    "type": "participant_removed",
                    "session_id": session_id,
                    "user_id": user_id,
                    "agent_id": agent_id,
                    "timestamp": datetime.utcnow().isoformat()
                }),
                session_id
            )
            
            return ApiResponse(
                success=True,
                message="参与者移除成功",
                data={
                    "session_id": session_id,
                    "user_id": user_id,
                    "agent_id": agent_id,
                    "removed_at": datetime.utcnow().isoformat()
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"参与者移除异常: {str(e)}", extra={
            "session_id": session_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="参与者移除失败，请稍后重试"
        )


@router.get("/{session_id}/participants", response_model=Dict[str, Any], summary="获取参与者列表")
async def get_session_participants(
    session_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取会话参与者列表
    
    - **session_id**: 会话ID
    
    需要对会话有读取权限
    """
    try:
        # 检查权限
        await check_session_permission(session_id, current_user["user_id"], "read")
        
        # 获取参与者列表
        participants = await session_service.get_session_participants(
            session_id, current_user["user_id"]
        )
        
        return {
            "success": True,
            "message": "获取参与者列表成功",
            "data": {
                "session_id": session_id,
                "participants": participants,
                "total_participants": len(participants)
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取参与者列表异常: {str(e)}", extra={
            "session_id": session_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取参与者列表失败"
        )


@router.get("/{session_id}/context", response_model=Dict[str, Any], summary="获取会话上下文")
async def get_session_context(
    session_id: int,
    version: Optional[int] = Query(None, description="上下文版本"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取会话上下文
    
    - **session_id**: 会话ID
    - **version**: 上下文版本（可选，默认最新版本）
    
    需要对会话有读取权限
    """
    try:
        # 检查权限
        await check_session_permission(session_id, current_user["user_id"], "read")
        
        # 获取会话上下文
        if version:
            context = await session_service.restore_context(
                session_id, version, current_user["user_id"]
            )
        else:
            context = await session_service.get_session_context(
                session_id, current_user["user_id"]
            )
        
        return {
            "success": True,
            "message": "获取会话上下文成功",
            "data": {
                "session_id": session_id,
                "context": context,
                "version": version
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取会话上下文异常: {str(e)}", extra={
            "session_id": session_id,
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取会话上下文失败"
        )


# WebSocket接口
@router.websocket("/{session_id}/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    session_id: int,
    token: str = Query(..., description="访问令牌")
):
    """
    会话WebSocket连接
    
    - **session_id**: 会话ID
    - **token**: 访问令牌
    
    提供实时消息推送和会话状态同步
    """
    try:
        # 验证令牌
        user_info = await auth_service.verify_token(token)
        
        # 检查会话权限
        await check_session_permission(session_id, user_info["user_id"], "read")
        
        # 建立连接
        await manager.connect(websocket, session_id)
        
        logger.info(f"WebSocket连接建立", extra={
            "session_id": session_id,
            "user_id": user_info["user_id"]
        })
        
        try:
            while True:
                # 接收客户端消息
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                # 处理不同类型的消息
                if message_data.get("type") == "ping":
                    await websocket.send_text(json.dumps({
                        "type": "pong",
                        "timestamp": datetime.utcnow().isoformat()
                    }))
                elif message_data.get("type") == "typing":
                    # 广播打字状态
                    await manager.broadcast_to_session(
                        json.dumps({
                            "type": "user_typing",
                            "user_id": user_info["user_id"],
                            "session_id": session_id,
                            "timestamp": datetime.utcnow().isoformat()
                        }),
                        session_id
                    )
                
        except WebSocketDisconnect:
            manager.disconnect(websocket, session_id)
            logger.info(f"WebSocket连接断开", extra={
                "session_id": session_id,
                "user_id": user_info["user_id"]
            })
            
    except Exception as e:
        logger.error(f"WebSocket连接异常: {str(e)}", extra={
            "session_id": session_id
        })
        await websocket.close(code=1000)