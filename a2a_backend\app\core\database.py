#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统数据库连接模块

使用SQLAlchemy 2.0异步模式连接MySQL数据库
"""

import asyncio
from typing import AsyncGenerator, Optional
from sqlalchemy.ext.asyncio import (
    AsyncSession,
    AsyncEngine,
    create_async_engine,
    async_sessionmaker
)
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy.pool import NullPool
from sqlalchemy import text
from loguru import logger

from app.core.config import get_settings
from app.models.base import Base


class DatabaseManager:
    """
    数据库管理器
    
    负责数据库连接、会话管理和生命周期控制
    """
    
    def __init__(self):
        self._engine: Optional[AsyncEngine] = None
        self._session_factory: Optional[async_sessionmaker[AsyncSession]] = None
        self._initialized = False
    
    async def initialize(self) -> None:
        """
        初始化数据库连接
        """
        if self._initialized:
            return
        
        settings = get_settings()
        
        try:
            # 创建异步引擎 - 明确指定NullPool避免连接池问题
            self._engine = create_async_engine(
                settings.database_url,
                echo=settings.debug,  # 调试模式下打印SQL
                future=True,
                poolclass=NullPool  # 使用NullPool避免异步连接池问题
            )
            
            # 创建会话工厂
            self._session_factory = async_sessionmaker(
                bind=self._engine,
                class_=AsyncSession,
                expire_on_commit=False,
                autoflush=True,
                autocommit=False
            )
            
            # 测试连接
            await self._test_connection()
            
            self._initialized = True
            logger.info("数据库连接已初始化")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    async def _test_connection(self) -> None:
        """
        测试数据库连接
        """
        if not self._engine:
            raise RuntimeError("数据库引擎未初始化")
        
        try:
            async with self._engine.begin() as conn:
                result = await conn.execute(text("SELECT 1"))
                result.fetchone()
            logger.info("数据库连接测试成功")
        except Exception as e:
            logger.error(f"数据库连接测试失败: {e}")
            raise
    
    async def close(self) -> None:
        """
        关闭数据库连接
        """
        if self._engine:
            await self._engine.dispose()
            self._engine = None
            self._session_factory = None
            self._initialized = False
            logger.info("数据库连接已关闭")
    
    def get_session_factory(self) -> async_sessionmaker[AsyncSession]:
        """
        获取会话工厂
        
        Returns:
            async_sessionmaker[AsyncSession]: 会话工厂
        """
        if not self._session_factory:
            raise RuntimeError("数据库未初始化")
        return self._session_factory
    
    def get_engine(self) -> AsyncEngine:
        """
        获取数据库引擎
        
        Returns:
            AsyncEngine: 数据库引擎
        """
        if not self._engine:
            raise RuntimeError("数据库未初始化")
        return self._engine
    
    async def create_tables(self) -> None:
        """
        创建数据库表
        """
        if not self._engine:
            raise RuntimeError("数据库未初始化")
        
        try:
            async with self._engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            logger.info("数据库表创建完成")
        except Exception as e:
            logger.error(f"数据库表创建失败: {e}")
            raise
    
    async def check_and_create_tables(self) -> None:
        """
        检查数据库表是否存在，不存在则创建
        """
        if not self._engine:
            raise RuntimeError("数据库未初始化")
        
        try:
            # 确保所有模型都被导入以加载元数据
            self._import_all_models()
            
            logger.info("开始检查数据库表...")
            
            # 检查表是否存在
            async with self._engine.begin() as conn:
                # 获取数据库中现有的表
                if 'mysql' in str(self._engine.url):
                    result = await conn.execute(text("SHOW TABLES"))
                    existing_tables = {row[0] for row in result.fetchall()}
                elif 'sqlite' in str(self._engine.url):
                    result = await conn.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
                    existing_tables = {row[0] for row in result.fetchall()}
                else:
                    # PostgreSQL
                    result = await conn.execute(text("SELECT tablename FROM pg_tables WHERE schemaname='public'"))
                    existing_tables = {row[0] for row in result.fetchall()}
                
                # 获取模型中定义的表
                required_tables = set(Base.metadata.tables.keys())
                
                logger.info(f"数据库中现有表: {existing_tables}")
                logger.info(f"模型中定义的表: {required_tables}")
                
                # 检查缺失的表
                missing_tables = required_tables - existing_tables
                
                if missing_tables:
                    logger.warning(f"发现缺失的表: {missing_tables}")
                    
                    # 尝试使用SQLAlchemy创建表
                    try:
                        await conn.run_sync(Base.metadata.create_all)
                        logger.info("✅ 使用SQLAlchemy成功创建缺失的表")
                        
                        # 如果创建了用户表，则创建默认管理员账号
                        if 'users' in missing_tables:
                            await self._create_default_admin()
                            
                    except Exception as e:
                        logger.warning(f"SQLAlchemy创建表失败: {e}，尝试使用SQL脚本")
                        
                        # 如果SQLAlchemy失败，尝试执行SQL脚本
                        await self._execute_sql_script()
                        
                        # 如果创建了用户表，则创建默认管理员账号
                        if 'users' in missing_tables:
                            await self._create_default_admin()
                else:
                    logger.info("✅ 所有必需的表都已存在")
                    
                    # 即使表已存在，也检查是否需要创建默认管理员账号
                    await self._create_default_admin()
                    
        except Exception as e:
            logger.error(f"检查和创建数据库表失败: {e}")
            raise
    
    def _import_all_models(self) -> None:
        """
        导入所有模型以确保元数据完整
        """
        try:
            # 导入所有模型模块
            import app.models.user
            import app.models.agent
            import app.models.session
            import app.models.message
            import app.models.task
            import app.models.memory
            import app.models.workflow
            import app.models.tool
            import app.models.artifact
            import app.models.config
            import app.models.log
        except ImportError as e:
            logger.warning(f"导入模型时出现警告: {e}")
    
    async def _execute_sql_script(self) -> None:
        """
        执行SQL脚本创建表
        """
        import os
        from pathlib import Path
        
        # 查找SQL脚本文件
        script_paths = [
            "scripts/db.sql",
            "../scripts/db.sql",
            "../../scripts/db.sql"
        ]
        
        sql_file = None
        for path in script_paths:
            if os.path.exists(path):
                sql_file = path
                break
        
        if not sql_file:
            logger.error("未找到数据库初始化SQL脚本")
            raise FileNotFoundError("数据库初始化SQL脚本不存在")
        
        logger.info(f"执行SQL脚本: {sql_file}")
        await self.execute_sql_file(sql_file)
    
    async def _create_default_admin(self) -> None:
        """
        创建默认管理员账号
        """
        try:
            from app.models.user import User
            
            # 使用数据库会话创建默认管理员
            async with self.get_session() as session:
                await User.create_default_admin(session)
                
        except Exception as e:
            logger.error(f"创建默认管理员账号失败: {e}")
            # 不抛出异常，避免影响数据库初始化流程
            pass
    
    def get_session(self) -> AsyncSession:
        """
        获取数据库会话

        Returns:
            AsyncSession: 数据库会话
        """
        if not self._session_factory:
            raise RuntimeError("数据库未初始化")

        return self._session_factory()
    
    async def drop_tables(self) -> None:
        """
        删除数据库表
        """
        if not self._engine:
            raise RuntimeError("数据库未初始化")
        
        try:
            async with self._engine.begin() as conn:
                await conn.run_sync(Base.metadata.drop_all)
            logger.info("数据库表删除完成")
        except Exception as e:
            logger.error(f"数据库表删除失败: {e}")
            raise
    
    async def execute_sql_file(self, file_path: str) -> None:
        """
        执行SQL文件
        
        Args:
            file_path: SQL文件路径
        """
        if not self._engine:
            raise RuntimeError("数据库未初始化")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # 分割SQL语句
            statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            
            async with self._engine.begin() as conn:
                for statement in statements:
                    if statement:
                        await conn.execute(text(statement))
            
            logger.info(f"SQL文件执行完成: {file_path}")
        except Exception as e:
            logger.error(f"SQL文件执行失败: {e}")
            raise


# 全局数据库管理器实例
_db_manager: Optional[DatabaseManager] = None


def get_database_manager() -> DatabaseManager:
    """
    获取数据库管理器实例（单例模式）
    
    Returns:
        DatabaseManager: 数据库管理器实例
    """
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager()
    return _db_manager


async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """
    获取异步数据库会话（依赖注入用）
    
    Yields:
        AsyncSession: 异步数据库会话
    """
    db_manager = get_database_manager()
    session_factory = db_manager.get_session_factory()
    
    async with session_factory() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def init_database() -> None:
    """
    初始化数据库连接并检查表
    """
    try:
        db_manager = get_database_manager()
        await db_manager.initialize()
        logger.info("数据库初始化完成")
        
        # 自动检查和创建数据库表
        await db_manager.check_and_create_tables()
        logger.info("数据库表检查完成")
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise


async def close_database() -> None:
    """
    关闭数据库连接
    """
    db_manager = get_database_manager()
    await db_manager.close()


async def create_tables() -> None:
    """
    创建数据库表
    """
    db_manager = get_database_manager()
    await db_manager.create_tables()


async def execute_schema_file(schema_file: str = "scripts/a2a_database_schema.sql") -> None:
    """
    执行数据库架构文件
    
    Args:
        schema_file: 架构文件路径
    """
    db_manager = get_database_manager()
    await db_manager.execute_sql_file(schema_file)


class DatabaseHealthCheck:
    """
    数据库健康检查
    """
    
    @staticmethod
    async def check_connection() -> bool:
        """
        检查数据库连接状态
        
        Returns:
            bool: 连接是否正常
        """
        try:
            db_manager = get_database_manager()
            engine = db_manager.get_engine()
            
            async with engine.begin() as conn:
                result = await conn.execute(text("SELECT 1"))
                await result.fetchone()
            
            return True
        except Exception as e:
            logger.error(f"数据库健康检查失败: {e}")
            return False
    
    @staticmethod
    async def get_connection_info() -> dict:
        """
        获取数据库连接信息
        
        Returns:
            dict: 连接信息
        """
        try:
            db_manager = get_database_manager()
            engine = db_manager.get_engine()
            
            pool = engine.pool
            
            return {
                "pool_size": pool.size(),
                "checked_in": pool.checkedin(),
                "checked_out": pool.checkedout(),
                "overflow": pool.overflow(),
                "invalid": pool.invalid()
            }
        except Exception as e:
            logger.error(f"获取数据库连接信息失败: {e}")
            return {}


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    获取数据库会话的依赖注入函数
    
    用于FastAPI的依赖注入系统
    
    Yields:
        AsyncSession: 数据库会话
    """
    db_manager = get_database_manager()
    async with db_manager.get_session() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()