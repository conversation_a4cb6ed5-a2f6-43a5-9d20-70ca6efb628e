# -*- coding: utf-8 -*-
"""
A2A多智能体系统工作流执行器

提供不同类型的工作流执行器
"""

import logging
import asyncio
import json
import uuid
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime, timedelta
from abc import ABC, abstractmethod
from sqlalchemy.orm import Session

from adk.agents.sequential_agent import SequentialAgent
from adk.agents.parallel_agent import ParallelAgent
from adk.agents.loop_agent import LoopAgent
from adk.agents.branch_agent import BranchAgent
from adk.agents.workflow_agent import WorkflowAgent

from app.models.agent import Agent
from app.core.logging import get_logger
from app.core.config import get_settings


class BaseExecutor(ABC):
    """
    执行器基类
    """
    
    def __init__(self, db, logger: Optional[logging.Logger] = None):
        """
        初始化执行器
        
        Args:
            db: 数据库会话
            logger: 日志记录器
        """
        self.db = db
        self.logger = logger or get_logger(self.__class__.__name__)
        self.settings = get_settings()
    
    @abstractmethod
    async def execute_node(
        self,
        execution_id: str,
        node: Dict[str, Any],
        context: Dict[str, Any]
    ) -> bool:
        """
        执行节点
        
        Args:
            execution_id: 执行ID
            node: 节点定义
            context: 执行上下文
            
        Returns:
            bool: 是否执行成功
        """
        pass
    
    async def _create_agent_instance(
        self,
        agent_id: int,
        user_id: int,
        config: Optional[Dict[str, Any]] = None
    ) -> Optional[WorkflowAgent]:
        """
        创建智能体实例
        
        Args:
            agent_id: 智能体ID
            user_id: 用户ID
            config: 配置信息
            
        Returns:
            Optional[WorkflowAgent]: 智能体实例
        """
        try:
            agent = self.db.query(Agent).filter(Agent.id == agent_id).first()
            if not agent:
                self.logger.error(f"智能体不存在: {agent_id}")
                return None
            
            # 合并配置
            agent_config = json.loads(agent.config or "{}")
            if config:
                agent_config.update(config)
            
            # 创建工作流智能体实例
            workflow_agent = WorkflowAgent(
                agent_id=agent.agent_id,
                name=agent.name,
                description=agent.description,
                config=agent_config,
                user_id=user_id
            )
            
            return workflow_agent
        except Exception as e:
            self.logger.error(f"创建智能体实例错误: {str(e)}")
            return None
    
    async def _execute_agent_task(
        self,
        agent: WorkflowAgent,
        input_data: Dict[str, Any],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        执行智能体任务
        
        Args:
            agent: 智能体实例
            input_data: 输入数据
            context: 执行上下文
            
        Returns:
            Dict[str, Any]: 执行结果
        """
        try:
            result = await agent.execute(
                input_data=input_data,
                context=context
            )
            return result
        except Exception as e:
            self.logger.error(f"执行智能体任务错误: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "output": None
            }


class SequentialExecutor(BaseExecutor):
    """
    顺序执行器
    
    按步骤顺序执行智能体
    """
    
    async def execute_node(
        self,
        execution_id: str,
        node: Dict[str, Any],
        context: Dict[str, Any]
    ) -> bool:
        """
        执行顺序节点
        
        Args:
            execution_id: 执行ID
            node: 节点定义
            context: 执行上下文
            
        Returns:
            bool: 是否执行成功
        """
        try:
            node_id = node.get("id")
            config = node.get("config", {})
            steps = config.get("steps", [])
            
            self.logger.info(f"开始顺序执行节点: {node_id}, 步骤数: {len(steps)}")
            
            # 创建顺序智能体
            sequential_agent = SequentialAgent(
                agent_id=f"sequential_{node_id}",
                name=f"Sequential Agent for {node_id}",
                description="顺序执行智能体",
                config=config,
                user_id=context.get("user_id")
            )
            
            # 准备输入数据
            input_data = context.get("input_data", {})
            step_results = []
            
            # 顺序执行每个步骤
            for step_index, step in enumerate(steps):
                step_id = step.get("id", f"step_{step_index}")
                agent_id = step.get("agent_id")
                step_config = step.get("config", {})
                
                self.logger.info(f"执行步骤: {step_id} (智能体: {agent_id})")
                
                if agent_id:
                    # 创建步骤智能体实例
                    step_agent = await self._create_agent_instance(
                        agent_id,
                        context.get("user_id"),
                        step_config
                    )
                    
                    if not step_agent:
                        self.logger.error(f"无法创建步骤智能体: {agent_id}")
                        return False
                    
                    # 准备步骤输入数据
                    step_input = input_data.copy()
                    if step_results:
                        step_input["previous_results"] = step_results
                        step_input["previous_output"] = step_results[-1].get("output")
                    
                    # 执行步骤
                    step_result = await self._execute_agent_task(
                        step_agent,
                        step_input,
                        context
                    )
                    
                    step_results.append({
                        "step_id": step_id,
                        "agent_id": agent_id,
                        "success": step_result.get("success", False),
                        "output": step_result.get("output"),
                        "error": step_result.get("error")
                    })
                    
                    # 检查步骤是否成功
                    if not step_result.get("success", False):
                        if not step.get("continue_on_failure", False):
                            self.logger.error(f"步骤执行失败，停止顺序执行: {step_id}")
                            return False
                    
                    # 更新输入数据为下一步
                    if step_result.get("success", False) and step_result.get("output"):
                        input_data.update(step_result.get("output", {}))
                else:
                    # 简单步骤（无智能体）
                    step_type = step.get("type", "noop")
                    
                    if step_type == "delay":
                        delay = step.get("delay", 1.0)
                        await asyncio.sleep(delay)
                    
                    step_results.append({
                        "step_id": step_id,
                        "type": step_type,
                        "success": True,
                        "output": {"message": f"步骤 {step_id} 完成"}
                    })
            
            # 更新上下文
            context["sequential_results"] = step_results
            context["output"] = {
                "success": True,
                "steps_completed": len(step_results),
                "final_output": step_results[-1].get("output") if step_results else None
            }
            
            self.logger.info(f"顺序执行节点完成: {node_id}")
            return True
        except Exception as e:
            self.logger.error(f"顺序执行节点错误: {str(e)}")
            return False


class ParallelExecutor(BaseExecutor):
    """
    并行执行器
    
    支持多智能体并行执行
    """
    
    async def execute_node(
        self,
        execution_id: str,
        node: Dict[str, Any],
        context: Dict[str, Any]
    ) -> bool:
        """
        执行并行节点
        
        Args:
            execution_id: 执行ID
            node: 节点定义
            context: 执行上下文
            
        Returns:
            bool: 是否执行成功
        """
        try:
            node_id = node.get("id")
            config = node.get("config", {})
            parallel_tasks = config.get("parallel_tasks", [])
            max_concurrency = config.get("max_concurrency", 5)
            wait_for_all = config.get("wait_for_all", True)
            
            self.logger.info(f"开始并行执行节点: {node_id}, 任务数: {len(parallel_tasks)}")
            
            # 创建并行智能体
            parallel_agent = ParallelAgent(
                agent_id=f"parallel_{node_id}",
                name=f"Parallel Agent for {node_id}",
                description="并行执行智能体",
                config=config,
                user_id=context.get("user_id")
            )
            
            # 准备输入数据
            input_data = context.get("input_data", {})
            
            # 创建并行任务
            tasks = []
            semaphore = asyncio.Semaphore(max_concurrency)
            
            async def execute_parallel_task(task_def: Dict[str, Any]) -> Dict[str, Any]:
                async with semaphore:
                    task_id = task_def.get("id", str(uuid.uuid4()))
                    agent_id = task_def.get("agent_id")
                    task_config = task_def.get("config", {})
                    task_input = task_def.get("input", {})
                    
                    try:
                        if agent_id:
                            # 创建任务智能体实例
                            task_agent = await self._create_agent_instance(
                                agent_id,
                                context.get("user_id"),
                                task_config
                            )
                            
                            if not task_agent:
                                return {
                                    "task_id": task_id,
                                    "success": False,
                                    "error": f"无法创建智能体: {agent_id}"
                                }
                            
                            # 准备任务输入数据
                            merged_input = input_data.copy()
                            merged_input.update(task_input)
                            
                            # 执行任务
                            result = await self._execute_agent_task(
                                task_agent,
                                merged_input,
                                context
                            )
                            
                            return {
                                "task_id": task_id,
                                "agent_id": agent_id,
                                "success": result.get("success", False),
                                "output": result.get("output"),
                                "error": result.get("error")
                            }
                        else:
                            # 简单任务
                            task_type = task_def.get("type", "noop")
                            
                            if task_type == "delay":
                                delay = task_def.get("delay", 1.0)
                                await asyncio.sleep(delay)
                            
                            return {
                                "task_id": task_id,
                                "type": task_type,
                                "success": True,
                                "output": {"message": f"任务 {task_id} 完成"}
                            }
                    except Exception as e:
                        return {
                            "task_id": task_id,
                            "success": False,
                            "error": str(e)
                        }
            
            # 启动所有并行任务
            for task_def in parallel_tasks:
                task = asyncio.create_task(execute_parallel_task(task_def))
                tasks.append(task)
            
            # 等待任务完成
            if wait_for_all:
                # 等待所有任务完成
                results = await asyncio.gather(*tasks, return_exceptions=True)
            else:
                # 等待第一个成功的任务
                done, pending = await asyncio.wait(
                    tasks,
                    return_when=asyncio.FIRST_COMPLETED
                )
                
                # 取消剩余任务
                for task in pending:
                    task.cancel()
                
                results = [task.result() for task in done]
            
            # 处理结果
            task_results = []
            successful_tasks = 0
            failed_tasks = 0
            
            for result in results:
                if isinstance(result, Exception):
                    task_results.append({
                        "success": False,
                        "error": str(result)
                    })
                    failed_tasks += 1
                else:
                    task_results.append(result)
                    if result.get("success", False):
                        successful_tasks += 1
                    else:
                        failed_tasks += 1
            
            # 判断整体执行是否成功
            success = successful_tasks > 0
            if wait_for_all:
                success = failed_tasks == 0
            
            # 更新上下文
            context["parallel_results"] = task_results
            context["output"] = {
                "success": success,
                "successful_tasks": successful_tasks,
                "failed_tasks": failed_tasks,
                "total_tasks": len(parallel_tasks),
                "results": task_results
            }
            
            self.logger.info(f"并行执行节点完成: {node_id}, 成功: {successful_tasks}, 失败: {failed_tasks}")
            return success
        except Exception as e:
            self.logger.error(f"并行执行节点错误: {str(e)}")
            return False


class LoopExecutor(BaseExecutor):
    """
    循环执行器
    
    支持条件循环和迭代
    """
    
    async def execute_node(
        self,
        execution_id: str,
        node: Dict[str, Any],
        context: Dict[str, Any]
    ) -> bool:
        """
        执行循环节点
        
        Args:
            execution_id: 执行ID
            node: 节点定义
            context: 执行上下文
            
        Returns:
            bool: 是否执行成功
        """
        try:
            node_id = node.get("id")
            config = node.get("config", {})
            loop_type = config.get("loop_type", "while")  # while, for, until
            max_iterations = config.get("max_iterations", 100)
            condition = config.get("condition", {})
            loop_body = config.get("loop_body", {})
            
            self.logger.info(f"开始循环执行节点: {node_id}, 类型: {loop_type}")
            
            # 创建循环智能体
            loop_agent = LoopAgent(
                agent_id=f"loop_{node_id}",
                name=f"Loop Agent for {node_id}",
                description="循环执行智能体",
                config=config,
                user_id=context.get("user_id")
            )
            
            # 准备输入数据
            input_data = context.get("input_data", {})
            iteration_results = []
            iteration_count = 0
            
            # 执行循环
            while iteration_count < max_iterations:
                # 检查循环条件
                should_continue = await self._evaluate_loop_condition(
                    loop_type,
                    condition,
                    iteration_count,
                    iteration_results,
                    input_data
                )
                
                if not should_continue:
                    break
                
                self.logger.info(f"执行循环迭代: {iteration_count + 1}")
                
                # 执行循环体
                iteration_result = await self._execute_loop_body(
                    loop_body,
                    iteration_count,
                    input_data,
                    context
                )
                
                iteration_results.append({
                    "iteration": iteration_count,
                    "success": iteration_result.get("success", False),
                    "output": iteration_result.get("output"),
                    "error": iteration_result.get("error")
                })
                
                # 检查迭代是否成功
                if not iteration_result.get("success", False):
                    if not config.get("continue_on_failure", False):
                        self.logger.error(f"循环迭代失败，停止执行: {iteration_count}")
                        break
                
                # 更新输入数据
                if iteration_result.get("output"):
                    input_data.update(iteration_result.get("output", {}))
                
                iteration_count += 1
            
            # 判断执行是否成功
            successful_iterations = sum(1 for r in iteration_results if r.get("success", False))
            success = successful_iterations > 0
            
            # 更新上下文
            context["loop_results"] = iteration_results
            context["output"] = {
                "success": success,
                "total_iterations": iteration_count,
                "successful_iterations": successful_iterations,
                "final_output": iteration_results[-1].get("output") if iteration_results else None
            }
            
            self.logger.info(f"循环执行节点完成: {node_id}, 迭代次数: {iteration_count}")
            return success
        except Exception as e:
            self.logger.error(f"循环执行节点错误: {str(e)}")
            return False
    
    async def _evaluate_loop_condition(
        self,
        loop_type: str,
        condition: Dict[str, Any],
        iteration_count: int,
        iteration_results: List[Dict[str, Any]],
        input_data: Dict[str, Any]
    ) -> bool:
        """
        评估循环条件
        
        Args:
            loop_type: 循环类型
            condition: 条件定义
            iteration_count: 当前迭代次数
            iteration_results: 迭代结果
            input_data: 输入数据
            
        Returns:
            bool: 是否继续循环
        """
        try:
            if loop_type == "for":
                # for循环：基于次数
                max_count = condition.get("count", 1)
                return iteration_count < max_count
            elif loop_type == "while":
                # while循环：基于条件
                return await self._evaluate_condition_expression(
                    condition,
                    iteration_count,
                    iteration_results,
                    input_data
                )
            elif loop_type == "until":
                # until循环：直到条件为真
                result = await self._evaluate_condition_expression(
                    condition,
                    iteration_count,
                    iteration_results,
                    input_data
                )
                return not result
            else:
                return False
        except Exception as e:
            self.logger.error(f"评估循环条件错误: {str(e)}")
            return False
    
    async def _evaluate_condition_expression(
        self,
        condition: Dict[str, Any],
        iteration_count: int,
        iteration_results: List[Dict[str, Any]],
        input_data: Dict[str, Any]
    ) -> bool:
        """
        评估条件表达式
        
        Args:
            condition: 条件定义
            iteration_count: 当前迭代次数
            iteration_results: 迭代结果
            input_data: 输入数据
            
        Returns:
            bool: 条件结果
        """
        try:
            condition_type = condition.get("type", "simple")
            
            if condition_type == "simple":
                # 简单条件
                field = condition.get("field")
                operator = condition.get("operator", "eq")
                value = condition.get("value")
                
                if field in input_data:
                    field_value = input_data[field]
                    
                    if operator == "eq":
                        return field_value == value
                    elif operator == "ne":
                        return field_value != value
                    elif operator == "gt":
                        return field_value > value
                    elif operator == "lt":
                        return field_value < value
                    elif operator == "gte":
                        return field_value >= value
                    elif operator == "lte":
                        return field_value <= value
                    elif operator == "in":
                        return field_value in value
                    elif operator == "contains":
                        return value in field_value
                
                return False
            elif condition_type == "iteration_based":
                # 基于迭代的条件
                max_iterations = condition.get("max_iterations", 10)
                return iteration_count < max_iterations
            elif condition_type == "result_based":
                # 基于结果的条件
                if not iteration_results:
                    return True
                
                last_result = iteration_results[-1]
                success_required = condition.get("success_required", True)
                
                if success_required:
                    return last_result.get("success", False)
                else:
                    return not last_result.get("success", False)
            else:
                return True
        except Exception as e:
            self.logger.error(f"评估条件表达式错误: {str(e)}")
            return False
    
    async def _execute_loop_body(
        self,
        loop_body: Dict[str, Any],
        iteration_count: int,
        input_data: Dict[str, Any],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        执行循环体
        
        Args:
            loop_body: 循环体定义
            iteration_count: 迭代次数
            input_data: 输入数据
            context: 执行上下文
            
        Returns:
            Dict[str, Any]: 执行结果
        """
        try:
            agent_id = loop_body.get("agent_id")
            body_config = loop_body.get("config", {})
            
            if agent_id:
                # 创建循环体智能体实例
                body_agent = await self._create_agent_instance(
                    agent_id,
                    context.get("user_id"),
                    body_config
                )
                
                if not body_agent:
                    return {
                        "success": False,
                        "error": f"无法创建循环体智能体: {agent_id}"
                    }
                
                # 准备循环体输入数据
                body_input = input_data.copy()
                body_input["iteration_count"] = iteration_count
                
                # 执行循环体
                result = await self._execute_agent_task(
                    body_agent,
                    body_input,
                    context
                )
                
                return result
            else:
                # 简单循环体
                body_type = loop_body.get("type", "noop")
                
                if body_type == "delay":
                    delay = loop_body.get("delay", 1.0)
                    await asyncio.sleep(delay)
                
                return {
                    "success": True,
                    "output": {"iteration": iteration_count, "message": "循环体执行完成"}
                }
        except Exception as e:
            self.logger.error(f"执行循环体错误: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }


class BranchExecutor(BaseExecutor):
    """
    分支执行器
    
    支持条件分支和决策
    """
    
    async def execute_node(
        self,
        execution_id: str,
        node: Dict[str, Any],
        context: Dict[str, Any]
    ) -> bool:
        """
        执行分支节点
        
        Args:
            execution_id: 执行ID
            node: 节点定义
            context: 执行上下文
            
        Returns:
            bool: 是否执行成功
        """
        try:
            node_id = node.get("id")
            config = node.get("config", {})
            branches = config.get("branches", [])
            default_branch = config.get("default_branch")
            
            self.logger.info(f"开始分支执行节点: {node_id}, 分支数: {len(branches)}")
            
            # 创建分支智能体
            branch_agent = BranchAgent(
                agent_id=f"branch_{node_id}",
                name=f"Branch Agent for {node_id}",
                description="分支执行智能体",
                config=config,
                user_id=context.get("user_id")
            )
            
            # 准备输入数据
            input_data = context.get("input_data", {})
            
            # 评估分支条件
            selected_branch = None
            for branch in branches:
                branch_id = branch.get("id")
                condition = branch.get("condition", {})
                
                self.logger.info(f"评估分支条件: {branch_id}")
                
                if await self._evaluate_branch_condition(condition, input_data, context):
                    selected_branch = branch
                    self.logger.info(f"选择分支: {branch_id}")
                    break
            
            # 如果没有匹配的分支，使用默认分支
            if not selected_branch and default_branch:
                selected_branch = default_branch
                self.logger.info("使用默认分支")
            
            if not selected_branch:
                self.logger.warning("没有匹配的分支")
                return False
            
            # 执行选中的分支
            branch_result = await self._execute_branch(
                selected_branch,
                input_data,
                context
            )
            
            # 更新上下文
            context["branch_result"] = branch_result
            context["selected_branch"] = selected_branch.get("id")
            context["output"] = {
                "success": branch_result.get("success", False),
                "selected_branch": selected_branch.get("id"),
                "output": branch_result.get("output")
            }
            
            success = branch_result.get("success", False)
            self.logger.info(f"分支执行节点完成: {node_id}, 成功: {success}")
            return success
        except Exception as e:
            self.logger.error(f"分支执行节点错误: {str(e)}")
            return False
    
    async def _evaluate_branch_condition(
        self,
        condition: Dict[str, Any],
        input_data: Dict[str, Any],
        context: Dict[str, Any]
    ) -> bool:
        """
        评估分支条件
        
        Args:
            condition: 条件定义
            input_data: 输入数据
            context: 执行上下文
            
        Returns:
            bool: 条件结果
        """
        try:
            condition_type = condition.get("type", "simple")
            
            if condition_type == "simple":
                # 简单条件
                field = condition.get("field")
                operator = condition.get("operator", "eq")
                value = condition.get("value")
                
                if field in input_data:
                    field_value = input_data[field]
                    
                    if operator == "eq":
                        return field_value == value
                    elif operator == "ne":
                        return field_value != value
                    elif operator == "gt":
                        return field_value > value
                    elif operator == "lt":
                        return field_value < value
                    elif operator == "gte":
                        return field_value >= value
                    elif operator == "lte":
                        return field_value <= value
                    elif operator == "in":
                        return field_value in value
                    elif operator == "contains":
                        return value in field_value
                
                return False
            elif condition_type == "complex":
                # 复杂条件（AND/OR逻辑）
                logic = condition.get("logic", "and")
                sub_conditions = condition.get("conditions", [])
                
                results = []
                for sub_condition in sub_conditions:
                    result = await self._evaluate_branch_condition(
                        sub_condition,
                        input_data,
                        context
                    )
                    results.append(result)
                
                if logic == "and":
                    return all(results)
                elif logic == "or":
                    return any(results)
                else:
                    return False
            elif condition_type == "always":
                # 总是为真
                return True
            elif condition_type == "never":
                # 总是为假
                return False
            else:
                return False
        except Exception as e:
            self.logger.error(f"评估分支条件错误: {str(e)}")
            return False
    
    async def _execute_branch(
        self,
        branch: Dict[str, Any],
        input_data: Dict[str, Any],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        执行分支
        
        Args:
            branch: 分支定义
            input_data: 输入数据
            context: 执行上下文
            
        Returns:
            Dict[str, Any]: 执行结果
        """
        try:
            branch_id = branch.get("id")
            agent_id = branch.get("agent_id")
            branch_config = branch.get("config", {})
            
            if agent_id:
                # 创建分支智能体实例
                branch_agent = await self._create_agent_instance(
                    agent_id,
                    context.get("user_id"),
                    branch_config
                )
                
                if not branch_agent:
                    return {
                        "success": False,
                        "error": f"无法创建分支智能体: {agent_id}"
                    }
                
                # 执行分支
                result = await self._execute_agent_task(
                    branch_agent,
                    input_data,
                    context
                )
                
                return result
            else:
                # 简单分支
                branch_type = branch.get("type", "noop")
                
                if branch_type == "delay":
                    delay = branch.get("delay", 1.0)
                    await asyncio.sleep(delay)
                
                return {
                    "success": True,
                    "output": {"branch_id": branch_id, "message": "分支执行完成"}
                }
        except Exception as e:
            self.logger.error(f"执行分支错误: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }