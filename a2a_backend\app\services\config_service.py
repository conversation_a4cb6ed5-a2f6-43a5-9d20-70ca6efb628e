# -*- coding: utf-8 -*-
"""
A2A多智能体系统配置服务

提供系统配置、用户配置、LLM配置和工具配置的管理功能
"""

import json
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc
from sqlalchemy.exc import IntegrityError
from fastapi import HTTPException, status
from cachetools import TTLCache
import threading

from app.models.config import SystemConfig, UserConfig, AgentConfig, ConfigTemplate, LLMConfig, ToolConfig
from app.models.user import User
from app.core.database import get_db
from app.core.logging import get_logger
from app.core.config import get_settings
from app.core.cache import get_cache_manager, CacheLevel
from app.core.storage import get_database_manager
from app.auth.dependencies import get_current_user

logger = get_logger(__name__)


class ConfigService:
    """
    配置服务类
    
    提供配置的读取、更新、缓存和权限管理功能
    """
    
    def __init__(self, db = None):
        """
        初始化配置服务
        
        Args:
            db: 数据库会话
        """
        self.db = db or next(get_db())
        self.logger = get_logger(self.__class__.__name__)
        self.settings = get_settings()
        self.cache_manager = get_cache_manager()
        self.db_manager = get_database_manager()
        
        # 配置缓存（TTL缓存，5分钟过期）
        self._config_cache = TTLCache(maxsize=1000, ttl=300)
        self._cache_lock = threading.RLock()
        
        self.logger.info("配置服务初始化完成")
    
    # ==================== 系统配置管理 ====================
    
    async def get_system_config(
        self, 
        config_key: str, 
        default_value: Any = None,
        user_id: Optional[int] = None
    ) -> Any:
        """
        获取系统配置
        
        Args:
            config_key: 配置键
            default_value: 默认值
            user_id: 用户ID（用于权限验证）
            
        Returns:
            配置值
            
        Raises:
            HTTPException: 权限不足或配置不存在
        """
        try:
            # 检查缓存
            cache_key = f"system_config:{config_key}"
            cached_value = await self.cache_manager.get(cache_key)
            if cached_value is not None:
                self.logger.debug(f"从缓存获取系统配置: {config_key}")
                return cached_value
            
            # 从数据库查询
            config = self.db.query(SystemConfig).filter(
                SystemConfig.config_key == config_key
            ).first()
            
            if not config:
                if default_value is not None:
                    return default_value
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"系统配置不存在: {config_key}"
                )
            
            # 权限检查（敏感配置需要管理员权限）
            if config.is_sensitive and user_id:
                user = self.db.query(User).filter(User.id == user_id).first()
                if not user or user.role != "admin":
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="访问敏感配置需要管理员权限"
                    )
            
            # 解析配置值
            value = self._parse_config_value(config.config_value, config.value_type)
            
            # 缓存配置
            await self.cache_manager.set(
                cache_key, 
                value, 
                ttl=300,
                tags=["system_config"],
                levels=[CacheLevel.MEMORY]
            )
            
            self.logger.info(f"获取系统配置成功: {config_key}")
            return value
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"获取系统配置失败: {config_key}, 错误: {str(e)}")
            if default_value is not None:
                return default_value
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取系统配置失败: {str(e)}"
            )
    
    async def set_system_config(
        self, 
        config_key: str, 
        config_value: Any,
        user_id: int,
        description: Optional[str] = None,
        category: str = "general",
        value_type: str = "string",
        is_sensitive: bool = False,
        is_readonly: bool = False
    ) -> SystemConfig:
        """
        设置系统配置
        
        Args:
            config_key: 配置键
            config_value: 配置值
            user_id: 用户ID
            description: 配置描述
            category: 配置分类
            value_type: 值类型
            is_sensitive: 是否敏感信息
            is_readonly: 是否只读
            
        Returns:
            系统配置对象
            
        Raises:
            HTTPException: 权限不足或配置错误
        """
        try:
            # 权限检查（需要管理员权限）
            user = self.db.query(User).filter(User.id == user_id).first()
            if not user or user.role != "admin":
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="设置系统配置需要管理员权限"
                )
            
            # 检查是否为只读配置
            existing_config = self.db.query(SystemConfig).filter(
                SystemConfig.config_key == config_key
            ).first()
            
            if existing_config and existing_config.is_readonly:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="只读配置不能修改"
                )
            
            # 验证配置值
            validated_value = self._validate_config_value(
                config_value, value_type, existing_config.validation_rule if existing_config else None
            )
            
            # 序列化配置值
            serialized_value = self._serialize_config_value(validated_value, value_type)
            
            if existing_config:
                # 更新现有配置
                existing_config.config_value = serialized_value
                existing_config.value_type = value_type
                existing_config.category = category
                existing_config.description = description or existing_config.description
                existing_config.is_sensitive = is_sensitive
                existing_config.is_readonly = is_readonly
                existing_config.updated_by = user_id
                existing_config.updated_at = datetime.utcnow()
                config = existing_config
            else:
                # 创建新配置
                config = SystemConfig(
                    config_key=config_key,
                    config_value=serialized_value,
                    value_type=value_type,
                    category=category,
                    description=description,
                    is_sensitive=is_sensitive,
                    is_readonly=is_readonly,
                    updated_by=user_id
                )
                self.db.add(config)
            
            self.db.commit()
            
            # 清除缓存
            cache_key = f"system_config:{config_key}"
            await self.cache_manager.delete(cache_key)
            
            self.logger.info(f"设置系统配置成功: {config_key}")
            return config
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"设置系统配置失败: {config_key}, 错误: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"设置系统配置失败: {str(e)}"
            )
    
    async def list_system_configs(
        self,
        user_id: int,
        category: Optional[str] = None,
        include_sensitive: bool = False,
        skip: int = 0,
        limit: int = 100
    ) -> List[SystemConfig]:
        """
        列出系统配置
        
        Args:
            user_id: 用户ID
            category: 配置分类过滤
            include_sensitive: 是否包含敏感配置
            skip: 跳过数量
            limit: 限制数量
            
        Returns:
            系统配置列表
            
        Raises:
            HTTPException: 权限不足
        """
        try:
            # 权限检查
            user = self.db.query(User).filter(User.id == user_id).first()
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="用户不存在"
                )
            
            # 构建查询
            query = self.db.query(SystemConfig)
            
            # 分类过滤
            if category:
                query = query.filter(SystemConfig.category == category)
            
            # 敏感配置过滤
            if not include_sensitive or user.role != "admin":
                query = query.filter(SystemConfig.is_sensitive == False)
            
            # 分页
            configs = query.offset(skip).limit(limit).all()
            
            self.logger.info(f"列出系统配置成功，数量: {len(configs)}")
            return configs
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"列出系统配置失败，错误: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"列出系统配置失败: {str(e)}"
            )
    
    # ==================== 用户配置管理 ====================
    
    async def get_user_config(
        self,
        user_id: int,
        config_key: str,
        default_value: Any = None
    ) -> Any:
        """
        获取用户配置
        
        Args:
            user_id: 用户ID
            config_key: 配置键
            default_value: 默认值
            
        Returns:
            配置值
        """
        try:
            # 检查缓存
            cache_key = f"user_config:{user_id}:{config_key}"
            cached_value = await self.cache_manager.get(cache_key)
            if cached_value is not None:
                self.logger.debug(f"从缓存获取用户配置: {user_id}:{config_key}")
                return cached_value
            
            # 从数据库查询
            config = self.db.query(UserConfig).filter(
                and_(
                    UserConfig.user_id == user_id,
                    UserConfig.config_key == config_key
                )
            ).first()
            
            if not config:
                if default_value is not None:
                    return default_value
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"用户配置不存在: {config_key}"
                )
            
            # 解析配置值
            value = self._parse_config_value(config.config_value, config.value_type)
            
            # 缓存配置
            await self.cache_manager.set(
                cache_key, 
                value, 
                ttl=300,
                tags=["user_config", f"user_{user_id}"],
                levels=[CacheLevel.MEMORY]
            )
            
            self.logger.info(f"获取用户配置成功: {user_id}:{config_key}")
            return value
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"获取用户配置失败: {user_id}:{config_key}, 错误: {str(e)}")
            if default_value is not None:
                return default_value
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取用户配置失败: {str(e)}"
            )
    
    async def set_user_config(
        self,
        user_id: int,
        config_key: str,
        config_value: Any,
        value_type: str = "string",
        description: Optional[str] = None
    ) -> UserConfig:
        """
        设置用户配置
        
        Args:
            user_id: 用户ID
            config_key: 配置键
            config_value: 配置值
            value_type: 值类型
            description: 配置描述
            
        Returns:
            用户配置对象
        """
        try:
            # 验证用户存在
            user = self.db.query(User).filter(User.id == user_id).first()
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="用户不存在"
                )
            
            # 验证配置值
            validated_value = self._validate_config_value(config_value, value_type)
            
            # 序列化配置值
            serialized_value = self._serialize_config_value(validated_value, value_type)
            
            # 查找现有配置
            existing_config = self.db.query(UserConfig).filter(
                and_(
                    UserConfig.user_id == user_id,
                    UserConfig.config_key == config_key
                )
            ).first()
            
            if existing_config:
                # 更新现有配置
                existing_config.config_value = serialized_value
                existing_config.value_type = value_type
                existing_config.description = description or existing_config.description
                existing_config.updated_at = datetime.utcnow()
                config = existing_config
            else:
                # 创建新配置
                config = UserConfig(
                    user_id=user_id,
                    config_key=config_key,
                    config_value=serialized_value,
                    value_type=value_type,
                    description=description
                )
                self.db.add(config)
            
            self.db.commit()
            
            # 清除缓存
            cache_key = f"user_config:{user_id}:{config_key}"
            await self.cache_manager.delete(cache_key)
            
            self.logger.info(f"设置用户配置成功: {user_id}:{config_key}")
            return config
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"设置用户配置失败: {user_id}:{config_key}, 错误: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"设置用户配置失败: {str(e)}"
            )
    
    # ==================== LLM配置管理 ====================
    
    async def get_llm_config(
        self,
        user_id: int,
        config_name: str
    ) -> Optional[LLMConfig]:
        """
        获取LLM配置
        
        Args:
            user_id: 用户ID
            config_name: 配置名称
            
        Returns:
            LLM配置对象
        """
        try:
            # 检查缓存
            cache_key = f"llm_config:{user_id}:{config_name}"
            with self._cache_lock:
                if cache_key in self._config_cache:
                    self.logger.debug(f"从缓存获取LLM配置: {user_id}:{config_name}")
                    return self._config_cache[cache_key]
            
            # 从数据库查询
            config = self.db.query(LLMConfig).filter(
                and_(
                    LLMConfig.user_id == user_id,
                    LLMConfig.config_name == config_name
                )
            ).first()
            
            # 缓存配置
            with self._cache_lock:
                self._config_cache[cache_key] = config
            
            if config:
                self.logger.info(f"获取LLM配置成功: {user_id}:{config_name}")
            
            return config
            
        except Exception as e:
            self.logger.error(f"获取LLM配置失败: {user_id}:{config_name}, 错误: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取LLM配置失败: {str(e)}"
            )
    
    # ==================== 工具配置管理 ====================
    
    async def get_tool_config(
        self,
        user_id: int,
        tool_name: str,
        version: Optional[str] = None
    ) -> Optional[ToolConfig]:
        """
        获取工具配置
        
        Args:
            user_id: 用户ID
            tool_name: 工具名称
            version: 版本号
            
        Returns:
            工具配置对象
        """
        try:
            # 检查缓存
            cache_key = f"tool_config:{user_id}:{tool_name}:{version or 'latest'}"
            with self._cache_lock:
                if cache_key in self._config_cache:
                    self.logger.debug(f"从缓存获取工具配置: {user_id}:{tool_name}")
                    return self._config_cache[cache_key]
            
            # 构建查询
            query = self.db.query(ToolConfig).filter(
                and_(
                    ToolConfig.user_id == user_id,
                    ToolConfig.tool_name == tool_name
                )
            )
            
            if version:
                query = query.filter(ToolConfig.version == version)
            else:
                # 获取最新版本
                query = query.order_by(desc(ToolConfig.version))
            
            config = query.first()
            
            # 缓存配置
            with self._cache_lock:
                self._config_cache[cache_key] = config
            
            if config:
                self.logger.info(f"获取工具配置成功: {user_id}:{tool_name}")
            
            return config
            
        except Exception as e:
            self.logger.error(f"获取工具配置失败: {user_id}:{tool_name}, 错误: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取工具配置失败: {str(e)}"
            )
    
    # ==================== 配置备份和恢复 ====================
    
    async def backup_user_configs(
        self,
        user_id: int,
        backup_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        备份用户配置
        
        Args:
            user_id: 用户ID
            backup_name: 备份名称
            
        Returns:
            备份数据
        """
        try:
            # 获取用户所有配置
            user_configs = self.db.query(UserConfig).filter(
                UserConfig.user_id == user_id
            ).all()
            
            llm_configs = self.db.query(LLMConfig).filter(
                LLMConfig.user_id == user_id
            ).all()
            
            tool_configs = self.db.query(ToolConfig).filter(
                ToolConfig.user_id == user_id
            ).all()
            
            # 构建备份数据
            backup_data = {
                "backup_name": backup_name or f"backup_{datetime.utcnow().isoformat()}",
                "user_id": user_id,
                "timestamp": datetime.utcnow().isoformat(),
                "user_configs": [
                    {
                        "config_key": config.config_key,
                        "config_value": config.config_value,
                        "value_type": config.value_type,
                        "description": config.description
                    }
                    for config in user_configs
                ],
                "llm_configs": [
                    {
                        "config_name": config.config_name,
                        "model_name": config.model_name,
                        "model_params": config.model_params,
                        "api_key": config.api_key,
                        "api_endpoint": config.api_endpoint,
                        "description": config.description
                    }
                    for config in llm_configs
                ],
                "tool_configs": [
                    {
                        "tool_name": config.tool_name,
                        "version": config.version,
                        "config_data": config.config_data,
                        "description": config.description
                    }
                    for config in tool_configs
                ]
            }
            
            self.logger.info(f"备份用户配置成功: {user_id}")
            return backup_data
            
        except Exception as e:
            self.logger.error(f"备份用户配置失败: {user_id}, 错误: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"备份用户配置失败: {str(e)}"
            )
    
    # ==================== 辅助方法 ====================
    
    def _parse_config_value(self, value: str, value_type: str) -> Any:
        """
        解析配置值
        
        Args:
            value: 配置值字符串
            value_type: 值类型
            
        Returns:
            解析后的值
        """
        if value is None:
            return None
        
        try:
            if value_type == "string":
                return value
            elif value_type == "int":
                return int(value)
            elif value_type == "float":
                return float(value)
            elif value_type == "bool":
                return value.lower() in ("true", "1", "yes", "on")
            elif value_type == "json":
                return json.loads(value)
            elif value_type == "list":
                return json.loads(value) if value.startswith('[') else value.split(',')
            else:
                return value
        except Exception as e:
            self.logger.warning(f"解析配置值失败: {value}, 类型: {value_type}, 错误: {str(e)}")
            return value
    
    def _serialize_config_value(self, value: Any, value_type: str) -> str:
        """
        序列化配置值
        
        Args:
            value: 配置值
            value_type: 值类型
            
        Returns:
            序列化后的字符串
        """
        if value is None:
            return None
        
        try:
            if value_type in ("json", "list"):
                return json.dumps(value, ensure_ascii=False)
            else:
                return str(value)
        except Exception as e:
            self.logger.warning(f"序列化配置值失败: {value}, 类型: {value_type}, 错误: {str(e)}")
            return str(value)
    
    def _validate_config_value(self, value: Any, value_type: str, validation_rule: Optional[str] = None) -> Any:
        """
        验证配置值
        
        Args:
            value: 配置值
            value_type: 值类型
            validation_rule: 验证规则
            
        Returns:
            验证后的值
            
        Raises:
            HTTPException: 验证失败
        """
        try:
            # 基本类型验证
            if value_type == "int":
                validated_value = int(value)
            elif value_type == "float":
                validated_value = float(value)
            elif value_type == "bool":
                if isinstance(value, bool):
                    validated_value = value
                else:
                    validated_value = str(value).lower() in ("true", "1", "yes", "on")
            elif value_type == "json":
                if isinstance(value, (dict, list)):
                    validated_value = value
                else:
                    validated_value = json.loads(str(value))
            elif value_type == "list":
                if isinstance(value, list):
                    validated_value = value
                else:
                    validated_value = json.loads(str(value)) if str(value).startswith('[') else str(value).split(',')
            else:
                validated_value = str(value)
            
            # 自定义验证规则
            if validation_rule:
                rule = json.loads(validation_rule)
                
                # 数值范围验证
                if "min" in rule and isinstance(validated_value, (int, float)):
                    if validated_value < rule["min"]:
                        raise ValueError(f"值不能小于 {rule['min']}")
                
                if "max" in rule and isinstance(validated_value, (int, float)):
                    if validated_value > rule["max"]:
                        raise ValueError(f"值不能大于 {rule['max']}")
                
                # 字符串长度验证
                if "min_length" in rule and isinstance(validated_value, str):
                    if len(validated_value) < rule["min_length"]:
                        raise ValueError(f"字符串长度不能小于 {rule['min_length']}")
                
                if "max_length" in rule and isinstance(validated_value, str):
                    if len(validated_value) > rule["max_length"]:
                        raise ValueError(f"字符串长度不能大于 {rule['max_length']}")
                
                # 枚举值验证
                if "enum" in rule:
                    if validated_value not in rule["enum"]:
                        raise ValueError(f"值必须是以下之一: {rule['enum']}")
            
            return validated_value
            
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"配置值验证失败: {str(e)}"
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"配置值格式错误: {str(e)}"
            )
    
    async def clear_cache(self, pattern: Optional[str] = None):
        """
        清除缓存
        
        Args:
            pattern: 缓存键模式（可选）
        """
        try:
            if pattern:
                # 清除匹配模式的缓存
                await self.cache_manager.clear(pattern=pattern)
                self.logger.info(f"清除匹配模式的缓存: {pattern}")
            else:
                # 清除所有缓存
                await self.cache_manager.clear()
                self.logger.info("清除所有缓存")
        except Exception as e:
            self.logger.error(f"清除缓存失败: {str(e)}")
    
    async def clear_user_cache(self, user_id: int) -> bool:
        """
        清除用户配置缓存
        
        Args:
            user_id: 用户ID
            
        Returns:
            是否清除成功
        """
        try:
            # 清除用户相关的所有缓存
            await self.cache_manager.clear(tags=[f"user_{user_id}"])
            
            self.logger.info(f"清除用户缓存成功: {user_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"清除用户缓存失败: {user_id}, 错误: {str(e)}")
            return False


# 全局配置服务实例
_config_service = None


def get_config_service(db = None):
    """
    获取配置服务实例
    
    Args:
        db: 数据库会话
        
    Returns:
        配置服务实例
    """
    global _config_service
    if _config_service is None:
        _config_service = ConfigService(db)
    return _config_service