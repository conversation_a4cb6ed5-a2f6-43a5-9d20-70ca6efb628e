{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/alert/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Alert from './src/alert.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElAlert: SFCWithInstall<typeof Alert> = withInstall(Alert)\nexport default ElAlert\n\nexport * from './src/alert'\nexport type { AlertInstance } from './src/instance'\n"], "names": ["withInstall", "<PERSON><PERSON>"], "mappings": ";;;;;;;;AAEY,MAAC,OAAO,GAAGA,mBAAW,CAACC,kBAAK;;;;;;;;"}