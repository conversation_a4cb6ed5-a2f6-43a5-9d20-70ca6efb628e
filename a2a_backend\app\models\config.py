#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A多智能体系统配置模型

定义系统配置相关的数据模型
"""

from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Index, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from .base import BaseModel


class SystemConfig(BaseModel):
    """
    系统配置模型
    
    存储系统级别的配置信息
    """
    
    __tablename__ = "system_configs"
    
    # 配置基本信息
    config_key = Column(
        String(100),
        nullable=False,
        unique=True,
        comment="配置键"
    )
    
    config_value = Column(
        Text,
        nullable=True,
        comment="配置值"
    )
    
    # 配置类型和分类
    value_type = Column(
        String(20),
        nullable=False,
        default="string",
        comment="值类型"
    )
    
    category = Column(
        String(50),
        nullable=False,
        default="general",
        comment="配置分类"
    )
    
    # 配置描述
    description = Column(
        Text,
        nullable=True,
        comment="配置描述"
    )
    
    # 配置约束
    is_required = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否必需"
    )
    
    is_sensitive = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否敏感信息"
    )
    
    is_readonly = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否只读"
    )
    
    # 默认值和验证
    default_value = Column(
        Text,
        nullable=True,
        comment="默认值"
    )
    
    validation_rule = Column(
        Text,
        nullable=True,
        comment="验证规则（JSON格式）"
    )
    
    # 更新信息
    updated_by = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        comment="更新者用户ID"
    )
    
    # 关联关系
    updated_by_user = relationship("User")
    
    # 索引
    __table_args__ = (
        Index("idx_system_configs_config_key", "config_key"),
        Index("idx_system_configs_category", "category"),
        Index("idx_system_configs_value_type", "value_type"),
        Index("idx_system_configs_is_required", "is_required"),
        Index("idx_system_configs_is_sensitive", "is_sensitive"),
        Index("idx_system_configs_is_active", "is_active"),
    )
    
    def get_typed_value(self) -> Union[str, int, float, bool, dict, list, None]:
        """
        获取类型化的配置值
        
        Returns:
            Union[str, int, float, bool, dict, list, None]: 类型化的值
        """
        if not self.config_value:
            return self.get_typed_default_value()
        
        try:
            if self.value_type == "string":
                return str(self.config_value)
            elif self.value_type == "integer":
                return int(self.config_value)
            elif self.value_type == "float":
                return float(self.config_value)
            elif self.value_type == "boolean":
                return self.config_value.lower() in ("true", "1", "yes", "on")
            elif self.value_type in ("json", "dict", "list"):
                import json
                return json.loads(self.config_value)
            else:
                return self.config_value
        except (ValueError, TypeError, json.JSONDecodeError):
            return self.get_typed_default_value()
    
    def get_typed_default_value(self) -> Union[str, int, float, bool, dict, list, None]:
        """
        获取类型化的默认值
        
        Returns:
            Union[str, int, float, bool, dict, list, None]: 类型化的默认值
        """
        if not self.default_value:
            return None
        
        try:
            if self.value_type == "string":
                return str(self.default_value)
            elif self.value_type == "integer":
                return int(self.default_value)
            elif self.value_type == "float":
                return float(self.default_value)
            elif self.value_type == "boolean":
                return self.default_value.lower() in ("true", "1", "yes", "on")
            elif self.value_type in ("json", "dict", "list"):
                import json
                return json.loads(self.default_value)
            else:
                return self.default_value
        except (ValueError, TypeError, json.JSONDecodeError):
            return None
    
    def set_typed_value(self, value: Union[str, int, float, bool, dict, list, None]) -> None:
        """
        设置类型化的配置值
        
        Args:
            value: 要设置的值
        """
        if value is None:
            self.config_value = None
            return
        
        if self.value_type in ("json", "dict", "list"):
            import json
            self.config_value = json.dumps(value, ensure_ascii=False)
        else:
            self.config_value = str(value)
    
    def get_validation_rule(self) -> Dict[str, Any]:
        """
        获取验证规则
        
        Returns:
            Dict[str, Any]: 验证规则字典
        """
        if not self.validation_rule:
            return {}
        
        try:
            import json
            return json.loads(self.validation_rule)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_validation_rule(self, rule: Dict[str, Any]) -> None:
        """
        设置验证规则
        
        Args:
            rule: 验证规则字典
        """
        import json
        self.validation_rule = json.dumps(rule, ensure_ascii=False)
    
    def validate_value(self, value: Any) -> bool:
        """
        验证配置值
        
        Args:
            value: 要验证的值
        
        Returns:
            bool: 是否有效
        """
        rule = self.get_validation_rule()
        if not rule:
            return True
        
        # 基本类型验证
        if "type" in rule:
            expected_type = rule["type"]
            if expected_type == "string" and not isinstance(value, str):
                return False
            elif expected_type == "integer" and not isinstance(value, int):
                return False
            elif expected_type == "float" and not isinstance(value, (int, float)):
                return False
            elif expected_type == "boolean" and not isinstance(value, bool):
                return False
        
        # 范围验证
        if "min" in rule and value < rule["min"]:
            return False
        if "max" in rule and value > rule["max"]:
            return False
        
        # 长度验证
        if "min_length" in rule and len(str(value)) < rule["min_length"]:
            return False
        if "max_length" in rule and len(str(value)) > rule["max_length"]:
            return False
        
        # 枚举验证
        if "enum" in rule and value not in rule["enum"]:
            return False
        
        # 正则表达式验证
        if "pattern" in rule:
            import re
            if not re.match(rule["pattern"], str(value)):
                return False
        
        return True
    
    def __repr__(self) -> str:
        return f"<SystemConfig(id={self.id}, config_key='{self.config_key}', category='{self.category}')>"


class UserConfig(BaseModel):
    """
    用户配置模型
    
    存储用户个人配置信息
    """
    
    __tablename__ = "user_configs"
    
    # 用户关联
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="用户ID"
    )
    
    # 配置基本信息
    config_key = Column(
        String(100),
        nullable=False,
        comment="配置键"
    )
    
    config_value = Column(
        Text,
        nullable=True,
        comment="配置值"
    )
    
    # 配置类型
    value_type = Column(
        String(20),
        nullable=False,
        default="string",
        comment="值类型"
    )
    
    # 关联关系
    user = relationship("User", back_populates="user_configs")
    
    # 索引
    __table_args__ = (
        Index("idx_user_configs_user_id", "user_id"),
        Index("idx_user_configs_config_key", "config_key"),
        Index("idx_user_configs_value_type", "value_type"),
        # 复合唯一索引
        Index("idx_user_configs_unique", "user_id", "config_key", unique=True),
    )
    
    def get_typed_value(self) -> Union[str, int, float, bool, dict, list, None]:
        """
        获取类型化的配置值
        
        Returns:
            Union[str, int, float, bool, dict, list, None]: 类型化的值
        """
        if not self.config_value:
            return None
        
        try:
            if self.value_type == "string":
                return str(self.config_value)
            elif self.value_type == "integer":
                return int(self.config_value)
            elif self.value_type == "float":
                return float(self.config_value)
            elif self.value_type == "boolean":
                return self.config_value.lower() in ("true", "1", "yes", "on")
            elif self.value_type in ("json", "dict", "list"):
                import json
                return json.loads(self.config_value)
            else:
                return self.config_value
        except (ValueError, TypeError, json.JSONDecodeError):
            return None
    
    def set_typed_value(self, value: Union[str, int, float, bool, dict, list, None]) -> None:
        """
        设置类型化的配置值
        
        Args:
            value: 要设置的值
        """
        if value is None:
            self.config_value = None
            return
        
        if self.value_type in ("json", "dict", "list"):
            import json
            self.config_value = json.dumps(value, ensure_ascii=False)
        else:
            self.config_value = str(value)
    
    def __repr__(self) -> str:
        return f"<UserConfig(id={self.id}, user_id={self.user_id}, config_key='{self.config_key}')>"


class AgentConfig(BaseModel):
    """
    智能体配置模型
    
    存储智能体配置信息
    """
    
    __tablename__ = "agent_configs"
    
    # 智能体关联
    agent_id = Column(
        Integer,
        ForeignKey("agents.id", ondelete="CASCADE"),
        nullable=False,
        comment="智能体ID"
    )
    
    # 用户关联
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="用户ID"
    )
    
    # 配置基本信息
    config_key = Column(
        String(100),
        nullable=False,
        comment="配置键"
    )
    
    config_value = Column(
        Text,
        nullable=True,
        comment="配置值"
    )
    
    # 配置类型
    value_type = Column(
        String(20),
        nullable=False,
        default="string",
        comment="值类型"
    )
    
    # 关联关系
    agent = relationship("Agent", back_populates="agent_configs")
    
    # 索引
    __table_args__ = (
        Index("idx_agent_configs_agent_id", "agent_id"),
        Index("idx_agent_configs_config_key", "config_key"),
        Index("idx_agent_configs_value_type", "value_type"),
        # 复合唯一索引
        Index("idx_agent_configs_unique", "agent_id", "config_key", unique=True),
    )
    
    def get_typed_value(self) -> Union[str, int, float, bool, dict, list, None]:
        """
        获取类型化的配置值
        
        Returns:
            Union[str, int, float, bool, dict, list, None]: 类型化的值
        """
        if not self.config_value:
            return None
        
        try:
            if self.value_type == "string":
                return str(self.config_value)
            elif self.value_type == "integer":
                return int(self.config_value)
            elif self.value_type == "float":
                return float(self.config_value)
            elif self.value_type == "boolean":
                return self.config_value.lower() in ("true", "1", "yes", "on")
            elif self.value_type in ("json", "dict", "list"):
                import json
                return json.loads(self.config_value)
            else:
                return self.config_value
        except (ValueError, TypeError, json.JSONDecodeError):
            return None
    
    def set_typed_value(self, value: Union[str, int, float, bool, dict, list, None]) -> None:
        """
        设置类型化的配置值
        
        Args:
            value: 要设置的值
        """
        if value is None:
            self.config_value = None
            return
        
        if self.value_type in ("json", "dict", "list"):
            import json
            self.config_value = json.dumps(value, ensure_ascii=False)
        else:
            self.config_value = str(value)
    
    def __repr__(self) -> str:
        return f"<AgentConfig(id={self.id}, agent_id={self.agent_id}, config_key='{self.config_key}')>"


class ConfigTemplate(BaseModel):
    """
    配置模板模型
    
    存储配置模板信息
    """
    
    __tablename__ = "config_templates"
    
    # 用户关联
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="创建者用户ID"
    )
    
    # 模板基本信息
    name = Column(
        String(100),
        nullable=False,
        comment="模板名称"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="模板描述"
    )
    
    # 模板类型
    template_type = Column(
        String(50),
        nullable=False,
        comment="模板类型"
    )
    
    # 模板内容
    template_data = Column(
        Text,
        nullable=False,
        comment="模板数据（JSON格式）"
    )
    
    # 模板状态
    is_default = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否为默认模板"
    )
    
    is_public = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否公开"
    )
    
    # 创建者
    created_by = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        comment="创建者用户ID"
    )
    
    # 统计信息
    usage_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="使用次数"
    )
    
    # 关联关系
    creator = relationship("User", foreign_keys=[created_by])
    user = relationship("User", foreign_keys=[user_id])
    
    # 索引
    __table_args__ = (
        Index("idx_config_templates_name", "name"),
        Index("idx_config_templates_template_type", "template_type"),
        Index("idx_config_templates_is_default", "is_default"),
        Index("idx_config_templates_is_public", "is_public"),
        Index("idx_config_templates_created_by", "created_by"),
        Index("idx_config_templates_is_active", "is_active"),
    )
    
    def get_template_data(self) -> Dict[str, Any]:
        """
        获取模板数据
        
        Returns:
            Dict[str, Any]: 模板数据字典
        """
        if not self.template_data:
            return {}
        
        try:
            import json
            return json.loads(self.template_data)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_template_data(self, data: Dict[str, Any]) -> None:
        """
        设置模板数据
        
        Args:
            data: 模板数据字典
        """
        import json
        self.template_data = json.dumps(data, ensure_ascii=False)
    
    def __repr__(self) -> str:
        return f"<ConfigTemplate(id={self.id}, name='{self.name}', template_type='{self.template_type}')>"


class LLMConfig(BaseModel):
    """
    LLM配置模型
    
    存储大语言模型的配置信息
    """
    
    __tablename__ = "llm_configs"
    
    # 关联关系
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="用户ID"
    )
    
    agent_id = Column(
        Integer,
        ForeignKey("agents.id", ondelete="SET NULL"),
        nullable=True,
        comment="智能体ID"
    )
    
    # 配置基本信息
    config_name = Column(
        String(100),
        nullable=False,
        comment="配置名称"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="配置描述"
    )
    
    # LLM基本配置
    provider = Column(
        String(50),
        nullable=False,
        comment="LLM提供商"
    )
    
    model_name = Column(
        String(100),
        nullable=False,
        comment="模型名称"
    )
    
    model_version = Column(
        String(50),
        nullable=True,
        comment="模型版本"
    )
    
    api_key = Column(
        String(500),
        nullable=True,
        comment="API密钥"
    )
    
    api_base = Column(
        String(200),
        nullable=True,
        comment="API基础URL"
    )
    
    # 模型参数
    temperature = Column(
        Float,
        nullable=False,
        default=0.7,
        comment="温度参数"
    )
    
    max_tokens = Column(
        Integer,
        nullable=True,
        comment="最大令牌数"
    )
    
    top_p = Column(
        Float,
        nullable=True,
        comment="Top-p参数"
    )
    
    frequency_penalty = Column(
        Float,
        nullable=True,
        comment="频率惩罚"
    )
    
    presence_penalty = Column(
        Float,
        nullable=True,
        comment="存在惩罚"
    )
    
    # 配置状态
    is_active = Column(
        Boolean,
        nullable=False,
        default=True,
        comment="是否激活"
    )
    
    is_default = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否为默认配置"
    )
    
    # 扩展配置
    extra_params = Column(
        Text,
        nullable=True,
        comment="扩展参数（JSON格式）"
    )
    
    # 使用统计
    usage_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="使用次数"
    )
    
    last_used_at = Column(
        DateTime,
        nullable=True,
        comment="最后使用时间"
    )
    
    # 关联关系
    user = relationship("User", back_populates="llm_configs")
    agent = relationship("Agent", back_populates="llm_configs")
    
    # 索引
    __table_args__ = (
        Index("idx_llm_configs_user_id", "user_id"),
        Index("idx_llm_configs_agent_id", "agent_id"),
        Index("idx_llm_configs_provider", "provider"),
        Index("idx_llm_configs_model_name", "model_name"),
        Index("idx_llm_configs_is_active", "is_active"),
        Index("idx_llm_configs_is_default", "is_default"),
        Index("idx_llm_configs_created_at", "created_at"),
    )
    
    def get_extra_params(self) -> Dict[str, Any]:
        """获取扩展参数"""
        if not self.extra_params:
            return {}
        
        try:
            import json
            return json.loads(self.extra_params)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_extra_params(self, params: Dict[str, Any]):
        """设置扩展参数"""
        import json
        self.extra_params = json.dumps(params, ensure_ascii=False) if params else None
    
    def increment_usage(self):
        """增加使用次数"""
        self.usage_count += 1
        self.last_used_at = datetime.now()
    
    def __repr__(self) -> str:
        return f"<LLMConfig(id={self.id}, config_name='{self.config_name}', provider='{self.provider}', model_name='{self.model_name}')>"


class ToolConfig(BaseModel):
    """
    工具配置模型
    
    存储智能体工具的配置信息
    """
    
    __tablename__ = "tool_configs"
    
    # 关联关系
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="用户ID"
    )
    
    agent_id = Column(
        Integer,
        ForeignKey("agents.id", ondelete="SET NULL"),
        nullable=True,
        comment="智能体ID"
    )
    
    # 配置基本信息
    config_name = Column(
        String(100),
        nullable=False,
        comment="配置名称"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="配置描述"
    )
    
    # 工具基本信息
    tool_name = Column(
        String(100),
        nullable=False,
        comment="工具名称"
    )
    
    tool_type = Column(
        String(50),
        nullable=False,
        comment="工具类型"
    )
    
    tool_version = Column(
        String(50),
        nullable=True,
        comment="工具版本"
    )
    
    # 工具配置
    tool_config = Column(
        Text,
        nullable=True,
        comment="工具配置（JSON格式）"
    )
    
    # 权限配置
    permissions = Column(
        Text,
        nullable=True,
        comment="权限配置（JSON格式）"
    )
    
    # 配置状态
    is_active = Column(
        Boolean,
        nullable=False,
        default=True,
        comment="是否激活"
    )
    
    is_default = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否为默认配置"
    )
    
    # 使用统计
    usage_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="使用次数"
    )
    
    last_used_at = Column(
        DateTime,
        nullable=True,
        comment="最后使用时间"
    )
    
    # 关联关系
    user = relationship("User", back_populates="tool_configs")
    agent = relationship("Agent", back_populates="tool_configs")
    
    # 索引
    __table_args__ = (
        Index("idx_tool_configs_user_id", "user_id"),
        Index("idx_tool_configs_agent_id", "agent_id"),
        Index("idx_tool_configs_tool_name", "tool_name"),
        Index("idx_tool_configs_tool_type", "tool_type"),
        Index("idx_tool_configs_is_active", "is_active"),
        Index("idx_tool_configs_is_default", "is_default"),
        Index("idx_tool_configs_created_at", "created_at"),
    )
    
    def get_tool_config(self) -> Dict[str, Any]:
        """获取工具配置"""
        if not self.tool_config:
            return {}
        
        try:
            import json
            return json.loads(self.tool_config)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_tool_config(self, config: Dict[str, Any]):
        """设置工具配置"""
        import json
        self.tool_config = json.dumps(config, ensure_ascii=False) if config else None
    
    def get_permissions(self) -> Dict[str, Any]:
        """获取权限配置"""
        if not self.permissions:
            return {}
        
        try:
            import json
            return json.loads(self.permissions)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_permissions(self, permissions: Dict[str, Any]):
        """设置权限配置"""
        import json
        self.permissions = json.dumps(permissions, ensure_ascii=False) if permissions else None
    
    def increment_usage(self):
        """增加使用次数"""
        self.usage_count += 1
        self.last_used_at = datetime.now()
    
    def __repr__(self) -> str:
        return f"<ToolConfig(id={self.id}, config_name='{self.config_name}', tool_name='{self.tool_name}', tool_type='{self.tool_type}')>"