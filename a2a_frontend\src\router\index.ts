import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import type { RouteRecordRaw } from 'vue-router'

/**
 * 路由配置
 */
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: { requiresAuth: false, title: '登录' }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/Register.vue'),
    meta: { requiresAuth: false, title: '注册' }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: { requiresAuth: true, title: '仪表板' }
  },
  {
    path: '/agents',
    name: 'Agents',
    component: () => import('@/views/agents/AgentList.vue'),
    meta: { requiresAuth: true, title: '智能体管理' }
  },
  {
    path: '/agents/create',
    name: 'CreateAgent',
    component: () => import('@/views/agents/AgentCreate.vue'),
    meta: { requiresAuth: true, title: '创建智能体' }
  },
  {
    path: '/agents/:id',
    name: 'AgentDetail',
    component: () => import('@/views/agents/AgentDetail.vue'),
    meta: { requiresAuth: true, title: '智能体详情' }
  },
  {
    path: '/sessions',
    name: 'Sessions',
    component: () => import('@/views/sessions/SessionList.vue'),
    meta: { requiresAuth: true, title: '会话管理' }
  },
  {
    path: '/sessions/create',
    name: 'CreateSession',
    component: () => import('@/views/sessions/SessionCreate.vue'),
    meta: { requiresAuth: true, title: '创建会话' }
  },
  {
    path: '/sessions/:id',
    name: 'SessionDetail',
    component: () => import('@/views/sessions/SessionDetail.vue'),
    meta: { requiresAuth: true, title: '会话详情' }
  },
  {
    path: '/tasks',
    name: 'Tasks',
    component: () => import('@/views/tasks/TaskList.vue'),
    meta: { requiresAuth: true, title: '任务管理' }
  },
  {
    path: '/workflows',
    name: 'Workflows',
    component: () => import('@/views/workflows/WorkflowList.vue'),
    meta: { requiresAuth: true, title: '工作流管理' }
  },
  {
    path: '/admin',
    name: 'Admin',
    component: () => import('@/views/admin/AdminPanel.vue'),
    meta: { requiresAuth: true, requiresAdmin: true, title: '系统管理' }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: { title: '页面未找到' }
  }
]

/**
 * 创建路由实例
 */
const router = createRouter({
  history: createWebHistory(),
  routes
})

/**
 * 路由守卫
 */
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - A2A多智能体系统` : 'A2A多智能体系统'

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      next('/login')
      return
    }

    // 检查用户信息（但不阻塞路由）
    if (!authStore.user) {
      // 异步获取用户信息，不等待结果
      authStore.checkAuth().catch(() => {
        // 如果获取用户信息失败，跳转到登录页
        if (to.path !== '/login') {
          next('/login')
        }
      })
    }

    // 检查管理员权限
    if (to.meta.requiresAdmin && authStore.user && !authStore.isAdmin) {
      next('/dashboard')
      return
    }
  }

  // 已登录用户访问登录/注册页面，重定向到仪表板
  if ((to.name === 'Login' || to.name === 'Register') && authStore.isAuthenticated) {
    next('/dashboard')
    return
  }

  next()
})

export default router
