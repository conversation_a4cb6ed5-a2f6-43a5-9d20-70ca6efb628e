/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AppHeader: typeof import('./src/components/layout/AppHeader.vue')['default']
    AppSidebar: typeof import('./src/components/layout/AppSidebar.vue')['default']
    AuditLogs: typeof import('./src/components/admin/AuditLogs.vue')['default']
    DataManagement: typeof import('./src/components/admin/DataManagement.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAside: typeof import('element-plus/es')['ElAside']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SystemConfig: typeof import('./src/components/admin/SystemConfig.vue')['default']
    SystemMonitor: typeof import('./src/components/admin/SystemMonitor.vue')['default']
    UserManagement: typeof import('./src/components/admin/UserManagement.vue')['default']
  }
}
