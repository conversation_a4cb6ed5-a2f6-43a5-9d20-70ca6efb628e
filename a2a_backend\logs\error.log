2025-06-27 21:17:14.221 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: GET /api/v1/auth/login - 405 (0.003s)
2025-06-27 21:18:12.864 | ERROR    | app.api.v1.auth:login_user:344 - 用户登录异常: RateLimiter.check_rate_limit() got an unexpected keyword argument 'key'
2025-06-27 21:18:12.864 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.002s)
2025-06-27 21:21:24.605 | ERROR    | app.api.v1.auth:login_user:344 - 用户登录异常: RateLimiter.check_rate_limit() got an unexpected keyword argument 'key'
2025-06-27 21:21:24.605 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.000s)
2025-06-27 21:25:22.654 | ERROR    | app.api.v1.auth:login_user:341 - 用户登录异常: object bool can't be used in 'await' expression
2025-06-27 21:25:22.660 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.005s)
2025-06-27 21:30:44.851 | ERROR    | app.api.v1.auth:login_user:340 - 用户登录异常: UserService.authenticate_user() missing 1 required positional argument: 'password'
2025-06-27 21:30:44.851 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.004s)
2025-06-27 21:38:57.132 | ERROR    | app.api.v1.auth:login_user:336 - 用户登录异常: 'coroutine' object does not support the asynchronous context manager protocol
2025-06-27 21:38:57.132 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.003s)
2025-06-27 21:55:38.351 | ERROR    | app.services.user_service:authenticate_user:253 - 用户认证异常: Could not determine join condition between parent/child tables on relationship User.agents - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-06-27 21:55:38.352 | ERROR    | app.api.v1.auth:login_user:328 - 用户登录异常: 认证失败: Could not determine join condition between parent/child tables on relationship User.agents - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-06-27 21:55:38.352 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.017s)
2025-06-27 22:10:36.210 | ERROR    | app.services.user_service:authenticate_user:253 - 用户认证异常: Could not determine join condition between parent/child tables on relationship User.artifacts - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-06-27 22:10:36.210 | ERROR    | app.api.v1.auth:login_user:328 - 用户登录异常: 认证失败: Could not determine join condition between parent/child tables on relationship User.artifacts - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-06-27 22:10:36.210 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.017s)
2025-06-27 22:17:18.964 | ERROR    | app.services.user_service:authenticate_user:253 - 用户认证异常: Column expression expected for argument 'remote_side'; got <built-in function id>.
2025-06-27 22:17:18.964 | ERROR    | app.api.v1.auth:login_user:328 - 用户登录异常: 认证失败: Column expression expected for argument 'remote_side'; got <built-in function id>.
2025-06-27 22:17:18.964 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.134s)
2025-06-27 22:17:33.707 | ERROR    | app.services.user_service:authenticate_user:253 - 用户认证异常: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ToolCategory(tool_categories)]'. Original exception was: Column expression expected for argument 'remote_side'; got <built-in function id>.
2025-06-27 22:17:33.707 | ERROR    | app.api.v1.auth:login_user:328 - 用户登录异常: 认证失败: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ToolCategory(tool_categories)]'. Original exception was: Column expression expected for argument 'remote_side'; got <built-in function id>.
2025-06-27 22:17:33.707 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.000s)
2025-06-27 22:18:09.437 | ERROR    | app.services.user_service:authenticate_user:253 - 用户认证异常: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ToolCategory(tool_categories)]'. Original exception was: Column expression expected for argument 'remote_side'; got <built-in function id>.
2025-06-27 22:18:09.437 | ERROR    | app.api.v1.auth:login_user:328 - 用户登录异常: 认证失败: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ToolCategory(tool_categories)]'. Original exception was: Column expression expected for argument 'remote_side'; got <built-in function id>.
2025-06-27 22:18:09.439 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.006s)
2025-06-27 22:19:45.730 | ERROR    | app.services.user_service:authenticate_user:253 - 用户认证异常: Could not determine join condition between parent/child tables on relationship Artifact.user - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-06-27 22:19:45.730 | ERROR    | app.api.v1.auth:login_user:328 - 用户登录异常: 认证失败: Could not determine join condition between parent/child tables on relationship Artifact.user - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-06-27 22:19:45.730 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.164s)
2025-06-27 22:21:27.935 | ERROR    | app.services.user_service:authenticate_user:253 - 用户认证异常: Could not determine join condition between parent/child tables on relationship ConfigTemplate.creator - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-06-27 22:21:27.935 | ERROR    | app.api.v1.auth:login_user:328 - 用户登录异常: 认证失败: Could not determine join condition between parent/child tables on relationship ConfigTemplate.creator - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-06-27 22:21:27.935 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.180s)
2025-06-27 22:24:08.838 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.217s)
2025-06-27 22:24:38.029 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.007s)
2025-06-27 22:28:09.696 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.217s)
2025-06-27 22:30:46.093 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.010s)
2025-06-27 22:32:32.873 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.007s)
2025-06-27 22:32:34.080 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.009s)
2025-06-27 22:32:34.808 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.009s)
2025-06-27 22:32:35.853 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.010s)
2025-06-27 22:32:36.487 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.008s)
2025-06-27 22:32:37.162 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.007s)
2025-06-27 22:33:34.323 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.010s)
2025-06-27 22:34:09.854 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.009s)
2025-06-27 22:34:31.688 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.009s)
2025-06-27 22:34:46.995 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.012s)
2025-06-27 22:35:00.411 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.017s)
2025-06-27 22:35:01.198 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.010s)
2025-06-27 22:35:01.810 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.009s)
2025-06-27 22:35:02.412 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.009s)
2025-06-27 22:35:02.977 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.011s)
2025-06-27 22:35:03.513 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.000s)
2025-06-27 22:35:04.111 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.000s)
2025-06-27 22:35:04.734 | ERROR    | app.api.v1.auth:login_user:328 - 用户登录异常: 429: 请求过于频繁，请稍后再试。限制: 10次/分钟
2025-06-27 22:35:04.734 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.001s)
2025-06-27 22:38:47.645 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.216s)
2025-06-27 22:47:28.885 | ERROR    | app.services.user_service:authenticate_user:264 - 用户认证异常: 'Settings' object has no attribute 'max_login_attempts'
2025-06-27 22:47:28.885 | ERROR    | app.api.v1.auth:login_user:331 - 用户登录异常: 认证失败: 'Settings' object has no attribute 'max_login_attempts'
2025-06-27 22:47:28.885 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.530s)
2025-06-27 22:51:36.040 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.547s)
2025-06-27 22:57:54.678 | ERROR    | app.auth.jwt_handler:_save_token_to_database:344 - 保存令牌到数据库失败: (sqlite3.IntegrityError) NOT NULL constraint failed: user_tokens.is_revoked
[SQL: 
                INSERT INTO user_tokens (
                    user_id, session_id, access_token, refresh_token,
                    access_expires_at, refresh_expires_at, device_info,
                    ip_address, user_agent, created_at
                ) VALUES (
                    ?, ?, ?, ?,
                    ?, ?, ?,
                    ?, ?, ?
                )
            ]
[parameters: (1, 'e12b7493-f69d-4412-8bb3-1e2f73d02b23', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiZTEyYjc0OTMtZjY5ZC00NDEyLThiYjMtMWUyZjczZDAyYjIzIiwidG9rZW5fdHlwZSI6ImFjY2Vz ... (5 characters truncated) ... mlhdCI6MTc1MTAzNjI3NCwiZXhwIjoxNzUxMDM4MDc0LCJqdGkiOiJjNTk0Yzg5Ni05ZGU0LTRmNjgtYmIzYy0wYmI4MmNlOTZjYTcifQ.kMll7ajA4YL-YZPcZzAJXDpPpgDfW7dC4Tk-fUWqdtI', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiZTEyYjc0OTMtZjY5ZC00NDEyLThiYjMtMWUyZjczZDAyYjIzIiwidG9rZW5fdHlwZSI6InJlZnJl ... (6 characters truncated) ... JpYXQiOjE3NTEwMzYyNzQsImV4cCI6MTc1MTY0MTA3NCwianRpIjoiNmY5ODI5ZWQtZDQ4MS00ZjM4LThiMjQtNzMyNDY4ZTk4ODI5In0.UlZG2yzs3s8GREi1jzvbST1ODU1-zUiNAKjraOt8ZeA', datetime.datetime(2025, 6, 27, 15, 27, 54, 677411), datetime.datetime(2025, 7, 4, 14, 57, 54, 677411), None, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0', datetime.datetime(2025, 6, 27, 22, 57, 54, 678494))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-27 22:57:54.678 | ERROR    | app.auth.jwt_handler:generate_tokens:116 - 生成JWT令牌失败: (sqlite3.IntegrityError) NOT NULL constraint failed: user_tokens.is_revoked
[SQL: 
                INSERT INTO user_tokens (
                    user_id, session_id, access_token, refresh_token,
                    access_expires_at, refresh_expires_at, device_info,
                    ip_address, user_agent, created_at
                ) VALUES (
                    ?, ?, ?, ?,
                    ?, ?, ?,
                    ?, ?, ?
                )
            ]
[parameters: (1, 'e12b7493-f69d-4412-8bb3-1e2f73d02b23', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiZTEyYjc0OTMtZjY5ZC00NDEyLThiYjMtMWUyZjczZDAyYjIzIiwidG9rZW5fdHlwZSI6ImFjY2Vz ... (5 characters truncated) ... mlhdCI6MTc1MTAzNjI3NCwiZXhwIjoxNzUxMDM4MDc0LCJqdGkiOiJjNTk0Yzg5Ni05ZGU0LTRmNjgtYmIzYy0wYmI4MmNlOTZjYTcifQ.kMll7ajA4YL-YZPcZzAJXDpPpgDfW7dC4Tk-fUWqdtI', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiZTEyYjc0OTMtZjY5ZC00NDEyLThiYjMtMWUyZjczZDAyYjIzIiwidG9rZW5fdHlwZSI6InJlZnJl ... (6 characters truncated) ... JpYXQiOjE3NTEwMzYyNzQsImV4cCI6MTc1MTY0MTA3NCwianRpIjoiNmY5ODI5ZWQtZDQ4MS00ZjM4LThiMjQtNzMyNDY4ZTk4ODI5In0.UlZG2yzs3s8GREi1jzvbST1ODU1-zUiNAKjraOt8ZeA', datetime.datetime(2025, 6, 27, 15, 27, 54, 677411), datetime.datetime(2025, 7, 4, 14, 57, 54, 677411), None, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0', datetime.datetime(2025, 6, 27, 22, 57, 54, 678494))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-27 22:57:54.678 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.484s)
2025-06-27 23:00:47.691 | ERROR    | app.auth.jwt_handler:_save_token_to_database:345 - 保存令牌到数据库失败: (sqlite3.IntegrityError) NOT NULL constraint failed: user_tokens.updated_at
[SQL: 
                INSERT INTO user_tokens (
                    user_id, session_id, access_token, refresh_token,
                    access_expires_at, refresh_expires_at, device_info,
                    ip_address, user_agent, is_revoked, created_at
                ) VALUES (
                    ?, ?, ?, ?,
                    ?, ?, ?,
                    ?, ?, ?, ?
                )
            ]
[parameters: (1, 'd32b0915-8132-407c-a767-2827709cbba9', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiZDMyYjA5MTUtODEzMi00MDdjLWE3NjctMjgyNzcwOWNiYmE5IiwidG9rZW5fdHlwZSI6ImFjY2Vz ... (5 characters truncated) ... mlhdCI6MTc1MTAzNjQ0NywiZXhwIjoxNzUxMDM4MjQ3LCJqdGkiOiI2ODExZTU0YS1lZTk5LTRiYTMtODdmMC1lY2I2YTVhMTc1MTUifQ.JG5VWet-nX1GoY_R-W-mBa9MYGJf-6zjCd0fxehgbjk', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiZDMyYjA5MTUtODEzMi00MDdjLWE3NjctMjgyNzcwOWNiYmE5IiwidG9rZW5fdHlwZSI6InJlZnJl ... (6 characters truncated) ... JpYXQiOjE3NTEwMzY0NDcsImV4cCI6MTc1MTY0MTI0NywianRpIjoiNmVlYjFkYmItOTRhNy00NDM0LTlkNTUtZDRlNGEyYTAyOTQ4In0.YSLYPGYM8Uddh3CSwMzn7QjWru2tCbiq5pKkfL-m4KU', datetime.datetime(2025, 6, 27, 15, 30, 47, 674184), datetime.datetime(2025, 7, 4, 15, 0, 47, 674184), None, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0', False, datetime.datetime(2025, 6, 27, 23, 0, 47, 674184))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-27 23:00:47.691 | ERROR    | app.auth.jwt_handler:generate_tokens:116 - 生成JWT令牌失败: (sqlite3.IntegrityError) NOT NULL constraint failed: user_tokens.updated_at
[SQL: 
                INSERT INTO user_tokens (
                    user_id, session_id, access_token, refresh_token,
                    access_expires_at, refresh_expires_at, device_info,
                    ip_address, user_agent, is_revoked, created_at
                ) VALUES (
                    ?, ?, ?, ?,
                    ?, ?, ?,
                    ?, ?, ?, ?
                )
            ]
[parameters: (1, 'd32b0915-8132-407c-a767-2827709cbba9', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiZDMyYjA5MTUtODEzMi00MDdjLWE3NjctMjgyNzcwOWNiYmE5IiwidG9rZW5fdHlwZSI6ImFjY2Vz ... (5 characters truncated) ... mlhdCI6MTc1MTAzNjQ0NywiZXhwIjoxNzUxMDM4MjQ3LCJqdGkiOiI2ODExZTU0YS1lZTk5LTRiYTMtODdmMC1lY2I2YTVhMTc1MTUifQ.JG5VWet-nX1GoY_R-W-mBa9MYGJf-6zjCd0fxehgbjk', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiZDMyYjA5MTUtODEzMi00MDdjLWE3NjctMjgyNzcwOWNiYmE5IiwidG9rZW5fdHlwZSI6InJlZnJl ... (6 characters truncated) ... JpYXQiOjE3NTEwMzY0NDcsImV4cCI6MTc1MTY0MTI0NywianRpIjoiNmVlYjFkYmItOTRhNy00NDM0LTlkNTUtZDRlNGEyYTAyOTQ4In0.YSLYPGYM8Uddh3CSwMzn7QjWru2tCbiq5pKkfL-m4KU', datetime.datetime(2025, 6, 27, 15, 30, 47, 674184), datetime.datetime(2025, 7, 4, 15, 0, 47, 674184), None, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0', False, datetime.datetime(2025, 6, 27, 23, 0, 47, 674184))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-27 23:00:47.691 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.517s)
2025-06-27 23:06:41.593 | ERROR    | app.auth.jwt_handler:_save_token_to_database:347 - 保存令牌到数据库失败: (sqlite3.IntegrityError) NOT NULL constraint failed: user_tokens.is_active
[SQL: 
                INSERT INTO user_tokens (
                    user_id, session_id, access_token, refresh_token,
                    access_expires_at, refresh_expires_at, device_info,
                    ip_address, user_agent, is_revoked, created_at, updated_at
                ) VALUES (
                    ?, ?, ?, ?,
                    ?, ?, ?,
                    ?, ?, ?, ?, ?
                )
            ]
[parameters: (1, '9afd2315-bf99-4ec1-bbf1-c22d45df85c0', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiOWFmZDIzMTUtYmY5OS00ZWMxLWJiZjEtYzIyZDQ1ZGY4NWMwIiwidG9rZW5fdHlwZSI6ImFjY2Vz ... (5 characters truncated) ... mlhdCI6MTc1MTAzNjgwMSwiZXhwIjoxNzUxMDM4NjAxLCJqdGkiOiIzM2QxZGMyZi0xZDlmLTQzNDktYWJlMS0xNzc5N2Y0MzRiZTcifQ.hLnOVF2z6tt3VPsMUvwvZ_1-lyOmVfNpC0lhrjVAMLM', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiOWFmZDIzMTUtYmY5OS00ZWMxLWJiZjEtYzIyZDQ1ZGY4NWMwIiwidG9rZW5fdHlwZSI6InJlZnJl ... (6 characters truncated) ... JpYXQiOjE3NTEwMzY4MDEsImV4cCI6MTc1MTY0MTYwMSwianRpIjoiNjJmMTUyOGUtMTEzZC00YzQ2LWJlMzktZGFjMWYyMWY3MzEzIn0.CxRunU2CJfmivdz4SvuhQMMB1VIW8EaGKC-j3OYe2PY', datetime.datetime(2025, 6, 27, 15, 36, 41, 577102), datetime.datetime(2025, 7, 4, 15, 6, 41, 577102), None, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0', False, datetime.datetime(2025, 6, 27, 23, 6, 41, 585845), datetime.datetime(2025, 6, 27, 23, 6, 41, 585845))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-27 23:06:41.593 | ERROR    | app.auth.jwt_handler:generate_tokens:116 - 生成JWT令牌失败: (sqlite3.IntegrityError) NOT NULL constraint failed: user_tokens.is_active
[SQL: 
                INSERT INTO user_tokens (
                    user_id, session_id, access_token, refresh_token,
                    access_expires_at, refresh_expires_at, device_info,
                    ip_address, user_agent, is_revoked, created_at, updated_at
                ) VALUES (
                    ?, ?, ?, ?,
                    ?, ?, ?,
                    ?, ?, ?, ?, ?
                )
            ]
[parameters: (1, '9afd2315-bf99-4ec1-bbf1-c22d45df85c0', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiOWFmZDIzMTUtYmY5OS00ZWMxLWJiZjEtYzIyZDQ1ZGY4NWMwIiwidG9rZW5fdHlwZSI6ImFjY2Vz ... (5 characters truncated) ... mlhdCI6MTc1MTAzNjgwMSwiZXhwIjoxNzUxMDM4NjAxLCJqdGkiOiIzM2QxZGMyZi0xZDlmLTQzNDktYWJlMS0xNzc5N2Y0MzRiZTcifQ.hLnOVF2z6tt3VPsMUvwvZ_1-lyOmVfNpC0lhrjVAMLM', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJzZXNzaW9uX2lkIjoiOWFmZDIzMTUtYmY5OS00ZWMxLWJiZjEtYzIyZDQ1ZGY4NWMwIiwidG9rZW5fdHlwZSI6InJlZnJl ... (6 characters truncated) ... JpYXQiOjE3NTEwMzY4MDEsImV4cCI6MTc1MTY0MTYwMSwianRpIjoiNjJmMTUyOGUtMTEzZC00YzQ2LWJlMzktZGFjMWYyMWY3MzEzIn0.CxRunU2CJfmivdz4SvuhQMMB1VIW8EaGKC-j3OYe2PY', datetime.datetime(2025, 6, 27, 15, 36, 41, 577102), datetime.datetime(2025, 7, 4, 15, 6, 41, 577102), None, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0', False, datetime.datetime(2025, 6, 27, 23, 6, 41, 585845), datetime.datetime(2025, 6, 27, 23, 6, 41, 585845))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-27 23:06:41.593 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 401 (0.445s)
