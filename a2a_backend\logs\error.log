2025-06-27 21:17:14.221 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: GET /api/v1/auth/login - 405 (0.003s)
2025-06-27 21:18:12.864 | ERROR    | app.api.v1.auth:login_user:344 - 用户登录异常: RateLimiter.check_rate_limit() got an unexpected keyword argument 'key'
2025-06-27 21:18:12.864 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.002s)
2025-06-27 21:21:24.605 | ERROR    | app.api.v1.auth:login_user:344 - 用户登录异常: RateLimiter.check_rate_limit() got an unexpected keyword argument 'key'
2025-06-27 21:21:24.605 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.000s)
2025-06-27 21:25:22.654 | ERROR    | app.api.v1.auth:login_user:341 - 用户登录异常: object bool can't be used in 'await' expression
2025-06-27 21:25:22.660 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.005s)
2025-06-27 21:30:44.851 | ERROR    | app.api.v1.auth:login_user:340 - 用户登录异常: UserService.authenticate_user() missing 1 required positional argument: 'password'
2025-06-27 21:30:44.851 | ERROR    | app.middleware.logging:dispatch:105 - 请求完成: POST /api/v1/auth/login - 500 (0.004s)
