import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import Cookies from 'js-cookie'
import type { UserInfo } from '@/types'
import { authApi } from '@/api/auth'

/**
 * 认证状态管理
 */
export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string | null>(Cookies.get('access_token') || null)
  const refreshToken = ref<string | null>(Cookies.get('refresh_token') || null)
  const user = ref<UserInfo | null>(null)
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)
  const isAdmin = computed(() => user.value?.role === 'admin' || user.value?.role === 'super_admin')

  /**
   * 登录
   */
  const login = async (credentials: { username: string; password: string; remember_me?: boolean }) => {
    try {
      loading.value = true
      const response = await authApi.login(credentials)

      // 后端响应格式适配
      const responseData = response.data

      // 保存token
      token.value = responseData.access_token
      refreshToken.value = responseData.refresh_token
      user.value = responseData.user_info

      // 保存到cookie
      const expires = credentials.remember_me ? 7 : 1 // 记住我7天，否则1天
      Cookies.set('access_token', responseData.access_token, { expires })
      Cookies.set('refresh_token', responseData.refresh_token, { expires: 7 })

      return response
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 注册
   */
  const register = async (userData: {
    username: string
    email: string
    password: string
    full_name?: string
    phone?: string
  }) => {
    try {
      loading.value = true
      const response = await authApi.register(userData)
      return response
    } catch (error) {
      console.error('注册失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 登出
   */
  const logout = async () => {
    try {
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('登出失败:', error)
    } finally {
      // 清除状态
      token.value = null
      refreshToken.value = null
      user.value = null
      
      // 清除cookie
      Cookies.remove('access_token')
      Cookies.remove('refresh_token')
      
      // 跳转到登录页
      window.location.href = '/login'
    }
  }

  /**
   * 刷新token
   */
  const refreshAccessToken = async () => {
    try {
      if (!refreshToken.value) {
        throw new Error('没有刷新令牌')
      }
      
      const response = await authApi.refreshToken(refreshToken.value)
      
      // 更新token
      token.value = response.data.access_token
      refreshToken.value = response.data.refresh_token
      
      // 更新cookie
      Cookies.set('access_token', response.data.access_token, { expires: 1 })
      Cookies.set('refresh_token', response.data.refresh_token, { expires: 7 })
      
      return response
    } catch (error) {
      console.error('刷新token失败:', error)
      await logout()
      throw error
    }
  }

  /**
   * 获取用户信息
   */
  const fetchUserInfo = async () => {
    try {
      if (!token.value) return

      const response = await authApi.verify()
      // 后端响应格式适配
      user.value = response.data
      return response
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 不要自动登出，让用户手动处理
      throw error
    }
  }

  /**
   * 检查认证状态
   */
  const checkAuth = async () => {
    if (token.value && !user.value) {
      try {
        await fetchUserInfo()
      } catch (error) {
        // 静默处理错误，不阻塞应用
        console.warn('检查认证状态失败:', error)
      }
    }
  }

  return {
    // 状态
    token,
    refreshToken,
    user,
    loading,
    
    // 计算属性
    isAuthenticated,
    isAdmin,
    
    // 方法
    login,
    register,
    logout,
    refreshAccessToken,
    fetchUserInfo,
    checkAuth
  }
})
