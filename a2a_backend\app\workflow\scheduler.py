# -*- coding: utf-8 -*-
"""
A2A多智能体系统工作流调度器

提供工作流任务的调度和分配、资源管理和优化
"""

import logging
import asyncio
import json
import uuid
from typing import Dict, List, Optional, Any, Union, Tuple, Set
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, field
from collections import defaultdict, deque
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func

from app.models.workflow import Workflow, WorkflowExecution
from app.models.user import User
from app.schemas.workflow import WorkflowExecutionStatus, WorkflowPriority
from app.core.database import get_db
from app.core.logging import get_logger
from app.core.config import get_settings


class TaskPriority(Enum):
    """任务优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4
    CRITICAL = 5


class ResourceType(Enum):
    """资源类型"""
    CPU = "cpu"
    MEMORY = "memory"
    GPU = "gpu"
    NETWORK = "network"
    STORAGE = "storage"


@dataclass
class ResourceRequirement:
    """资源需求"""
    cpu_cores: float = 0.1
    memory_mb: int = 100
    gpu_count: int = 0
    network_bandwidth: int = 0  # MB/s
    storage_mb: int = 0
    estimated_duration: float = 30.0  # seconds


@dataclass
class ScheduledTask:
    """调度任务"""
    execution_id: str
    workflow_id: int
    user_id: int
    priority: TaskPriority
    resource_requirements: ResourceRequirement
    created_at: datetime
    scheduled_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    estimated_completion: Optional[datetime] = None
    dependencies: List[str] = field(default_factory=list)
    retry_count: int = 0
    max_retries: int = 3
    timeout: Optional[int] = None
    config: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ResourcePool:
    """资源池"""
    total_cpu_cores: float = 4.0
    total_memory_mb: int = 8192
    total_gpu_count: int = 0
    total_network_bandwidth: int = 1000  # MB/s
    total_storage_mb: int = 10240
    
    used_cpu_cores: float = 0.0
    used_memory_mb: int = 0
    used_gpu_count: int = 0
    used_network_bandwidth: int = 0
    used_storage_mb: int = 0
    
    def available_cpu_cores(self) -> float:
        return max(0, self.total_cpu_cores - self.used_cpu_cores)
    
    def available_memory_mb(self) -> int:
        return max(0, self.total_memory_mb - self.used_memory_mb)
    
    def available_gpu_count(self) -> int:
        return max(0, self.total_gpu_count - self.used_gpu_count)
    
    def available_network_bandwidth(self) -> int:
        return max(0, self.total_network_bandwidth - self.used_network_bandwidth)
    
    def available_storage_mb(self) -> int:
        return max(0, self.total_storage_mb - self.used_storage_mb)
    
    def can_allocate(self, requirement: ResourceRequirement) -> bool:
        """检查是否可以分配资源"""
        return (
            self.available_cpu_cores() >= requirement.cpu_cores and
            self.available_memory_mb() >= requirement.memory_mb and
            self.available_gpu_count() >= requirement.gpu_count and
            self.available_network_bandwidth() >= requirement.network_bandwidth and
            self.available_storage_mb() >= requirement.storage_mb
        )
    
    def allocate(self, requirement: ResourceRequirement) -> bool:
        """分配资源"""
        if self.can_allocate(requirement):
            self.used_cpu_cores += requirement.cpu_cores
            self.used_memory_mb += requirement.memory_mb
            self.used_gpu_count += requirement.gpu_count
            self.used_network_bandwidth += requirement.network_bandwidth
            self.used_storage_mb += requirement.storage_mb
            return True
        return False
    
    def deallocate(self, requirement: ResourceRequirement) -> None:
        """释放资源"""
        self.used_cpu_cores = max(0, self.used_cpu_cores - requirement.cpu_cores)
        self.used_memory_mb = max(0, self.used_memory_mb - requirement.memory_mb)
        self.used_gpu_count = max(0, self.used_gpu_count - requirement.gpu_count)
        self.used_network_bandwidth = max(0, self.used_network_bandwidth - requirement.network_bandwidth)
        self.used_storage_mb = max(0, self.used_storage_mb - requirement.storage_mb)


class WorkflowScheduler:
    """
    工作流调度器类
    
    提供以下功能：
    1. 工作流任务的调度和分配
    2. 资源的管理和优化
    3. 优先级的处理和排队
    4. 负载均衡和性能优化
    5. 任务依赖关系管理
    6. 故障恢复和重试机制
    """
    
    def __init__(self, db, logger: Optional[logging.Logger] = None):
        """
        初始化工作流调度器
        
        Args:
            db: 数据库会话
            logger: 日志记录器
        """
        self.db = db
        self.logger = logger or get_logger("workflow_scheduler")
        self.settings = get_settings()
        
        # 任务队列（按优先级分组）
        self.task_queues: Dict[TaskPriority, deque] = {
            priority: deque() for priority in TaskPriority
        }
        
        # 运行中的任务
        self.running_tasks: Dict[str, ScheduledTask] = {}
        
        # 已完成的任务
        self.completed_tasks: Dict[str, ScheduledTask] = {}
        
        # 失败的任务
        self.failed_tasks: Dict[str, ScheduledTask] = {}
        
        # 资源池
        self.resource_pool = ResourcePool(
            total_cpu_cores=self.settings.scheduler_cpu_cores,
            total_memory_mb=self.settings.scheduler_memory_mb,
            total_gpu_count=self.settings.scheduler_gpu_count,
            total_network_bandwidth=self.settings.scheduler_network_bandwidth,
            total_storage_mb=self.settings.scheduler_storage_mb
        )
        
        # 调度配置
        self.max_concurrent_tasks = self.settings.max_concurrent_workflows
        self.scheduling_interval = 1.0  # 调度间隔（秒）
        self.resource_check_interval = 5.0  # 资源检查间隔（秒）
        self.cleanup_interval = 60.0  # 清理间隔（秒）
        
        # 性能统计
        self.stats = {
            "total_scheduled": 0,
            "total_completed": 0,
            "total_failed": 0,
            "average_wait_time": 0.0,
            "average_execution_time": 0.0,
            "resource_utilization": 0.0
        }
        
        # 调度器状态
        self.is_running = False
        self.scheduler_task: Optional[asyncio.Task] = None
        
        self.logger.info("WorkflowScheduler已初始化")
    
    async def start(self) -> None:
        """
        启动调度器
        """
        if self.is_running:
            self.logger.warning("调度器已在运行")
            return
        
        self.is_running = True
        self.scheduler_task = asyncio.create_task(self._scheduler_loop())
        
        self.logger.info("工作流调度器已启动")
    
    async def stop(self) -> None:
        """
        停止调度器
        """
        if not self.is_running:
            return
        
        self.is_running = False
        
        if self.scheduler_task:
            self.scheduler_task.cancel()
            try:
                await self.scheduler_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("工作流调度器已停止")
    
    async def schedule_workflow(
        self,
        execution_id: str,
        workflow_id: int,
        user_id: int,
        priority: TaskPriority = TaskPriority.NORMAL,
        resource_requirements: Optional[ResourceRequirement] = None,
        dependencies: Optional[List[str]] = None,
        config: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        调度工作流执行
        
        Args:
            execution_id: 执行ID
            workflow_id: 工作流ID
            user_id: 用户ID
            priority: 任务优先级
            resource_requirements: 资源需求
            dependencies: 依赖任务列表
            config: 配置信息
            
        Returns:
            bool: 是否调度成功
        """
        try:
            # 检查任务是否已存在
            if execution_id in self.running_tasks or execution_id in self.completed_tasks:
                self.logger.warning(f"任务已存在: {execution_id}")
                return False
            
            # 获取工作流信息
            workflow = self.db.query(Workflow).filter(Workflow.id == workflow_id).first()
            if not workflow:
                self.logger.error(f"工作流不存在: {workflow_id}")
                return False
            
            # 设置默认资源需求
            if resource_requirements is None:
                resource_requirements = await self._estimate_resource_requirements(workflow)
            
            # 创建调度任务
            scheduled_task = ScheduledTask(
                execution_id=execution_id,
                workflow_id=workflow_id,
                user_id=user_id,
                priority=priority,
                resource_requirements=resource_requirements,
                created_at=datetime.utcnow(),
                dependencies=dependencies or [],
                config=config or {},
                timeout=workflow.timeout,
                max_retries=workflow.max_retries or 3
            )
            
            # 添加到队列
            self.task_queues[priority].append(scheduled_task)
            self.stats["total_scheduled"] += 1
            
            self.logger.info(f"工作流已调度: {execution_id}, 优先级: {priority.name}")
            return True
        except Exception as e:
            self.logger.error(f"调度工作流错误: {str(e)}")
            return False
    
    async def _estimate_resource_requirements(self, workflow: Workflow) -> ResourceRequirement:
        """
        估算工作流资源需求
        
        Args:
            workflow: 工作流对象
            
        Returns:
            ResourceRequirement: 资源需求
        """
        try:
            definition = json.loads(workflow.definition)
            nodes = definition.get("nodes", [])
            
            # 基础资源需求
            cpu_cores = 0.1
            memory_mb = 100
            gpu_count = 0
            network_bandwidth = 0
            storage_mb = 0
            estimated_duration = 30.0
            
            # 根据节点数量和类型估算
            for node in nodes:
                node_config = node.get("config", {})
                node_type = node.get("type", "task")
                
                # 根据节点类型调整资源需求
                if node_type == "parallel":
                    cpu_cores += 0.2
                    memory_mb += 200
                elif node_type == "loop":
                    cpu_cores += 0.1
                    memory_mb += 100
                    estimated_duration += 10.0
                elif node_type == "branch":
                    cpu_cores += 0.05
                    memory_mb += 50
                else:
                    cpu_cores += 0.1
                    memory_mb += 100
                
                # 从配置中获取资源需求
                cpu_cores += node_config.get("cpu_cores", 0)
                memory_mb += node_config.get("memory_mb", 0)
                gpu_count += node_config.get("gpu_count", 0)
                network_bandwidth += node_config.get("network_bandwidth", 0)
                storage_mb += node_config.get("storage_mb", 0)
                estimated_duration += node_config.get("estimated_duration", 5.0)
            
            # 应用工作流级别的配置
            workflow_config = json.loads(workflow.config or "{}")
            cpu_cores = max(cpu_cores, workflow_config.get("min_cpu_cores", 0.1))
            memory_mb = max(memory_mb, workflow_config.get("min_memory_mb", 100))
            
            return ResourceRequirement(
                cpu_cores=min(cpu_cores, 2.0),  # 限制最大CPU使用
                memory_mb=min(memory_mb, 2048),  # 限制最大内存使用
                gpu_count=gpu_count,
                network_bandwidth=network_bandwidth,
                storage_mb=storage_mb,
                estimated_duration=estimated_duration
            )
        except Exception as e:
            self.logger.error(f"估算资源需求错误: {str(e)}")
            return ResourceRequirement()
    
    async def _scheduler_loop(self) -> None:
        """
        调度器主循环
        """
        last_resource_check = datetime.utcnow()
        last_cleanup = datetime.utcnow()
        
        while self.is_running:
            try:
                current_time = datetime.utcnow()
                
                # 执行调度
                await self._schedule_tasks()
                
                # 检查运行中的任务
                await self._check_running_tasks()
                
                # 定期资源检查
                if (current_time - last_resource_check).total_seconds() >= self.resource_check_interval:
                    await self._check_resource_usage()
                    last_resource_check = current_time
                
                # 定期清理
                if (current_time - last_cleanup).total_seconds() >= self.cleanup_interval:
                    await self._cleanup_completed_tasks()
                    last_cleanup = current_time
                
                # 更新统计信息
                await self._update_statistics()
                
                # 等待下一个调度周期
                await asyncio.sleep(self.scheduling_interval)
            except Exception as e:
                self.logger.error(f"调度器循环错误: {str(e)}")
                await asyncio.sleep(1.0)
    
    async def _schedule_tasks(self) -> None:
        """
        调度任务
        """
        try:
            # 检查是否达到最大并发数
            if len(self.running_tasks) >= self.max_concurrent_tasks:
                return
            
            # 按优先级顺序处理队列
            for priority in reversed(list(TaskPriority)):
                queue = self.task_queues[priority]
                
                while queue and len(self.running_tasks) < self.max_concurrent_tasks:
                    task = queue.popleft()
                    
                    # 检查依赖关系
                    if not await self._check_dependencies(task):
                        # 依赖未满足，重新加入队列末尾
                        queue.append(task)
                        break
                    
                    # 检查资源可用性
                    if not self.resource_pool.can_allocate(task.resource_requirements):
                        # 资源不足，重新加入队列开头
                        queue.appendleft(task)
                        break
                    
                    # 分配资源并启动任务
                    if await self._start_task(task):
                        self.running_tasks[task.execution_id] = task
                    else:
                        # 启动失败，重新加入队列
                        queue.appendleft(task)
                        break
        except Exception as e:
            self.logger.error(f"调度任务错误: {str(e)}")
    
    async def _check_dependencies(self, task: ScheduledTask) -> bool:
        """
        检查任务依赖关系
        
        Args:
            task: 调度任务
            
        Returns:
            bool: 依赖是否满足
        """
        try:
            for dependency_id in task.dependencies:
                # 检查依赖任务是否完成
                if dependency_id in self.running_tasks:
                    return False
                
                if dependency_id not in self.completed_tasks:
                    # 依赖任务不存在或未完成
                    return False
            
            return True
        except Exception as e:
            self.logger.error(f"检查依赖关系错误: {str(e)}")
            return False
    
    async def _start_task(self, task: ScheduledTask) -> bool:
        """
        启动任务
        
        Args:
            task: 调度任务
            
        Returns:
            bool: 是否启动成功
        """
        try:
            # 分配资源
            if not self.resource_pool.allocate(task.resource_requirements):
                self.logger.warning(f"资源分配失败: {task.execution_id}")
                return False
            
            # 更新任务状态
            task.scheduled_at = datetime.utcnow()
            task.started_at = datetime.utcnow()
            
            if task.resource_requirements.estimated_duration > 0:
                task.estimated_completion = task.started_at + timedelta(
                    seconds=task.resource_requirements.estimated_duration
                )
            
            # 更新数据库状态
            execution = self.db.query(WorkflowExecution).filter(
                WorkflowExecution.execution_id == task.execution_id
            ).first()
            
            if execution:
                execution.status = WorkflowExecutionStatus.RUNNING.value
                execution.started_at = task.started_at
                self.db.commit()
            
            # 这里应该调用工作流引擎启动执行
            # await workflow_engine.execute_workflow(task.execution_id, ...)
            
            self.logger.info(f"任务已启动: {task.execution_id}")
            return True
        except Exception as e:
            self.logger.error(f"启动任务错误: {str(e)}")
            # 释放已分配的资源
            self.resource_pool.deallocate(task.resource_requirements)
            return False
    
    async def _check_running_tasks(self) -> None:
        """
        检查运行中的任务
        """
        try:
            current_time = datetime.utcnow()
            completed_tasks = []
            failed_tasks = []
            
            for execution_id, task in self.running_tasks.items():
                # 检查任务状态
                execution = self.db.query(WorkflowExecution).filter(
                    WorkflowExecution.execution_id == execution_id
                ).first()
                
                if not execution:
                    # 执行记录不存在，标记为失败
                    failed_tasks.append(execution_id)
                    continue
                
                status = execution.status
                
                if status == WorkflowExecutionStatus.COMPLETED.value:
                    # 任务完成
                    completed_tasks.append(execution_id)
                elif status in [WorkflowExecutionStatus.FAILED.value, WorkflowExecutionStatus.CANCELLED.value]:
                    # 任务失败或取消
                    failed_tasks.append(execution_id)
                elif task.timeout and task.started_at:
                    # 检查超时
                    elapsed = (current_time - task.started_at).total_seconds()
                    if elapsed > task.timeout:
                        self.logger.warning(f"任务超时: {execution_id}")
                        failed_tasks.append(execution_id)
                        
                        # 更新数据库状态
                        execution.status = WorkflowExecutionStatus.FAILED.value
                        execution.completed_at = current_time
                        execution.error_info = json.dumps({
                            "error": "任务执行超时",
                            "timeout": task.timeout,
                            "elapsed": elapsed
                        }, ensure_ascii=False)
                        self.db.commit()
            
            # 处理完成的任务
            for execution_id in completed_tasks:
                await self._handle_task_completion(execution_id, True)
            
            # 处理失败的任务
            for execution_id in failed_tasks:
                await self._handle_task_completion(execution_id, False)
        except Exception as e:
            self.logger.error(f"检查运行任务错误: {str(e)}")
    
    async def _handle_task_completion(self, execution_id: str, success: bool) -> None:
        """
        处理任务完成
        
        Args:
            execution_id: 执行ID
            success: 是否成功
        """
        try:
            task = self.running_tasks.get(execution_id)
            if not task:
                return
            
            # 释放资源
            self.resource_pool.deallocate(task.resource_requirements)
            
            # 从运行队列移除
            del self.running_tasks[execution_id]
            
            if success:
                # 添加到完成队列
                self.completed_tasks[execution_id] = task
                self.stats["total_completed"] += 1
                self.logger.info(f"任务完成: {execution_id}")
            else:
                # 检查是否需要重试
                if task.retry_count < task.max_retries:
                    task.retry_count += 1
                    task.started_at = None
                    task.scheduled_at = None
                    
                    # 重新加入队列
                    self.task_queues[task.priority].appendleft(task)
                    self.logger.info(f"任务重试: {execution_id}, 重试次数: {task.retry_count}")
                else:
                    # 添加到失败队列
                    self.failed_tasks[execution_id] = task
                    self.stats["total_failed"] += 1
                    self.logger.error(f"任务失败: {execution_id}")
        except Exception as e:
            self.logger.error(f"处理任务完成错误: {str(e)}")
    
    async def _check_resource_usage(self) -> None:
        """
        检查资源使用情况
        """
        try:
            # 计算资源利用率
            cpu_utilization = self.resource_pool.used_cpu_cores / self.resource_pool.total_cpu_cores
            memory_utilization = self.resource_pool.used_memory_mb / self.resource_pool.total_memory_mb
            
            overall_utilization = (cpu_utilization + memory_utilization) / 2
            self.stats["resource_utilization"] = overall_utilization
            
            # 记录资源使用情况
            self.logger.debug(
                f"资源使用情况 - CPU: {cpu_utilization:.2%}, "
                f"内存: {memory_utilization:.2%}, "
                f"整体: {overall_utilization:.2%}"
            )
            
            # 如果资源利用率过高，可以考虑调整调度策略
            if overall_utilization > 0.9:
                self.logger.warning("资源利用率过高，考虑限制新任务调度")
        except Exception as e:
            self.logger.error(f"检查资源使用错误: {str(e)}")
    
    async def _cleanup_completed_tasks(self) -> None:
        """
        清理已完成的任务
        """
        try:
            current_time = datetime.utcnow()
            cleanup_threshold = timedelta(hours=24)  # 24小时后清理
            
            # 清理完成的任务
            completed_to_remove = []
            for execution_id, task in self.completed_tasks.items():
                if task.started_at and (current_time - task.started_at) > cleanup_threshold:
                    completed_to_remove.append(execution_id)
            
            for execution_id in completed_to_remove:
                del self.completed_tasks[execution_id]
            
            # 清理失败的任务
            failed_to_remove = []
            for execution_id, task in self.failed_tasks.items():
                if task.started_at and (current_time - task.started_at) > cleanup_threshold:
                    failed_to_remove.append(execution_id)
            
            for execution_id in failed_to_remove:
                del self.failed_tasks[execution_id]
            
            if completed_to_remove or failed_to_remove:
                self.logger.info(
                    f"清理任务 - 完成: {len(completed_to_remove)}, 失败: {len(failed_to_remove)}"
                )
        except Exception as e:
            self.logger.error(f"清理任务错误: {str(e)}")
    
    async def _update_statistics(self) -> None:
        """
        更新统计信息
        """
        try:
            # 计算平均等待时间
            wait_times = []
            for task in self.running_tasks.values():
                if task.created_at and task.started_at:
                    wait_time = (task.started_at - task.created_at).total_seconds()
                    wait_times.append(wait_time)
            
            if wait_times:
                self.stats["average_wait_time"] = sum(wait_times) / len(wait_times)
            
            # 计算平均执行时间
            execution_times = []
            for task in self.completed_tasks.values():
                if task.started_at:
                    # 从数据库获取完成时间
                    execution = self.db.query(WorkflowExecution).filter(
                        WorkflowExecution.execution_id == task.execution_id
                    ).first()
                    
                    if execution and execution.completed_at:
                        execution_time = (execution.completed_at - task.started_at).total_seconds()
                        execution_times.append(execution_time)
            
            if execution_times:
                self.stats["average_execution_time"] = sum(execution_times) / len(execution_times)
        except Exception as e:
            self.logger.error(f"更新统计信息错误: {str(e)}")
    
    async def cancel_task(self, execution_id: str) -> bool:
        """
        取消任务
        
        Args:
            execution_id: 执行ID
            
        Returns:
            bool: 是否取消成功
        """
        try:
            # 检查运行中的任务
            if execution_id in self.running_tasks:
                task = self.running_tasks[execution_id]
                
                # 释放资源
                self.resource_pool.deallocate(task.resource_requirements)
                
                # 从运行队列移除
                del self.running_tasks[execution_id]
                
                # 更新数据库状态
                execution = self.db.query(WorkflowExecution).filter(
                    WorkflowExecution.execution_id == execution_id
                ).first()
                
                if execution:
                    execution.status = WorkflowExecutionStatus.CANCELLED.value
                    execution.completed_at = datetime.utcnow()
                    self.db.commit()
                
                self.logger.info(f"任务已取消: {execution_id}")
                return True
            
            # 检查队列中的任务
            for priority, queue in self.task_queues.items():
                for i, task in enumerate(queue):
                    if task.execution_id == execution_id:
                        del queue[i]
                        self.logger.info(f"队列中的任务已取消: {execution_id}")
                        return True
            
            return False
        except Exception as e:
            self.logger.error(f"取消任务错误: {str(e)}")
            return False
    
    async def get_scheduler_status(self) -> Dict[str, Any]:
        """
        获取调度器状态
        
        Returns:
            Dict[str, Any]: 调度器状态信息
        """
        try:
            # 统计队列中的任务
            queue_stats = {}
            total_queued = 0
            for priority, queue in self.task_queues.items():
                count = len(queue)
                queue_stats[priority.name] = count
                total_queued += count
            
            return {
                "is_running": self.is_running,
                "running_tasks": len(self.running_tasks),
                "queued_tasks": total_queued,
                "completed_tasks": len(self.completed_tasks),
                "failed_tasks": len(self.failed_tasks),
                "queue_stats": queue_stats,
                "resource_pool": {
                    "cpu_utilization": self.resource_pool.used_cpu_cores / self.resource_pool.total_cpu_cores,
                    "memory_utilization": self.resource_pool.used_memory_mb / self.resource_pool.total_memory_mb,
                    "available_cpu": self.resource_pool.available_cpu_cores(),
                    "available_memory": self.resource_pool.available_memory_mb()
                },
                "statistics": self.stats
            }
        except Exception as e:
            self.logger.error(f"获取调度器状态错误: {str(e)}")
            return {}
    
    async def update_task_priority(self, execution_id: str, new_priority: TaskPriority) -> bool:
        """
        更新任务优先级
        
        Args:
            execution_id: 执行ID
            new_priority: 新优先级
            
        Returns:
            bool: 是否更新成功
        """
        try:
            # 在队列中查找任务
            for priority, queue in self.task_queues.items():
                for i, task in enumerate(queue):
                    if task.execution_id == execution_id:
                        # 从当前队列移除
                        del queue[i]
                        
                        # 更新优先级
                        task.priority = new_priority
                        
                        # 添加到新队列
                        self.task_queues[new_priority].append(task)
                        
                        self.logger.info(f"任务优先级已更新: {execution_id}, 新优先级: {new_priority.name}")
                        return True
            
            return False
        except Exception as e:
            self.logger.error(f"更新任务优先级错误: {str(e)}")
            return False