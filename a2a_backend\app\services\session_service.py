# -*- coding: utf-8 -*-
"""
A2A多智能体系统会话服务

提供会话的创建、管理、状态恢复和上下文管理功能
"""

import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union
from sqlalchemy import and_, or_, desc, func
from sqlalchemy.orm import Session, selectinload
from loguru import logger

from app.core.database import get_database_manager
from app.models.session import Session as SessionModel, SessionContext, SessionParticipant
from app.models.agent import Agent
from app.models.user import User
from app.services.auth_service import AuthService
from app.core.config import get_settings


class SessionService:
    """
    会话服务
    
    提供会话的创建、管理、状态恢复和上下文管理功能
    """
    
    def __init__(self):
        self.db_manager = get_database_manager()
        self.auth_service = AuthService()
        self.settings = get_settings()
    
    async def create_session(self, user_id: int, session_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建会话
        
        Args:
            user_id: 用户ID
            session_data: 会话数据
            
        Returns:
            Dict[str, Any]: 创建结果
            
        Raises:
            ValueError: 参数无效
            Exception: 创建失败
        """
        async with self.db_manager.get_session() as session:
            try:
                # 验证用户权限
                has_permission = await self.auth_service.check_permission(
                    user_id, "session", None, "create"
                )
                if not has_permission:
                    raise ValueError("没有创建会话的权限")
                
                # 验证必需字段
                required_fields = ["title", "type"]
                for field in required_fields:
                    if field not in session_data:
                        raise ValueError(f"缺少必需字段: {field}")
                
                # 验证智能体ID（如果提供）
                agent_id = session_data.get("agent_id")
                if agent_id:
                    agent_result = await session.execute(
                        session.query(Agent).filter(
                            and_(
                                Agent.id == agent_id,
                                Agent.deleted_at.is_(None),
                                Agent.status.in_(["active", "inactive"])
                            )
                        )
                    )
                    agent = agent_result.scalar_one_or_none()
                    
                    if not agent:
                        raise ValueError("智能体不存在或不可用")
                    
                    # 检查智能体访问权限
                    is_owner = await self.auth_service.check_resource_ownership(
                        user_id, "agent", agent_id
                    )
                    has_execute_permission = await self.auth_service.check_permission(
                        user_id, "agent", agent_id, "execute"
                    )
                    
                    if not is_owner and not has_execute_permission and not agent.is_public:
                        raise ValueError("没有使用该智能体的权限")
                
                # 创建会话记录
                session_id = str(uuid.uuid4())
                new_session = SessionModel(
                    id=session_id,
                    user_id=user_id,
                    agent_id=agent_id,
                    title=session_data["title"],
                    type=session_data["type"],
                    description=session_data.get("description", ""),
                    status="active",
                    settings=json.dumps(session_data.get("settings", {})),
                    metadata=json.dumps(session_data.get("metadata", {})),
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                
                session.add(new_session)
                await session.flush()  # 获取session.id
                
                # 创建初始上下文
                initial_context = SessionContext(
                    id=str(uuid.uuid4()),
                    session_id=session_id,
                    context_data=json.dumps({
                        "messages": [],
                        "variables": session_data.get("initial_variables", {}),
                        "state": "initialized"
                    }),
                    version=1,
                    is_current=True,
                    created_at=datetime.utcnow()
                )
                
                session.add(initial_context)
                
                # 添加会话参与者（创建者）
                participant = SessionParticipant(
                    id=str(uuid.uuid4()),
                    session_id=session_id,
                    user_id=user_id,
                    role="owner",
                    permissions=json.dumps(["read", "write", "manage"]),
                    joined_at=datetime.utcnow()
                )
                
                session.add(participant)
                
                # 添加其他参与者（如果提供）
                participants = session_data.get("participants", [])
                for participant_data in participants:
                    if participant_data["user_id"] != user_id:  # 避免重复添加创建者
                        participant = SessionParticipant(
                            id=str(uuid.uuid4()),
                            session_id=session_id,
                            user_id=participant_data["user_id"],
                            role=participant_data.get("role", "participant"),
                            permissions=json.dumps(participant_data.get("permissions", ["read"])),
                            joined_at=datetime.utcnow()
                        )
                        session.add(participant)
                
                await session.commit()
                
                logger.info(f"会话创建成功: {new_session.title}", extra={
                    "user_id": user_id,
                    "session_id": session_id,
                    "session_type": new_session.type,
                    "agent_id": agent_id
                })
                
                return {
                    "success": True,
                    "message": "会话创建成功",
                    "data": {
                        "session_id": session_id,
                        "title": new_session.title,
                        "type": new_session.type,
                        "status": new_session.status,
                        "created_at": new_session.created_at.isoformat()
                    }
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"会话创建失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"会话创建异常: {str(e)}")
                raise Exception(f"会话创建失败: {str(e)}")
    
    async def get_session(self, user_id: int, session_id: str) -> Dict[str, Any]:
        """
        获取会话信息
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            
        Returns:
            Dict[str, Any]: 会话信息
            
        Raises:
            ValueError: 会话不存在或无权限
        """
        async with self.db_manager.get_session() as session:
            try:
                # 查询会话
                result = await session.execute(
                    session.query(SessionModel)
                    .options(
                        selectinload(SessionModel.contexts),
                        selectinload(SessionModel.participants),
                        selectinload(SessionModel.agent)
                    )
                    .filter(
                        and_(
                            SessionModel.id == session_id,
                            SessionModel.deleted_at.is_(None)
                        )
                    )
                )
                session_obj = result.scalar_one_or_none()
                
                if not session_obj:
                    raise ValueError("会话不存在")
                
                # 检查访问权限
                has_access = False
                user_role = None
                user_permissions = []
                
                for participant in session_obj.participants:
                    if participant.user_id == user_id and participant.left_at is None:
                        has_access = True
                        user_role = participant.role
                        user_permissions = json.loads(participant.permissions)
                        break
                
                if not has_access:
                    # 检查是否有通用读取权限
                    has_permission = await self.auth_service.check_permission(
                        user_id, "session", session_id, "read"
                    )
                    if not has_permission:
                        raise ValueError("没有访问权限")
                
                # 获取当前上下文
                current_context = None
                for context in session_obj.contexts:
                    if context.is_current:
                        current_context = context
                        break
                
                # 格式化参与者信息
                participants = []
                for participant in session_obj.participants:
                    if participant.left_at is None:  # 只显示活跃参与者
                        participants.append({
                            "user_id": participant.user_id,
                            "role": participant.role,
                            "permissions": json.loads(participant.permissions),
                            "joined_at": participant.joined_at.isoformat()
                        })
                
                return {
                    "session_id": session_obj.id,
                    "title": session_obj.title,
                    "type": session_obj.type,
                    "description": session_obj.description,
                    "status": session_obj.status,
                    "settings": json.loads(session_obj.settings) if session_obj.settings else {},
                    "metadata": json.loads(session_obj.metadata) if session_obj.metadata else {},
                    "agent": {
                        "agent_id": session_obj.agent.id,
                        "name": session_obj.agent.name,
                        "type": session_obj.agent.type
                    } if session_obj.agent else None,
                    "context": {
                        "version": current_context.version,
                        "data": json.loads(current_context.context_data),
                        "updated_at": current_context.created_at.isoformat()
                    } if current_context else None,
                    "participants": participants,
                    "user_role": user_role,
                    "user_permissions": user_permissions,
                    "created_at": session_obj.created_at.isoformat(),
                    "updated_at": session_obj.updated_at.isoformat()
                }
                
            except ValueError as e:
                logger.warning(f"获取会话失败: {str(e)}")
                raise
            except Exception as e:
                logger.error(f"获取会话异常: {str(e)}")
                raise Exception(f"获取会话失败: {str(e)}")
    
    async def update_session(self, user_id: int, session_id: str, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新会话
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            update_data: 更新数据
            
        Returns:
            Dict[str, Any]: 更新结果
        """
        async with self.db_manager.get_session() as session:
            try:
                # 检查管理权限
                has_manage_permission = await self._check_session_permission(
                    session, user_id, session_id, "manage"
                )
                if not has_manage_permission:
                    raise ValueError("没有管理权限")
                
                # 查询会话
                result = await session.execute(
                    session.query(SessionModel).filter(
                        and_(
                            SessionModel.id == session_id,
                            SessionModel.deleted_at.is_(None)
                        )
                    )
                )
                session_obj = result.scalar_one_or_none()
                
                if not session_obj:
                    raise ValueError("会话不存在")
                
                # 更新基本信息
                if "title" in update_data:
                    session_obj.title = update_data["title"]
                
                if "description" in update_data:
                    session_obj.description = update_data["description"]
                
                if "status" in update_data:
                    if update_data["status"] in ["active", "paused", "completed", "archived"]:
                        session_obj.status = update_data["status"]
                    else:
                        raise ValueError("无效的会话状态")
                
                if "settings" in update_data:
                    session_obj.settings = json.dumps(update_data["settings"])
                
                if "metadata" in update_data:
                    session_obj.metadata = json.dumps(update_data["metadata"])
                
                session_obj.updated_at = datetime.utcnow()
                await session.commit()
                
                logger.info(f"会话更新成功: {session_obj.title}", extra={
                    "user_id": user_id,
                    "session_id": session_id
                })
                
                return {
                    "success": True,
                    "message": "会话更新成功",
                    "data": {
                        "session_id": session_id,
                        "updated_at": session_obj.updated_at.isoformat()
                    }
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"会话更新失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"会话更新异常: {str(e)}")
                raise Exception(f"会话更新失败: {str(e)}")
    
    async def delete_session(self, user_id: int, session_id: str, hard_delete: bool = False) -> Dict[str, Any]:
        """
        删除会话
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            hard_delete: 是否硬删除
            
        Returns:
            Dict[str, Any]: 删除结果
        """
        async with self.db_manager.get_session() as session:
            try:
                # 检查所有权或管理权限
                is_owner = await self.auth_service.check_resource_ownership(
                    user_id, "session", session_id
                )
                has_manage_permission = await self._check_session_permission(
                    session, user_id, session_id, "manage"
                )
                
                if not is_owner and not has_manage_permission:
                    raise ValueError("没有删除权限")
                
                # 查询会话
                result = await session.execute(
                    session.query(SessionModel).filter(
                        and_(
                            SessionModel.id == session_id,
                            SessionModel.deleted_at.is_(None) if not hard_delete else True
                        )
                    )
                )
                session_obj = result.scalar_one_or_none()
                
                if not session_obj:
                    raise ValueError("会话不存在")
                
                if hard_delete:
                    # 硬删除：删除所有相关记录
                    await session.execute(
                        session.query(SessionContext).filter(
                            SessionContext.session_id == session_id
                        ).delete()
                    )
                    
                    await session.execute(
                        session.query(SessionParticipant).filter(
                            SessionParticipant.session_id == session_id
                        ).delete()
                    )
                    
                    await session.delete(session_obj)
                    
                    logger.info(f"会话硬删除成功: {session_obj.title}", extra={
                        "user_id": user_id,
                        "session_id": session_id
                    })
                    
                    message = "会话已永久删除"
                else:
                    # 软删除：标记删除时间
                    session_obj.deleted_at = datetime.utcnow()
                    session_obj.status = "deleted"
                    session_obj.updated_at = datetime.utcnow()
                    
                    logger.info(f"会话软删除成功: {session_obj.title}", extra={
                        "user_id": user_id,
                        "session_id": session_id
                    })
                    
                    message = "会话已删除"
                
                await session.commit()
                
                return {
                    "success": True,
                    "message": message
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"会话删除失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"会话删除异常: {str(e)}")
                raise Exception(f"会话删除失败: {str(e)}")
    
    async def list_sessions(self, user_id: int, filters: Optional[Dict[str, Any]] = None, 
                           page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """
        获取会话列表
        
        Args:
            user_id: 用户ID
            filters: 过滤条件
            page: 页码
            page_size: 页面大小
            
        Returns:
            Dict[str, Any]: 会话列表
        """
        async with self.db_manager.get_session() as session:
            try:
                # 构建查询条件
                query_conditions = [
                    SessionModel.deleted_at.is_(None)
                ]
                
                # 权限过滤：只显示用户参与的会话
                participant_subquery = session.query(SessionParticipant.session_id).filter(
                    and_(
                        SessionParticipant.user_id == user_id,
                        SessionParticipant.left_at.is_(None)
                    )
                ).subquery()
                
                query_conditions.append(
                    SessionModel.id.in_(participant_subquery)
                )
                
                # 应用过滤条件
                if filters:
                    if "type" in filters:
                        query_conditions.append(SessionModel.type == filters["type"])
                    
                    if "status" in filters:
                        query_conditions.append(SessionModel.status == filters["status"])
                    
                    if "title" in filters:
                        query_conditions.append(
                            SessionModel.title.ilike(f"%{filters['title']}%")
                        )
                    
                    if "agent_id" in filters:
                        query_conditions.append(SessionModel.agent_id == filters["agent_id"])
                    
                    if "created_after" in filters:
                        query_conditions.append(
                            SessionModel.created_at >= datetime.fromisoformat(filters["created_after"])
                        )
                    
                    if "created_before" in filters:
                        query_conditions.append(
                            SessionModel.created_at <= datetime.fromisoformat(filters["created_before"])
                        )
                
                # 计算总数
                count_result = await session.execute(
                    session.query(func.count(SessionModel.id)).filter(
                        and_(*query_conditions)
                    )
                )
                total = count_result.scalar()
                
                # 分页查询
                offset = (page - 1) * page_size
                result = await session.execute(
                    session.query(SessionModel)
                    .options(
                        selectinload(SessionModel.agent),
                        selectinload(SessionModel.participants)
                    )
                    .filter(and_(*query_conditions))
                    .order_by(desc(SessionModel.updated_at))
                    .offset(offset)
                    .limit(page_size)
                )
                sessions = result.scalars().all()
                
                # 格式化结果
                session_list = []
                for session_obj in sessions:
                    # 获取用户在该会话中的角色
                    user_role = None
                    for participant in session_obj.participants:
                        if participant.user_id == user_id and participant.left_at is None:
                            user_role = participant.role
                            break
                    
                    # 统计参与者数量
                    active_participants = sum(
                        1 for p in session_obj.participants if p.left_at is None
                    )
                    
                    session_list.append({
                        "session_id": session_obj.id,
                        "title": session_obj.title,
                        "type": session_obj.type,
                        "description": session_obj.description,
                        "status": session_obj.status,
                        "agent": {
                            "agent_id": session_obj.agent.id,
                            "name": session_obj.agent.name,
                            "type": session_obj.agent.type
                        } if session_obj.agent else None,
                        "user_role": user_role,
                        "participant_count": active_participants,
                        "created_at": session_obj.created_at.isoformat(),
                        "updated_at": session_obj.updated_at.isoformat()
                    })
                
                return {
                    "sessions": session_list,
                    "pagination": {
                        "page": page,
                        "page_size": page_size,
                        "total": total,
                        "pages": (total + page_size - 1) // page_size
                    }
                }
                
            except Exception as e:
                logger.error(f"获取会话列表异常: {str(e)}")
                raise Exception(f"获取会话列表失败: {str(e)}")
    
    async def update_context(self, user_id: int, session_id: str, context_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新会话上下文
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            context_data: 上下文数据
            
        Returns:
            Dict[str, Any]: 更新结果
        """
        async with self.db_manager.get_session() as session:
            try:
                # 检查写入权限
                has_write_permission = await self._check_session_permission(
                    session, user_id, session_id, "write"
                )
                if not has_write_permission:
                    raise ValueError("没有写入权限")
                
                # 获取当前上下文
                current_context_result = await session.execute(
                    session.query(SessionContext).filter(
                        and_(
                            SessionContext.session_id == session_id,
                            SessionContext.is_current == True
                        )
                    )
                )
                current_context = current_context_result.scalar_one_or_none()
                
                if current_context:
                    # 标记当前上下文为非当前
                    current_context.is_current = False
                    
                    # 创建新的上下文版本
                    new_version = current_context.version + 1
                else:
                    new_version = 1
                
                # 创建新的上下文记录
                new_context = SessionContext(
                    id=str(uuid.uuid4()),
                    session_id=session_id,
                    context_data=json.dumps(context_data),
                    version=new_version,
                    is_current=True,
                    created_at=datetime.utcnow()
                )
                
                session.add(new_context)
                
                # 更新会话的更新时间
                await session.execute(
                    session.query(SessionModel)
                    .filter(SessionModel.id == session_id)
                    .update({"updated_at": datetime.utcnow()})
                )
                
                await session.commit()
                
                logger.info(f"会话上下文更新成功", extra={
                    "user_id": user_id,
                    "session_id": session_id,
                    "version": new_version
                })
                
                return {
                    "success": True,
                    "message": "上下文更新成功",
                    "data": {
                        "context_id": new_context.id,
                        "version": new_version,
                        "updated_at": new_context.created_at.isoformat()
                    }
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"上下文更新失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"上下文更新异常: {str(e)}")
                raise Exception(f"上下文更新失败: {str(e)}")
    
    async def restore_context(self, user_id: int, session_id: str, version: int) -> Dict[str, Any]:
        """
        恢复会话上下文到指定版本
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            version: 版本号
            
        Returns:
            Dict[str, Any]: 恢复结果
        """
        async with self.db_manager.get_session() as session:
            try:
                # 检查管理权限
                has_manage_permission = await self._check_session_permission(
                    session, user_id, session_id, "manage"
                )
                if not has_manage_permission:
                    raise ValueError("没有管理权限")
                
                # 查找指定版本的上下文
                target_context_result = await session.execute(
                    session.query(SessionContext).filter(
                        and_(
                            SessionContext.session_id == session_id,
                            SessionContext.version == version
                        )
                    )
                )
                target_context = target_context_result.scalar_one_or_none()
                
                if not target_context:
                    raise ValueError(f"版本 {version} 的上下文不存在")
                
                # 标记所有上下文为非当前
                await session.execute(
                    session.query(SessionContext)
                    .filter(SessionContext.session_id == session_id)
                    .update({"is_current": False})
                )
                
                # 创建新的上下文记录（基于目标版本）
                current_context_result = await session.execute(
                    session.query(SessionContext).filter(
                        SessionContext.session_id == session_id
                    ).order_by(desc(SessionContext.version))
                )
                latest_context = current_context_result.first()
                new_version = latest_context.version + 1 if latest_context else 1
                
                restored_context = SessionContext(
                    id=str(uuid.uuid4()),
                    session_id=session_id,
                    context_data=target_context.context_data,
                    version=new_version,
                    is_current=True,
                    created_at=datetime.utcnow()
                )
                
                session.add(restored_context)
                
                # 更新会话的更新时间
                await session.execute(
                    session.query(SessionModel)
                    .filter(SessionModel.id == session_id)
                    .update({"updated_at": datetime.utcnow()})
                )
                
                await session.commit()
                
                logger.info(f"会话上下文恢复成功", extra={
                    "user_id": user_id,
                    "session_id": session_id,
                    "restored_from_version": version,
                    "new_version": new_version
                })
                
                return {
                    "success": True,
                    "message": f"上下文已恢复到版本 {version}",
                    "data": {
                        "context_id": restored_context.id,
                        "restored_from_version": version,
                        "new_version": new_version,
                        "context_data": json.loads(restored_context.context_data),
                        "restored_at": restored_context.created_at.isoformat()
                    }
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"上下文恢复失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"上下文恢复异常: {str(e)}")
                raise Exception(f"上下文恢复失败: {str(e)}")
    
    async def get_context_history(self, user_id: int, session_id: str, 
                                 page: int = 1, page_size: int = 10) -> Dict[str, Any]:
        """
        获取会话上下文历史
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            page: 页码
            page_size: 页面大小
            
        Returns:
            Dict[str, Any]: 上下文历史
        """
        async with self.db_manager.get_session() as session:
            try:
                # 检查读取权限
                has_read_permission = await self._check_session_permission(
                    session, user_id, session_id, "read"
                )
                if not has_read_permission:
                    raise ValueError("没有读取权限")
                
                # 计算总数
                count_result = await session.execute(
                    session.query(func.count(SessionContext.id)).filter(
                        SessionContext.session_id == session_id
                    )
                )
                total = count_result.scalar()
                
                # 分页查询
                offset = (page - 1) * page_size
                result = await session.execute(
                    session.query(SessionContext)
                    .filter(SessionContext.session_id == session_id)
                    .order_by(desc(SessionContext.version))
                    .offset(offset)
                    .limit(page_size)
                )
                contexts = result.scalars().all()
                
                # 格式化结果
                context_list = []
                for context in contexts:
                    context_data = json.loads(context.context_data)
                    context_list.append({
                        "context_id": context.id,
                        "version": context.version,
                        "is_current": context.is_current,
                        "summary": {
                            "message_count": len(context_data.get("messages", [])),
                            "variable_count": len(context_data.get("variables", {})),
                            "state": context_data.get("state", "unknown")
                        },
                        "created_at": context.created_at.isoformat()
                    })
                
                return {
                    "contexts": context_list,
                    "pagination": {
                        "page": page,
                        "page_size": page_size,
                        "total": total,
                        "pages": (total + page_size - 1) // page_size
                    }
                }
                
            except ValueError as e:
                logger.warning(f"获取上下文历史失败: {str(e)}")
                raise
            except Exception as e:
                logger.error(f"获取上下文历史异常: {str(e)}")
                raise Exception(f"获取上下文历史失败: {str(e)}")
    
    async def add_participant(self, user_id: int, session_id: str, 
                             participant_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        添加会话参与者
        
        Args:
            user_id: 操作用户ID
            session_id: 会话ID
            participant_data: 参与者数据
            
        Returns:
            Dict[str, Any]: 添加结果
        """
        async with self.db_manager.get_session() as session:
            try:
                # 检查管理权限
                has_manage_permission = await self._check_session_permission(
                    session, user_id, session_id, "manage"
                )
                if not has_manage_permission:
                    raise ValueError("没有管理权限")
                
                # 验证参与者用户是否存在
                participant_user_id = participant_data["user_id"]
                user_result = await session.execute(
                    session.query(User).filter(
                        and_(
                            User.id == participant_user_id,
                            User.is_active == True,
                            User.deleted_at.is_(None)
                        )
                    )
                )
                participant_user = user_result.scalar_one_or_none()
                
                if not participant_user:
                    raise ValueError("参与者用户不存在")
                
                # 检查是否已经是参与者
                existing_result = await session.execute(
                    session.query(SessionParticipant).filter(
                        and_(
                            SessionParticipant.session_id == session_id,
                            SessionParticipant.user_id == participant_user_id,
                            SessionParticipant.left_at.is_(None)
                        )
                    )
                )
                if existing_result.scalar_one_or_none():
                    raise ValueError("用户已经是会话参与者")
                
                # 添加参与者
                participant = SessionParticipant(
                    id=str(uuid.uuid4()),
                    session_id=session_id,
                    user_id=participant_user_id,
                    role=participant_data.get("role", "participant"),
                    permissions=json.dumps(participant_data.get("permissions", ["read"])),
                    joined_at=datetime.utcnow()
                )
                
                session.add(participant)
                await session.commit()
                
                logger.info(f"会话参与者添加成功", extra={
                    "user_id": user_id,
                    "session_id": session_id,
                    "participant_user_id": participant_user_id
                })
                
                return {
                    "success": True,
                    "message": "参与者添加成功",
                    "data": {
                        "participant_id": participant.id,
                        "user_id": participant_user_id,
                        "role": participant.role,
                        "joined_at": participant.joined_at.isoformat()
                    }
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"添加参与者失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"添加参与者异常: {str(e)}")
                raise Exception(f"添加参与者失败: {str(e)}")
    
    async def remove_participant(self, user_id: int, session_id: str, participant_user_id: int) -> Dict[str, Any]:
        """
        移除会话参与者
        
        Args:
            user_id: 操作用户ID
            session_id: 会话ID
            participant_user_id: 参与者用户ID
            
        Returns:
            Dict[str, Any]: 移除结果
        """
        async with self.db_manager.get_session() as session:
            try:
                # 检查管理权限
                has_manage_permission = await self._check_session_permission(
                    session, user_id, session_id, "manage"
                )
                if not has_manage_permission:
                    raise ValueError("没有管理权限")
                
                # 查找参与者
                participant_result = await session.execute(
                    session.query(SessionParticipant).filter(
                        and_(
                            SessionParticipant.session_id == session_id,
                            SessionParticipant.user_id == participant_user_id,
                            SessionParticipant.left_at.is_(None)
                        )
                    )
                )
                participant = participant_result.scalar_one_or_none()
                
                if not participant:
                    raise ValueError("参与者不存在")
                
                # 不能移除会话所有者
                if participant.role == "owner":
                    raise ValueError("不能移除会话所有者")
                
                # 标记参与者离开
                participant.left_at = datetime.utcnow()
                await session.commit()
                
                logger.info(f"会话参与者移除成功", extra={
                    "user_id": user_id,
                    "session_id": session_id,
                    "participant_user_id": participant_user_id
                })
                
                return {
                    "success": True,
                    "message": "参与者移除成功"
                }
                
            except ValueError as e:
                await session.rollback()
                logger.warning(f"移除参与者失败: {str(e)}")
                raise
            except Exception as e:
                await session.rollback()
                logger.error(f"移除参与者异常: {str(e)}")
                raise Exception(f"移除参与者失败: {str(e)}")
    
    async def _check_session_permission(self, session, user_id: int, 
                                       session_id: str, permission: str) -> bool:
        """
        检查会话权限
        
        Args:
            session: 数据库会话
            user_id: 用户ID
            session_id: 会话ID
            permission: 权限名称
            
        Returns:
            bool: 是否有权限
        """
        try:
            # 查找用户在会话中的参与记录
            participant_result = await session.execute(
                session.query(SessionParticipant).filter(
                    and_(
                        SessionParticipant.session_id == session_id,
                        SessionParticipant.user_id == user_id,
                        SessionParticipant.left_at.is_(None)
                    )
                )
            )
            participant = participant_result.scalar_one_or_none()
            
            if participant:
                user_permissions = json.loads(participant.permissions)
                return permission in user_permissions
            
            # 检查系统级权限
            return await self.auth_service.check_permission(
                user_id, "session", session_id, permission
            )
            
        except Exception as e:
            logger.error(f"检查会话权限异常: {str(e)}")
            return False