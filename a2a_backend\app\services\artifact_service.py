# -*- coding: utf-8 -*-
"""
A2A多智能体系统工件服务

提供工件的上传、下载、版本管理、权限控制和元数据管理功能
"""

import os
import hashlib
import gzip
import json
import mimetypes
from datetime import datetime
from typing import Optional, List, Dict, Any, BinaryIO, Tu<PERSON>
from pathlib import Path
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func
from sqlalchemy.exc import IntegrityError
from fastapi import HTTPException, status, UploadFile
from cachetools import TTLCache
import threading
import shutil
from io import BytesIO

from app.models.artifact import Artifact, ArtifactShare, ArtifactVersion
from app.models.user import User
from app.core.database import get_db
from app.core.logging import get_logger
from app.core.config import get_settings
from app.core.cache import get_cache_manager, CacheLevel
from app.core.storage import (
    get_chunk_storage_manager, 
    get_compression_manager, 
    get_database_manager,
    get_storage_cleanup_manager
)
from app.auth.dependencies import get_current_user

logger = get_logger(__name__)


class ArtifactService:
    """
    工件服务类
    
    提供工件的存储、管理、版本控制和权限管理功能
    """
    
    def __init__(self, db = None):
        """
        初始化工件服务
        
        Args:
            db: 数据库会话
        """
        self.db = db or next(get_db())
        self.logger = get_logger(self.__class__.__name__)
        self.settings = get_settings()
        
        # 初始化管理器
        self.cache_manager = get_cache_manager()
        self.chunk_storage_manager = get_chunk_storage_manager()
        self.compression_manager = get_compression_manager()
        self.db_manager = get_database_manager()
        self.cleanup_manager = get_storage_cleanup_manager()
        
        # 工件缓存（TTL缓存，10分钟过期）
        self._artifact_cache = TTLCache(maxsize=500, ttl=600)
        self._cache_lock = threading.RLock()
        
        # 存储配置
        self.storage_path = Path(getattr(self.settings, 'ARTIFACT_STORAGE_PATH', './storage/artifacts'))
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # 文件大小限制（默认100MB）
        self.max_file_size = getattr(self.settings, 'MAX_ARTIFACT_SIZE', 100 * 1024 * 1024)
        
        # 大文件阈值（10MB以上使用分块存储）
        self.large_file_threshold = 10 * 1024 * 1024
        
        # 支持的文件类型
        self.allowed_extensions = getattr(self.settings, 'ALLOWED_ARTIFACT_EXTENSIONS', {
            '.txt', '.md', '.json', '.xml', '.csv', '.xlsx', '.pdf', '.doc', '.docx',
            '.png', '.jpg', '.jpeg', '.gif', '.svg', '.mp4', '.mp3', '.zip', '.tar.gz'
        })
        
        self.logger.info("工件服务初始化完成")
    
    # ==================== 文件上传和存储 ====================
    
    async def upload_artifact(
        self,
        file: UploadFile,
        user_id: int,
        name: Optional[str] = None,
        description: Optional[str] = None,
        artifact_type: str = "file",
        owner_id: Optional[int] = None,
        agent_id: Optional[int] = None,
        session_id: Optional[str] = None,
        message_id: Optional[int] = None,
        compress: bool = True
    ) -> Artifact:
        """
        上传工件
        
        Args:
            file: 上传的文件
            user_id: 用户ID
            name: 工件名称
            description: 工件描述
            artifact_type: 工件类型
            owner_id: 拥有者ID
            agent_id: 智能体ID
            session_id: 会话ID
            message_id: 消息ID
            compress: 是否压缩
            
        Returns:
            工件对象
            
        Raises:
            HTTPException: 上传失败
        """
        try:
            # 验证用户存在
            user = self.db.query(User).filter(User.id == user_id).first()
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="用户不存在"
                )
            
            # 验证文件大小
            if file.size and file.size > self.max_file_size:
                raise HTTPException(
                    status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                    detail=f"文件大小超过限制: {self.max_file_size / 1024 / 1024:.1f}MB"
                )
            
            # 验证文件扩展名
            file_extension = Path(file.filename).suffix.lower()
            if file_extension not in self.allowed_extensions:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"不支持的文件类型: {file_extension}"
                )
            
            # 读取文件内容
            content = await file.read()
            if len(content) > self.max_file_size:
                raise HTTPException(
                    status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                    detail=f"文件大小超过限制: {self.max_file_size / 1024 / 1024:.1f}MB"
                )
            
            # 计算文件哈希
            file_hash = hashlib.sha256(content).hexdigest()
            
            # 检查是否已存在相同文件
            existing_artifact = self.db.query(Artifact).filter(
                and_(
                    Artifact.user_id == user_id,
                    Artifact.file_hash == file_hash
                )
            ).first()
            
            if existing_artifact:
                self.logger.info(f"文件已存在，返回现有工件: {existing_artifact.artifact_id}")
                return existing_artifact
            
            # 生成工件ID
            artifact_id = self._generate_artifact_id()
            
            # 确定内容类型
            content_type = file.content_type or mimetypes.guess_type(file.filename)[0] or "application/octet-stream"
            
            # 存储策略选择
            original_size = len(content)
            storage_info = {}
            compressed_size = 0
            
            if original_size > self.large_file_threshold:
                # 大文件使用分块存储
                chunk_info = await self.chunk_storage_manager.store_file_chunks(content, file_hash)
                storage_info = {
                    "storage_type": "chunked",
                    "chunk_info": chunk_info
                }
                compressed_size = chunk_info["compression_stats"]["total_compressed_size"]
                compress = True
            else:
                # 小文件直接压缩存储
                if compress and original_size > 1024:
                    compressed_content, algorithm, compression_ratio = self.compression_manager.compress(
                        content, content_type=content_type
                    )
                    compressed_size = len(compressed_content)
                    
                    # 如果压缩后文件更大，则不使用压缩
                    if compressed_size >= original_size:
                        compressed_content = content
                        compressed_size = 0
                        compress = False
                        algorithm = None
                else:
                    compressed_content = content
                    compress = False
                    algorithm = None
                
                # 存储文件
                file_storage_info = await self._store_file_content(
                    artifact_id, 
                    compressed_content,
                    file_extension
                )
                storage_info = {
                    "storage_type": "direct",
                    "storage_path": file_storage_info["path"],
                    "compression_algorithm": algorithm if compress else None
                }
            
            # 创建工件记录
            artifact = Artifact(
                user_id=user_id,
                owner_id=owner_id or user_id,
                agent_id=agent_id,
                session_id=session_id,
                message_id=message_id,
                artifact_id=artifact_id,
                name=name or file.filename,
                description=description,
                artifact_type=artifact_type,
                content_type=content_type,
                file_extension=file_extension,
                file_size=original_size,
                compressed_size=compressed_size if compress else None,
                file_hash=file_hash,
                storage_path=storage_info["path"],
                is_compressed=compress,
                metadata={
                    "original_filename": file.filename,
                    "upload_timestamp": datetime.utcnow().isoformat(),
                    "compression_ratio": compressed_size / original_size if compress else 1.0
                }
            )
            
            self.db.add(artifact)
            self.db.commit()
            
            # 创建初始版本记录
            await self._create_artifact_version(
                artifact.id,
                user_id,
                "1.0.0",
                "初始版本",
                storage_info["path"]
            )
            
            self.logger.info(f"上传工件成功: {artifact_id}, 大小: {original_size} bytes")
            return artifact
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"上传工件失败: {file.filename}, 错误: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"上传工件失败: {str(e)}"
            )
    
    async def download_artifact(
        self,
        artifact_id: str,
        user_id: int,
        version: Optional[str] = None
    ) -> Tuple[bytes, str, str]:
        """
        下载工件
        
        Args:
            artifact_id: 工件ID
            user_id: 用户ID
            version: 版本号（可选）
            
        Returns:
            (文件内容, 文件名, 内容类型)
            
        Raises:
            HTTPException: 下载失败
        """
        try:
            # 获取工件信息
            artifact = await self.get_artifact(artifact_id, user_id)
            if not artifact:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="工件不存在"
                )
            
            # 检查访问权限
            if not await self._check_artifact_access(artifact, user_id, "read"):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有访问权限"
                )
            
            # 检查缓存
            cache_key = f"artifact_content:{artifact_id}:{version or 'latest'}"
            cached_content = await self.cache_manager.get(cache_key)
            
            if cached_content:
                self.logger.debug(f"从缓存获取工件内容: {artifact_id}")
                return cached_content["data"], artifact.name, artifact.content_type
            
            # 确定存储路径
            storage_path = artifact.storage_path
            if version:
                # 获取指定版本的存储路径
                version_record = self.db.query(ArtifactVersion).filter(
                    and_(
                        ArtifactVersion.artifact_id == artifact.id,
                        ArtifactVersion.version == version
                    )
                ).first()
                
                if not version_record:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"版本不存在: {version}"
                    )
                
                storage_path = version_record.storage_path
            
            # 根据存储类型读取文件
            storage_info = artifact.metadata.get("storage_info", {})
            storage_type = storage_info.get("storage_type", "direct")
            
            if storage_type == "chunked":
                # 分块存储的文件
                chunk_info = storage_info["chunk_info"]
                content = await self.chunk_storage_manager.retrieve_file_chunks(
                    chunk_info["chunk_ids"], 
                    chunk_info["file_hash"]
                )
            else:
                # 直接存储的文件
                compressed_data = await self._read_file_content(storage_path)
                
                # 解压缩
                compression_algorithm = storage_info.get("compression_algorithm")
                if compression_algorithm:
                    content = self.compression_manager.decompress(
                        compressed_data, compression_algorithm
                    )
                elif artifact.is_compressed:
                    # 兼容旧版本的gzip压缩
                    content = gzip.decompress(compressed_data)
                else:
                    content = compressed_data
            
            # 缓存文件内容（如果文件不太大）
            if len(content) <= 10 * 1024 * 1024:  # 10MB以下才缓存
                await self.cache_manager.set(
                    cache_key,
                    {
                        "data": content,
                        "name": artifact.name,
                        "content_type": artifact.content_type
                    },
                    ttl=600,  # 10分钟缓存
                    tags=["artifact_content", f"user_{user_id}"],
                    levels=[CacheLevel.MEMORY, CacheLevel.DISK]
                )
            
            # 更新下载统计
            artifact.download_count = (artifact.download_count or 0) + 1
            artifact.last_accessed_at = datetime.utcnow()
            self.db.commit()
            
            self.logger.info(f"下载工件成功: {artifact_id}")
            return content, artifact.name, artifact.content_type
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"下载工件失败: {artifact_id}, 错误: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"下载工件失败: {str(e)}"
            )
    
    # ==================== 工件管理 ====================
    
    async def get_artifact(
        self,
        artifact_id: str,
        user_id: int,
        check_permission: bool = True
    ) -> Optional[Artifact]:
        """
        获取工件信息
        
        Args:
            artifact_id: 工件ID
            user_id: 用户ID
            check_permission: 是否检查权限
            
        Returns:
            工件对象
        """
        try:
            # 检查缓存
            cache_key = f"artifact:{artifact_id}"
            cached_artifact = await self.cache_manager.get(cache_key)
            
            if cached_artifact:
                # 从缓存数据重建Artifact对象
                artifact = Artifact(**cached_artifact)
                if check_permission and not await self._check_artifact_access(artifact, user_id, "read"):
                    return None
                return artifact
            
            # 从数据库查询
            query = self.db.query(Artifact).filter(Artifact.artifact_id == artifact_id)
            
            if check_permission:
                # 添加权限过滤
                query = query.filter(
                    or_(
                        Artifact.user_id == user_id,
                        Artifact.owner_id == user_id,
                        Artifact.id.in_(
                            self.db.query(ArtifactShare.artifact_id).filter(
                                and_(
                                    ArtifactShare.shared_with_user_id == user_id,
                                    ArtifactShare.permission.in_(["read", "write", "admin"])
                                )
                            )
                        )
                    )
                )
            
            artifact = query.first()
            
            if artifact:
                # 缓存结果
                artifact_dict = {
                    "id": artifact.id,
                    "artifact_id": artifact.artifact_id,
                    "user_id": artifact.user_id,
                    "owner_id": artifact.owner_id,
                    "agent_id": artifact.agent_id,
                    "session_id": artifact.session_id,
                    "message_id": artifact.message_id,
                    "name": artifact.name,
                    "description": artifact.description,
                    "artifact_type": artifact.artifact_type,
                    "content_type": artifact.content_type,
                    "file_extension": artifact.file_extension,
                    "file_size": artifact.file_size,
                    "file_hash": artifact.file_hash,
                    "compressed_size": artifact.compressed_size,
                    "storage_path": artifact.storage_path,
                    "is_compressed": artifact.is_compressed,
                    "metadata": artifact.metadata,
                    "download_count": artifact.download_count,
                    "created_at": artifact.created_at.isoformat() if artifact.created_at else None,
                    "updated_at": artifact.updated_at.isoformat() if artifact.updated_at else None,
                    "last_accessed_at": artifact.last_accessed_at.isoformat() if artifact.last_accessed_at else None
                }
                
                await self.cache_manager.set(
                    cache_key,
                    artifact_dict,
                    ttl=600,
                    tags=["artifact", f"user_{user_id}"],
                    levels=[CacheLevel.MEMORY]
                )
                
                # 额外权限检查
                if check_permission and not await self._check_artifact_access(artifact, user_id, "read"):
                    return None
            
            self.logger.info(f"获取工件成功: {artifact_id}")
            return artifact
            
        except Exception as e:
            self.logger.error(f"获取工件失败: {artifact_id}, 错误: {str(e)}")
            return None
    
    async def list_artifacts(
        self,
        user_id: int,
        artifact_type: Optional[str] = None,
        owner_id: Optional[int] = None,
        agent_id: Optional[int] = None,
        session_id: Optional[str] = None,
        search_query: Optional[str] = None,
        skip: int = 0,
        limit: int = 50
    ) -> List[Artifact]:
        """
        列出工件
        
        Args:
            user_id: 用户ID
            artifact_type: 工件类型过滤
            owner_id: 拥有者ID过滤
            agent_id: 智能体ID过滤
            session_id: 会话ID过滤
            search_query: 搜索查询
            skip: 跳过数量
            limit: 限制数量
            
        Returns:
            工件列表
        """
        try:
            # 构建基础查询
            query = self.db.query(Artifact)
            
            # 权限过滤：只能看到自己的工件或共享给自己的工件
            query = query.filter(
                or_(
                    Artifact.user_id == user_id,
                    Artifact.owner_id == user_id,
                    Artifact.id.in_(
                        self.db.query(ArtifactShare.artifact_id).filter(
                            and_(
                                ArtifactShare.shared_with_user_id == user_id,
                                ArtifactShare.permission.in_(["read", "write", "admin"])
                            )
                        )
                    )
                )
            )
            
            # 类型过滤
            if artifact_type:
                query = query.filter(Artifact.artifact_type == artifact_type)
            
            # 拥有者过滤
            if owner_id:
                query = query.filter(Artifact.owner_id == owner_id)
            
            # 智能体过滤
            if agent_id:
                query = query.filter(Artifact.agent_id == agent_id)
            
            # 会话过滤
            if session_id:
                query = query.filter(Artifact.session_id == session_id)
            
            # 搜索过滤
            if search_query:
                search_pattern = f"%{search_query}%"
                query = query.filter(
                    or_(
                        Artifact.name.ilike(search_pattern),
                        Artifact.description.ilike(search_pattern)
                    )
                )
            
            # 排序和分页
            artifacts = query.order_by(desc(Artifact.created_at)).offset(skip).limit(limit).all()
            
            self.logger.info(f"列出工件成功，数量: {len(artifacts)}")
            return artifacts
            
        except Exception as e:
            self.logger.error(f"列出工件失败，错误: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"列出工件失败: {str(e)}"
            )
    
    async def update_artifact(
        self,
        artifact_id: str,
        user_id: int,
        name: Optional[str] = None,
        description: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Artifact:
        """
        更新工件信息
        
        Args:
            artifact_id: 工件ID
            user_id: 用户ID
            name: 新名称
            description: 新描述
            metadata: 新元数据
            
        Returns:
            更新后的工件对象
            
        Raises:
            HTTPException: 更新失败
        """
        try:
            # 获取工件
            artifact = await self.get_artifact(artifact_id, user_id)
            if not artifact:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="工件不存在"
                )
            
            # 检查写入权限
            if not await self._check_artifact_access(artifact, user_id, "write"):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有修改权限"
                )
            
            # 更新字段
            if name is not None:
                artifact.name = name
            
            if description is not None:
                artifact.description = description
            
            if metadata is not None:
                # 合并元数据
                current_metadata = artifact.metadata or {}
                current_metadata.update(metadata)
                artifact.metadata = current_metadata
            
            artifact.updated_at = datetime.utcnow()
            self.db.commit()
            
            # 清除缓存
            cache_key = f"artifact:{artifact_id}"
            with self._cache_lock:
                self._artifact_cache.pop(cache_key, None)
            
            self.logger.info(f"更新工件成功: {artifact_id}")
            return artifact
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"更新工件失败: {artifact_id}, 错误: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"更新工件失败: {str(e)}"
            )
    
    async def delete_artifact(
        self,
        artifact_id: str,
        user_id: int,
        force: bool = False
    ) -> bool:
        """
        删除工件
        
        Args:
            artifact_id: 工件ID
            user_id: 用户ID
            force: 是否强制删除
            
        Returns:
            是否删除成功
            
        Raises:
            HTTPException: 删除失败
        """
        try:
            # 获取工件
            artifact = await self.get_artifact(artifact_id, user_id)
            if not artifact:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="工件不存在"
                )
            
            # 检查删除权限（只有拥有者或管理员可以删除）
            if not force and not await self._check_artifact_access(artifact, user_id, "admin"):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有删除权限"
                )
            
            # 根据存储类型删除文件
            storage_info = artifact.metadata.get("storage_info", {}) if artifact.metadata else {}
            storage_type = storage_info.get("storage_type", "direct")
            
            if storage_type == "chunked":
                # 删除分块文件
                chunk_info = storage_info["chunk_info"]
                await self.chunk_storage_manager.delete_file_chunks(chunk_info["chunk_ids"])
            else:
                # 删除直接存储的文件
                storage_path = storage_info.get("storage_path") or artifact.storage_path
                if storage_path:
                    try:
                        await self._delete_file_content(storage_path)
                    except Exception as e:
                        self.logger.warning(f"删除文件失败: {storage_path}, 错误: {str(e)}")
            
            # 删除版本记录
            versions = self.db.query(ArtifactVersion).filter(
                ArtifactVersion.artifact_id == artifact.id
            ).all()
            
            for version in versions:
                if version.storage_path:
                    try:
                        await self._delete_file_content(version.storage_path)
                    except Exception as e:
                        self.logger.warning(f"删除版本文件失败: {version.storage_path}, 错误: {str(e)}")
            
            self.db.query(ArtifactVersion).filter(
                ArtifactVersion.artifact_id == artifact.id
            ).delete()
            
            # 删除共享记录
            self.db.query(ArtifactShare).filter(
                ArtifactShare.artifact_id == artifact.id
            ).delete()
            
            # 删除工件记录
            self.db.delete(artifact)
            self.db.commit()
            
            # 清除相关缓存
            cache_keys = [
                f"artifact:{artifact_id}",
                f"artifact_content:{artifact_id}:latest"
            ]
            
            for cache_key in cache_keys:
                await self.cache_manager.delete(cache_key)
            
            # 清除用户相关缓存标签
            await self.cache_manager.clear_by_tags([f"user_{user_id}"])
            
            self.logger.info(f"删除工件成功: {artifact_id}")
            return True
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"删除工件失败: {artifact_id}, 错误: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"删除工件失败: {str(e)}"
            )
    
    # ==================== 版本管理 ====================
    
    async def create_artifact_version(
        self,
        artifact_id: str,
        user_id: int,
        version: str,
        description: Optional[str] = None,
        file: Optional[UploadFile] = None
    ) -> ArtifactVersion:
        """
        创建工件版本
        
        Args:
            artifact_id: 工件ID
            user_id: 用户ID
            version: 版本号
            description: 版本描述
            file: 新文件（可选）
            
        Returns:
            版本对象
            
        Raises:
            HTTPException: 创建失败
        """
        try:
            # 获取工件
            artifact = await self.get_artifact(artifact_id, user_id)
            if not artifact:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="工件不存在"
                )
            
            # 检查写入权限
            if not await self._check_artifact_access(artifact, user_id, "write"):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有修改权限"
                )
            
            # 检查版本是否已存在
            existing_version = self.db.query(ArtifactVersion).filter(
                and_(
                    ArtifactVersion.artifact_id == artifact.id,
                    ArtifactVersion.version == version
                )
            ).first()
            
            if existing_version:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"版本已存在: {version}"
                )
            
            # 确定存储路径
            storage_path = artifact.storage_path
            
            # 如果提供了新文件，则上传并存储
            if file:
                # 验证文件
                content = await file.read()
                if len(content) > self.max_file_size:
                    raise HTTPException(
                        status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                        detail=f"文件大小超过限制: {self.max_file_size / 1024 / 1024:.1f}MB"
                    )
                
                # 压缩文件（如果需要）
                if artifact.is_compressed and len(content) > 1024:
                    compressed_content = gzip.compress(content)
                    if len(compressed_content) < len(content):
                        content = compressed_content
                
                # 存储新版本文件
                version_storage_info = await self._store_file_content(
                    f"{artifact.artifact_id}_v{version}",
                    content,
                    artifact.file_extension
                )
                storage_path = version_storage_info["path"]
            
            # 创建版本记录
            version_record = await self._create_artifact_version(
                artifact.id,
                user_id,
                version,
                description,
                storage_path
            )
            
            self.logger.info(f"创建工件版本成功: {artifact_id} v{version}")
            return version_record
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"创建工件版本失败: {artifact_id} v{version}, 错误: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"创建工件版本失败: {str(e)}"
            )
    
    async def list_artifact_versions(
        self,
        artifact_id: str,
        user_id: int
    ) -> List[ArtifactVersion]:
        """
        列出工件版本
        
        Args:
            artifact_id: 工件ID
            user_id: 用户ID
            
        Returns:
            版本列表
        """
        try:
            # 获取工件
            artifact = await self.get_artifact(artifact_id, user_id)
            if not artifact:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="工件不存在"
                )
            
            # 获取版本列表
            versions = self.db.query(ArtifactVersion).filter(
                ArtifactVersion.artifact_id == artifact.id
            ).order_by(desc(ArtifactVersion.created_at)).all()
            
            self.logger.info(f"列出工件版本成功: {artifact_id}, 数量: {len(versions)}")
            return versions
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"列出工件版本失败: {artifact_id}, 错误: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"列出工件版本失败: {str(e)}"
            )
    
    # ==================== 权限管理 ====================
    
    async def share_artifact(
        self,
        artifact_id: str,
        owner_user_id: int,
        shared_with_user_id: int,
        permission: str = "read"
    ) -> ArtifactShare:
        """
        共享工件
        
        Args:
            artifact_id: 工件ID
            owner_user_id: 拥有者用户ID
            shared_with_user_id: 被共享用户ID
            permission: 权限级别（read/write/admin）
            
        Returns:
            共享记录
            
        Raises:
            HTTPException: 共享失败
        """
        try:
            # 获取工件
            artifact = await self.get_artifact(artifact_id, owner_user_id)
            if not artifact:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="工件不存在"
                )
            
            # 检查管理权限
            if not await self._check_artifact_access(artifact, owner_user_id, "admin"):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有管理权限"
                )
            
            # 验证被共享用户存在
            shared_user = self.db.query(User).filter(User.id == shared_with_user_id).first()
            if not shared_user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="被共享用户不存在"
                )
            
            # 验证权限级别
            if permission not in ["read", "write", "admin"]:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的权限级别"
                )
            
            # 检查是否已存在共享记录
            existing_share = self.db.query(ArtifactShare).filter(
                and_(
                    ArtifactShare.artifact_id == artifact.id,
                    ArtifactShare.shared_with_user_id == shared_with_user_id
                )
            ).first()
            
            if existing_share:
                # 更新权限
                existing_share.permission = permission
                existing_share.updated_at = datetime.utcnow()
                share = existing_share
            else:
                # 创建新共享记录
                share = ArtifactShare(
                    artifact_id=artifact.id,
                    shared_by_user_id=owner_user_id,
                    shared_with_user_id=shared_with_user_id,
                    permission=permission
                )
                self.db.add(share)
            
            self.db.commit()
            
            self.logger.info(f"共享工件成功: {artifact_id} -> 用户{shared_with_user_id}")
            return share
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"共享工件失败: {artifact_id}, 错误: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"共享工件失败: {str(e)}"
            )
    
    # ==================== 统计和监控 ====================
    
    async def get_user_artifact_stats(
        self,
        user_id: int
    ) -> Dict[str, Any]:
        """
        获取用户工件统计
        
        Args:
            user_id: 用户ID
            
        Returns:
            统计信息
        """
        try:
            # 拥有的工件数量
            owned_count = self.db.query(func.count(Artifact.id)).filter(
                Artifact.owner_id == user_id
            ).scalar()
            
            # 上传的工件数量
            uploaded_count = self.db.query(func.count(Artifact.id)).filter(
                Artifact.user_id == user_id
            ).scalar()
            
            # 共享给自己的工件数量
            shared_count = self.db.query(func.count(ArtifactShare.id)).filter(
                ArtifactShare.shared_with_user_id == user_id
            ).scalar()
            
            # 总存储大小
            total_size = self.db.query(func.sum(Artifact.file_size)).filter(
                Artifact.user_id == user_id
            ).scalar() or 0
            
            # 按类型统计
            type_stats = self.db.query(
                Artifact.artifact_type,
                func.count(Artifact.id).label('count'),
                func.sum(Artifact.file_size).label('total_size')
            ).filter(
                Artifact.user_id == user_id
            ).group_by(Artifact.artifact_type).all()
            
            stats = {
                "owned_artifacts": owned_count,
                "uploaded_artifacts": uploaded_count,
                "shared_artifacts": shared_count,
                "total_storage_size": total_size,
                "storage_size_mb": round(total_size / 1024 / 1024, 2),
                "type_statistics": [
                    {
                        "type": stat.artifact_type,
                        "count": stat.count,
                        "size": stat.total_size or 0,
                        "size_mb": round((stat.total_size or 0) / 1024 / 1024, 2)
                    }
                    for stat in type_stats
                ]
            }
            
            self.logger.info(f"获取用户工件统计成功: {user_id}")
            return stats
            
        except Exception as e:
            self.logger.error(f"获取用户工件统计失败: {user_id}, 错误: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取统计信息失败: {str(e)}"
            )
    
    # ==================== 辅助方法 ====================
    
    def _generate_artifact_id(self) -> str:
        """
        生成工件ID
        
        Returns:
            工件ID
        """
        import uuid
        return str(uuid.uuid4())
    
    async def _store_file_content(
        self,
        artifact_id: str,
        content: bytes,
        file_extension: str
    ) -> Dict[str, str]:
        """
        存储文件内容
        
        Args:
            artifact_id: 工件ID
            content: 文件内容
            file_extension: 文件扩展名
            
        Returns:
            存储信息
        """
        # 创建存储目录结构（按日期分组）
        date_path = datetime.utcnow().strftime("%Y/%m/%d")
        storage_dir = self.storage_path / date_path
        storage_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成文件路径
        filename = f"{artifact_id}{file_extension}"
        file_path = storage_dir / filename
        
        # 写入文件
        with open(file_path, 'wb') as f:
            f.write(content)
        
        return {
            "path": str(file_path),
            "relative_path": f"{date_path}/{filename}"
        }
    
    async def _read_file_content(self, storage_path: str) -> bytes:
        """
        读取文件内容
        
        Args:
            storage_path: 存储路径
            
        Returns:
            文件内容
        """
        with open(storage_path, 'rb') as f:
            return f.read()
    
    async def _delete_file_content(self, storage_path: str):
        """
        删除文件内容
        
        Args:
            storage_path: 存储路径
        """
        if os.path.exists(storage_path):
            os.remove(storage_path)
    
    async def _check_artifact_access(
        self,
        artifact: Artifact,
        user_id: int,
        required_permission: str
    ) -> bool:
        """
        检查工件访问权限
        
        Args:
            artifact: 工件对象
            user_id: 用户ID
            required_permission: 所需权限
            
        Returns:
            是否有权限
        """
        # 拥有者和上传者有所有权限
        if artifact.owner_id == user_id or artifact.user_id == user_id:
            return True
        
        # 检查共享权限
        share = self.db.query(ArtifactShare).filter(
            and_(
                ArtifactShare.artifact_id == artifact.id,
                ArtifactShare.shared_with_user_id == user_id
            )
        ).first()
        
        if not share:
            return False
        
        # 权限级别检查
        permission_levels = {"read": 1, "write": 2, "admin": 3}
        user_level = permission_levels.get(share.permission, 0)
        required_level = permission_levels.get(required_permission, 0)
        
        return user_level >= required_level
    
    async def _create_artifact_version(
        self,
        artifact_id: int,
        user_id: int,
        version: str,
        description: Optional[str],
        storage_path: str
    ) -> ArtifactVersion:
        """
        创建工件版本记录
        
        Args:
            artifact_id: 工件数据库ID
            user_id: 用户ID
            version: 版本号
            description: 版本描述
            storage_path: 存储路径
            
        Returns:
            版本对象
        """
        version_record = ArtifactVersion(
            artifact_id=artifact_id,
            version=version,
            description=description,
            storage_path=storage_path,
            created_by=user_id
        )
        
        self.db.add(version_record)
        self.db.commit()
        
        return version_record
    
    def clear_cache(self, pattern: Optional[str] = None):
        """
        清除缓存
        
        Args:
            pattern: 缓存键模式（可选）
        """
        with self._cache_lock:
            if pattern:
                # 清除匹配模式的缓存
                keys_to_remove = [key for key in self._artifact_cache.keys() if pattern in key]
                for key in keys_to_remove:
                    self._artifact_cache.pop(key, None)
                self.logger.info(f"清除匹配模式的缓存: {pattern}, 数量: {len(keys_to_remove)}")
            else:
                # 清除所有缓存
                cache_size = len(self._artifact_cache)
                self._artifact_cache.clear()
                self.logger.info(f"清除所有缓存，数量: {cache_size}")


# 全局工件服务实例
_artifact_service = None


def get_artifact_service(db = None):
    """
    获取工件服务实例
    
    Args:
        db: 数据库会话
        
    Returns:
        工件服务实例
    """
    global _artifact_service
    if _artifact_service is None:
        _artifact_service = ArtifactService(db)
    return _artifact_service