# -*- coding: utf-8 -*-
"""
A2A多智能体系统日志模块

使用loguru配置结构化日志和数据库日志存储
"""

import sys
import json
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path
from loguru import logger
from sqlalchemy import text

from app.core.config import get_settings
from app.core.database import get_database_manager


class DatabaseLogHandler:
    """
    数据库日志处理器
    
    将日志写入数据库的system_logs表
    """
    
    def __init__(self):
        self._queue = asyncio.Queue(maxsize=1000)
        self._task: Optional[asyncio.Task] = None
        self._running = False
    
    async def start(self) -> None:
        """
        启动数据库日志处理器
        """
        if self._running:
            return
        
        self._running = True
        self._task = asyncio.create_task(self._process_logs())
        logger.info("数据库日志处理器已启动")
    
    async def stop(self) -> None:
        """
        停止数据库日志处理器
        """
        if not self._running:
            return
        
        self._running = False
        
        # 等待队列中的日志处理完成
        while not self._queue.empty():
            await asyncio.sleep(0.1)
        
        if self._task:
            self._task.cancel()
            try:
                await self._task
            except asyncio.CancelledError:
                pass
        
        logger.info("数据库日志处理器已停止")
    
    def add_log(self, record: Dict[str, Any]) -> None:
        """
        添加日志记录到队列
        
        Args:
            record: 日志记录字典
        """
        if not self._running:
            return
        
        try:
            self._queue.put_nowait(record)
        except asyncio.QueueFull:
            # 队列满时丢弃最旧的日志
            try:
                self._queue.get_nowait()
                self._queue.put_nowait(record)
            except asyncio.QueueEmpty:
                pass
    
    async def _process_logs(self) -> None:
        """
        处理日志队列
        """
        batch_size = 10
        batch_timeout = 5.0
        
        while self._running:
            try:
                records = []
                start_time = asyncio.get_event_loop().time()
                
                # 收集批量日志
                while len(records) < batch_size and (asyncio.get_event_loop().time() - start_time) < batch_timeout:
                    try:
                        record = await asyncio.wait_for(self._queue.get(), timeout=1.0)
                        records.append(record)
                    except asyncio.TimeoutError:
                        break
                
                if records:
                    await self._batch_insert_logs(records)
                    
            except Exception as e:
                print(f"[ERROR] 日志处理器异常: {e}", file=sys.stderr)
                await asyncio.sleep(1.0)
    
    async def _batch_insert_logs(self, records: list) -> None:
        """
        批量插入日志记录
        
        Args:
            records: 日志记录列表
        """
        try:
            db_manager = get_database_manager()
            engine = db_manager.get_engine()
            
            # 使用参数化查询避免SQL注入和语法错误
            sql = """
                INSERT INTO system_logs (
                    level, logger_name, message, module, function, line_number,
                    extra_data, user_id, session_id, request_id, timestamp,
                    is_active, created_at, updated_at
                ) VALUES (
                    :level, :logger_name, :message, :module, :function, :line_number,
                    :extra_data, :user_id, :session_id, :request_id, :timestamp,
                    :is_active, :created_at, :updated_at
                )
            """
            
            # 准备批量插入数据
            batch_data = []
            for record in records:
                # 处理用户ID，如果是None或'NULL'字符串，设为None
                user_id = record.get('user_id')
                if user_id == 'NULL' or user_id == 'None':
                    user_id = None
                elif user_id is not None:
                    try:
                        user_id = int(user_id)
                    except (ValueError, TypeError):
                        user_id = None
                
                # 处理行号
                line_number = record.get('line_number')
                if line_number == 'NULL' or line_number == 'None':
                    line_number = None
                elif line_number is not None:
                    try:
                        line_number = int(line_number)
                    except (ValueError, TypeError):
                        line_number = None
                
                current_time = datetime.now()
                batch_data.append({
                    'level': record.get('level', 'INFO'),
                    'logger_name': record.get('logger_name', ''),
                    'message': str(record.get('message', ''))[:1000],  # 限制消息长度
                    'module': record.get('module', ''),
                    'function': record.get('function', ''),
                    'line_number': line_number,
                    'extra_data': json.dumps(record.get('extra_data', {}), ensure_ascii=False)[:2000],
                    'user_id': user_id,
                    'session_id': record.get('session_id', ''),
                    'request_id': record.get('request_id', ''),
                    'timestamp': record.get('timestamp', datetime.now().isoformat()),
                    'is_active': True,
                    'created_at': current_time,
                    'updated_at': current_time
                })
            
            async with engine.begin() as conn:
                # 逐条插入以避免批量插入的复杂性
                for data in batch_data:
                    await conn.execute(text(sql), data)
            
        except Exception as e:
            # 避免递归：直接打印到stderr而不是使用logger
            print(f"[ERROR] 批量插入日志失败: {e}", file=sys.stderr)
            # 可选：写入文件日志作为备份
            try:
                with open("logs/db_error.log", "a", encoding="utf-8") as f:
                    f.write(f"{datetime.now().isoformat()} - 批量插入日志失败: {e}\n")
            except:
                pass  # 如果文件写入也失败，就忽略


class StructuredFormatter:
    """
    结构化日志格式化器
    """
    
    @staticmethod
    def format_record(record: dict) -> str:
        """
        格式化日志记录
        
        Args:
            record: 日志记录
            
        Returns:
            格式化后的日志字符串
        """
        timestamp = record.get('time', datetime.now().isoformat())
        level = record.get('level', {}).get('name', 'INFO')
        message = record.get('message', '')
        module = record.get('name', '')
        function = record.get('function', '')
        line = record.get('line', '')
        
        # 构建结构化日志
        log_data = {
            'timestamp': timestamp,
            'level': level,
            'module': module,
            'function': function,
            'line': line,
            'message': message
        }
        
        # 添加额外数据
        extra = record.get('extra', {})
        if extra:
            log_data['extra'] = extra
        
        return json.dumps(log_data, ensure_ascii=False, separators=(',', ':'))


# 全局数据库日志处理器实例
_db_handler: Optional[DatabaseLogHandler] = None


def get_database_log_handler() -> DatabaseLogHandler:
    """
    获取数据库日志处理器实例
    
    Returns:
        数据库日志处理器实例
    """
    global _db_handler
    if _db_handler is None:
        _db_handler = DatabaseLogHandler()
    return _db_handler


def database_sink(message):
    """
    数据库日志接收器
    
    Args:
        message: loguru日志消息对象
    """
    try:
        handler = get_database_log_handler()
        
        # 提取日志记录信息
        record = message.record
        
        log_data = {
            'level': record['level'].name,
            'logger_name': record['name'],
            'message': record['message'],
            'module': record['module'],
            'function': record['function'],
            'line_number': record['line'],
            'extra_data': record.get('extra', {}),
            'user_id': record.get('extra', {}).get('user_id'),
            'session_id': record.get('extra', {}).get('session_id'),
            'request_id': record.get('extra', {}).get('request_id'),
            'timestamp': record['time'].isoformat()
        }
        
        handler.add_log(log_data)
        
    except Exception as e:
        # 避免递归：直接打印到stderr
        print(f"[ERROR] 数据库日志接收器异常: {e}", file=sys.stderr)


def setup_logging():
    """
    设置日志配置
    """
    settings = get_settings()
    
    # 移除默认处理器
    logger.remove()
    
    # 控制台日志
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
               "<level>{message}</level>",
        level=settings.log_level,
        colorize=True
    )
    
    # 文件日志
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    logger.add(
        log_dir / "app.log",
        format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}",
        level=settings.log_level,
        rotation="10 MB",
        retention="30 days",
        compression="zip",
        encoding="utf-8"
    )
    
    # 错误日志单独文件
    logger.add(
        log_dir / "error.log",
        format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}",
        level="ERROR",
        rotation="10 MB",
        retention="30 days",
        compression="zip",
        encoding="utf-8"
    )
    
    # 数据库日志（如果启用）
    if hasattr(settings, 'enable_database_logging') and settings.enable_database_logging:
        logger.add(
            database_sink,
            level=settings.log_level,
            format="{message}"
        )
    
    logger.info("日志系统初始化完成")


async def start_database_logging():
    """
    启动数据库日志处理
    """
    settings = get_settings()
    if hasattr(settings, 'enable_database_logging') and settings.enable_database_logging:
        handler = get_database_log_handler()
        await handler.start()
        logger.info("数据库日志处理已启动")


async def stop_database_logging():
    """
    停止数据库日志处理
    """
    global _db_handler
    if _db_handler:
        await _db_handler.stop()
        _db_handler = None
        logger.info("数据库日志处理已停止")


def get_logger(name: str = None):
    """
    获取日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        日志记录器实例
    """
    if name:
        return logger.bind(name=name)
    return logger


# 导出常用函数
__all__ = [
    'setup_logging',
    'start_database_logging', 
    'stop_database_logging',
    'get_logger',
    'DatabaseLogHandler',
    'StructuredFormatter'
]