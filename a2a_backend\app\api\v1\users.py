# -*- coding: utf-8 -*-
"""
A2A多智能体系统用户管理API接口

提供用户信息查询、更新、权限管理和操作日志功能
"""

from datetime import datetime
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr, Field, validator
from loguru import logger

from app.services.user_service import UserService
from app.services.auth_service import AuthService
from app.core.config import get_settings
from app.core.rate_limiter import RateLimiter
from app.core.security import get_client_ip


# 创建路由器
router = APIRouter(prefix="/users", tags=["用户管理"])

# 安全依赖
security = HTTPBearer()

# 服务实例
user_service = UserService()
auth_service = AuthService()
settings = get_settings()
rate_limiter = RateLimiter()


# 请求模型
class UserUpdateRequest(BaseModel):
    """用户信息更新请求模型"""
    full_name: Optional[str] = Field(None, max_length=100, description="全名")
    phone: Optional[str] = Field(None, max_length=20, description="手机号")
    avatar_url: Optional[str] = Field(None, max_length=500, description="头像URL")
    timezone: Optional[str] = Field(None, max_length=50, description="时区")
    language: Optional[str] = Field(None, max_length=10, description="语言")
    preferences: Optional[Dict[str, Any]] = Field(None, description="用户偏好设置")


class UserPermissionRequest(BaseModel):
    """用户权限请求模型"""
    resource_type: str = Field(..., description="资源类型")
    resource_id: Optional[str] = Field(None, description="资源ID")
    permission: str = Field(..., description="权限名称")
    
    @validator('resource_type')
    def validate_resource_type(cls, v):
        """验证资源类型"""
        allowed_types = ['user', 'agent', 'session', 'task', 'message', 'system']
        if v not in allowed_types:
            raise ValueError(f'资源类型必须是: {", ".join(allowed_types)}')
        return v
    
    @validator('permission')
    def validate_permission(cls, v):
        """验证权限名称"""
        allowed_permissions = ['read', 'write', 'delete', 'manage', 'execute', 'admin']
        if v not in allowed_permissions:
            raise ValueError(f'权限必须是: {", ".join(allowed_permissions)}')
        return v


class UserRoleUpdateRequest(BaseModel):
    """用户角色更新请求模型"""
    role: str = Field(..., description="用户角色")
    
    @validator('role')
    def validate_role(cls, v):
        """验证用户角色"""
        allowed_roles = ['user', 'premium', 'admin', 'super_admin']
        if v not in allowed_roles:
            raise ValueError(f'角色必须是: {", ".join(allowed_roles)}')
        return v


class UserStatusUpdateRequest(BaseModel):
    """用户状态更新请求模型"""
    is_verified: Optional[bool] = Field(None, description="是否已验证")
    is_locked: Optional[bool] = Field(None, description="是否已锁定")
    locked_until: Optional[datetime] = Field(None, description="锁定到期时间")
    reason: Optional[str] = Field(None, max_length=500, description="操作原因")


# 响应模型
class UserInfoResponse(BaseModel):
    """用户信息响应模型"""
    user_id: int = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    email: str = Field(..., description="邮箱")
    full_name: Optional[str] = Field(None, description="全名")
    phone: Optional[str] = Field(None, description="手机号")
    avatar_url: Optional[str] = Field(None, description="头像URL")
    role: str = Field(..., description="用户角色")
    is_verified: bool = Field(..., description="是否已验证")
    is_locked: bool = Field(..., description="是否已锁定")
    timezone: Optional[str] = Field(None, description="时区")
    language: Optional[str] = Field(None, description="语言")
    preferences: Optional[Dict[str, Any]] = Field(None, description="用户偏好")
    last_login_at: Optional[datetime] = Field(None, description="最后登录时间")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class MessageResponse(BaseModel):
    """消息响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")


# 依赖函数
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """获取当前用户"""
    try:
        token = credentials.credentials
        user_info = await auth_service.verify_token(token)
        return user_info
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的访问令牌",
            headers={"WWW-Authenticate": "Bearer"}
        )


async def require_admin_permission(current_user: Dict[str, Any] = Depends(get_current_user)):
    """要求管理员权限"""
    if current_user["role"] not in ["admin", "super_admin"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user


# API接口
@router.get("/me", response_model=UserInfoResponse, summary="获取当前用户信息")
async def get_current_user_info(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取当前用户的详细信息
    
    需要提供有效的访问令牌
    """
    try:
        # 获取用户详细信息
        user_info = await user_service.get_user_by_id(current_user["user_id"])
        
        if user_info:
            return UserInfoResponse(**user_info)
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
            
    except Exception as e:
        logger.error(f"获取用户信息异常: {str(e)}", extra={
            "user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户信息失败"
        )


@router.put("/me", response_model=MessageResponse, summary="更新当前用户信息")
async def update_current_user_info(
    request: UserUpdateRequest,
    http_request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    更新当前用户的信息
    
    - **full_name**: 全名（可选）
    - **phone**: 手机号（可选）
    - **avatar_url**: 头像URL（可选）
    - **timezone**: 时区（可选）
    - **language**: 语言（可选）
    - **preferences**: 用户偏好设置（可选）
    
    需要提供有效的访问令牌
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 速率限制检查
        rate_limiter.check_rate_limit(
            client_id=f"user_update:{current_user['user_id']}",
            limit=10  # 每分钟最多10次更新
        )
        
        # 准备更新数据
        update_data = {}
        if request.full_name is not None:
            update_data["full_name"] = request.full_name
        if request.phone is not None:
            update_data["phone"] = request.phone
        if request.avatar_url is not None:
            update_data["avatar_url"] = request.avatar_url
        if request.timezone is not None:
            update_data["timezone"] = request.timezone
        if request.language is not None:
            update_data["language"] = request.language
        if request.preferences is not None:
            update_data["preferences"] = request.preferences
        
        # 更新用户信息
        result = await user_service.update_user(
            current_user["user_id"],
            update_data,
            client_ip
        )
        
        if result["success"]:
            logger.info(f"用户信息更新成功", extra={
                "user_id": current_user["user_id"],
                "username": current_user["username"],
                "client_ip": client_ip
            })
            
            return MessageResponse(
                success=True,
                message="用户信息更新成功",
                data={
                    "user_id": current_user["user_id"],
                    "updated_at": datetime.utcnow().isoformat()
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except ValueError as e:
        logger.warning(f"用户信息更新失败: {str(e)}", extra={
            "user_id": current_user["user_id"],
            "client_ip": get_client_ip(http_request)
        })
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"用户信息更新异常: {str(e)}", extra={
            "user_id": current_user["user_id"],
            "client_ip": get_client_ip(http_request)
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="用户信息更新失败，请稍后重试"
        )


@router.get("/{user_id}", response_model=UserInfoResponse, summary="获取指定用户信息")
async def get_user_info(
    user_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取指定用户的信息
    
    - **user_id**: 用户ID
    
    需要管理员权限或查看自己的信息
    """
    try:
        # 权限检查：只能查看自己的信息或需要管理员权限
        if (current_user["user_id"] != user_id and 
            current_user["role"] not in ["admin", "super_admin"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有查看该用户信息的权限"
            )
        
        # 获取用户信息
        user_info = await user_service.get_user_by_id(user_id)
        
        if user_info:
            return UserInfoResponse(**user_info)
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户信息异常: {str(e)}", extra={
            "target_user_id": user_id,
            "current_user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户信息失败"
        )


@router.get("/", response_model=Dict[str, Any], summary="获取用户列表")
async def get_users_list(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="页面大小"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    role: Optional[str] = Query(None, description="角色过滤"),
    is_verified: Optional[bool] = Query(None, description="验证状态过滤"),
    is_locked: Optional[bool] = Query(None, description="锁定状态过滤"),
    current_user: Dict[str, Any] = Depends(require_admin_permission)
):
    """
    获取用户列表（管理员功能）
    
    - **page**: 页码（默认1）
    - **page_size**: 页面大小（默认20，最大100）
    - **search**: 搜索关键词（用户名、邮箱、全名）
    - **role**: 角色过滤
    - **is_verified**: 验证状态过滤
    - **is_locked**: 锁定状态过滤
    
    需要管理员权限
    """
    try:
        # 构建过滤条件
        filters = {}
        if search:
            filters["search"] = search
        if role:
            filters["role"] = role
        if is_verified is not None:
            filters["is_verified"] = is_verified
        if is_locked is not None:
            filters["is_locked"] = is_locked
        
        # 获取用户列表
        result = await user_service.list_users(filters, page, page_size)
        
        return {
            "success": True,
            "message": "获取用户列表成功",
            "data": result
        }
        
    except Exception as e:
        logger.error(f"获取用户列表异常: {str(e)}", extra={
            "current_user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户列表失败"
        )


@router.post("/{user_id}/permissions", response_model=MessageResponse, summary="授予用户权限")
async def grant_user_permission(
    user_id: int,
    request: UserPermissionRequest,
    http_request: Request,
    current_user: Dict[str, Any] = Depends(require_admin_permission)
):
    """
    授予用户权限（管理员功能）
    
    - **user_id**: 用户ID
    - **resource_type**: 资源类型
    - **resource_id**: 资源ID（可选）
    - **permission**: 权限名称
    
    需要管理员权限
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 授予权限
        result = await user_service.grant_permission(
            user_id,
            request.resource_type,
            request.resource_id,
            request.permission,
            current_user["user_id"],
            client_ip
        )
        
        if result["success"]:
            logger.info(f"权限授予成功", extra={
                "target_user_id": user_id,
                "resource_type": request.resource_type,
                "resource_id": request.resource_id,
                "permission": request.permission,
                "granted_by": current_user["user_id"],
                "client_ip": client_ip
            })
            
            return MessageResponse(
                success=True,
                message="权限授予成功",
                data={
                    "user_id": user_id,
                    "resource_type": request.resource_type,
                    "resource_id": request.resource_id,
                    "permission": request.permission,
                    "granted_at": datetime.utcnow().isoformat()
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except ValueError as e:
        logger.warning(f"权限授予失败: {str(e)}", extra={
            "target_user_id": user_id,
            "current_user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"权限授予异常: {str(e)}", extra={
            "target_user_id": user_id,
            "current_user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="权限授予失败，请稍后重试"
        )


@router.delete("/{user_id}/permissions", response_model=MessageResponse, summary="撤销用户权限")
async def revoke_user_permission(
    user_id: int,
    resource_type: str = Query(..., description="资源类型"),
    resource_id: Optional[str] = Query(None, description="资源ID"),
    permission: str = Query(..., description="权限名称"),
    http_request: Request = None,
    current_user: Dict[str, Any] = Depends(require_admin_permission)
):
    """
    撤销用户权限（管理员功能）
    
    - **user_id**: 用户ID
    - **resource_type**: 资源类型
    - **resource_id**: 资源ID（可选）
    - **permission**: 权限名称
    
    需要管理员权限
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 撤销权限
        result = await user_service.revoke_permission(
            user_id,
            resource_type,
            resource_id,
            permission,
            current_user["user_id"],
            client_ip
        )
        
        if result["success"]:
            logger.info(f"权限撤销成功", extra={
                "target_user_id": user_id,
                "resource_type": resource_type,
                "resource_id": resource_id,
                "permission": permission,
                "revoked_by": current_user["user_id"],
                "client_ip": client_ip
            })
            
            return MessageResponse(
                success=True,
                message="权限撤销成功",
                data={
                    "user_id": user_id,
                    "resource_type": resource_type,
                    "resource_id": resource_id,
                    "permission": permission,
                    "revoked_at": datetime.utcnow().isoformat()
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except ValueError as e:
        logger.warning(f"权限撤销失败: {str(e)}", extra={
            "target_user_id": user_id,
            "current_user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"权限撤销异常: {str(e)}", extra={
            "target_user_id": user_id,
            "current_user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="权限撤销失败，请稍后重试"
        )


@router.get("/{user_id}/permissions", response_model=Dict[str, Any], summary="获取用户权限列表")
async def get_user_permissions(
    user_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取用户的权限列表
    
    - **user_id**: 用户ID
    
    需要管理员权限或查看自己的权限
    """
    try:
        # 权限检查：只能查看自己的权限或需要管理员权限
        if (current_user["user_id"] != user_id and 
            current_user["role"] not in ["admin", "super_admin"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有查看该用户权限的权限"
            )
        
        # 获取用户权限
        permissions = await user_service.get_user_permissions(user_id)
        
        return {
            "success": True,
            "message": "获取用户权限成功",
            "data": {
                "user_id": user_id,
                "permissions": permissions,
                "total_permissions": len(permissions)
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户权限异常: {str(e)}", extra={
            "target_user_id": user_id,
            "current_user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户权限失败"
        )


@router.put("/{user_id}/role", response_model=MessageResponse, summary="更新用户角色")
async def update_user_role(
    user_id: int,
    request: UserRoleUpdateRequest,
    http_request: Request,
    current_user: Dict[str, Any] = Depends(require_admin_permission)
):
    """
    更新用户角色（管理员功能）
    
    - **user_id**: 用户ID
    - **role**: 新角色
    
    需要管理员权限
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 检查权限：super_admin才能设置admin角色
        if (request.role in ["admin", "super_admin"] and 
            current_user["role"] != "super_admin"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有超级管理员才能设置管理员角色"
            )
        
        # 不能修改自己的角色
        if user_id == current_user["user_id"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能修改自己的角色"
            )
        
        # 更新用户角色
        result = await user_service.update_user(
            user_id,
            {"role": request.role},
            client_ip,
            operator_id=current_user["user_id"]
        )
        
        if result["success"]:
            logger.info(f"用户角色更新成功", extra={
                "target_user_id": user_id,
                "new_role": request.role,
                "updated_by": current_user["user_id"],
                "client_ip": client_ip
            })
            
            return MessageResponse(
                success=True,
                message="用户角色更新成功",
                data={
                    "user_id": user_id,
                    "new_role": request.role,
                    "updated_at": datetime.utcnow().isoformat()
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except HTTPException:
        raise
    except ValueError as e:
        logger.warning(f"用户角色更新失败: {str(e)}", extra={
            "target_user_id": user_id,
            "current_user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"用户角色更新异常: {str(e)}", extra={
            "target_user_id": user_id,
            "current_user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="用户角色更新失败，请稍后重试"
        )


@router.put("/{user_id}/status", response_model=MessageResponse, summary="更新用户状态")
async def update_user_status(
    user_id: int,
    request: UserStatusUpdateRequest,
    http_request: Request,
    current_user: Dict[str, Any] = Depends(require_admin_permission)
):
    """
    更新用户状态（管理员功能）
    
    - **user_id**: 用户ID
    - **is_verified**: 是否已验证（可选）
    - **is_locked**: 是否已锁定（可选）
    - **locked_until**: 锁定到期时间（可选）
    - **reason**: 操作原因（可选）
    
    需要管理员权限
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 不能修改自己的状态
        if user_id == current_user["user_id"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能修改自己的状态"
            )
        
        # 准备更新数据
        update_data = {}
        if request.is_verified is not None:
            update_data["is_verified"] = request.is_verified
        if request.is_locked is not None:
            update_data["is_locked"] = request.is_locked
        if request.locked_until is not None:
            update_data["locked_until"] = request.locked_until
        
        # 更新用户状态
        result = await user_service.update_user(
            user_id,
            update_data,
            client_ip,
            operator_id=current_user["user_id"],
            reason=request.reason
        )
        
        if result["success"]:
            logger.info(f"用户状态更新成功", extra={
                "target_user_id": user_id,
                "update_data": update_data,
                "reason": request.reason,
                "updated_by": current_user["user_id"],
                "client_ip": client_ip
            })
            
            return MessageResponse(
                success=True,
                message="用户状态更新成功",
                data={
                    "user_id": user_id,
                    "updated_fields": list(update_data.keys()),
                    "reason": request.reason,
                    "updated_at": datetime.utcnow().isoformat()
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except HTTPException:
        raise
    except ValueError as e:
        logger.warning(f"用户状态更新失败: {str(e)}", extra={
            "target_user_id": user_id,
            "current_user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"用户状态更新异常: {str(e)}", extra={
            "target_user_id": user_id,
            "current_user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="用户状态更新失败，请稍后重试"
        )


@router.get("/{user_id}/activity-logs", response_model=Dict[str, Any], summary="获取用户操作日志")
async def get_user_activity_logs(
    user_id: int,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="页面大小"),
    action_type: Optional[str] = Query(None, description="操作类型过滤"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取用户操作日志
    
    - **user_id**: 用户ID
    - **page**: 页码（默认1）
    - **page_size**: 页面大小（默认20，最大100）
    - **action_type**: 操作类型过滤
    - **start_date**: 开始日期
    - **end_date**: 结束日期
    
    需要管理员权限或查看自己的日志
    """
    try:
        # 权限检查：只能查看自己的日志或需要管理员权限
        if (current_user["user_id"] != user_id and 
            current_user["role"] not in ["admin", "super_admin"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有查看该用户操作日志的权限"
            )
        
        # 构建过滤条件
        filters = {}
        if action_type:
            filters["action_type"] = action_type
        if start_date:
            filters["start_date"] = start_date.isoformat()
        if end_date:
            filters["end_date"] = end_date.isoformat()
        
        # 获取操作日志
        result = await user_service.get_user_activity_logs(
            user_id, filters, page, page_size
        )
        
        return {
            "success": True,
            "message": "获取用户操作日志成功",
            "data": result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户操作日志异常: {str(e)}", extra={
            "target_user_id": user_id,
            "current_user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户操作日志失败"
        )


@router.delete("/{user_id}", response_model=MessageResponse, summary="删除用户")
async def delete_user(
    user_id: int,
    hard_delete: bool = Query(False, description="是否硬删除"),
    reason: Optional[str] = Query(None, description="删除原因"),
    http_request: Request = None,
    current_user: Dict[str, Any] = Depends(require_admin_permission)
):
    """
    删除用户（管理员功能）
    
    - **user_id**: 用户ID
    - **hard_delete**: 是否硬删除（默认false，软删除）
    - **reason**: 删除原因
    
    需要管理员权限
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        
        # 不能删除自己
        if user_id == current_user["user_id"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能删除自己"
            )
        
        # 硬删除需要超级管理员权限
        if hard_delete and current_user["role"] != "super_admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="硬删除需要超级管理员权限"
            )
        
        # 删除用户
        result = await user_service.delete_user(
            user_id,
            hard_delete,
            operator_id=current_user["user_id"],
            client_ip=client_ip,
            reason=reason
        )
        
        if result["success"]:
            logger.info(f"用户删除成功", extra={
                "target_user_id": user_id,
                "hard_delete": hard_delete,
                "reason": reason,
                "deleted_by": current_user["user_id"],
                "client_ip": client_ip
            })
            
            return MessageResponse(
                success=True,
                message=result["message"],
                data={
                    "user_id": user_id,
                    "hard_delete": hard_delete,
                    "reason": reason,
                    "deleted_at": datetime.utcnow().isoformat()
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except HTTPException:
        raise
    except ValueError as e:
        logger.warning(f"用户删除失败: {str(e)}", extra={
            "target_user_id": user_id,
            "current_user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"用户删除异常: {str(e)}", extra={
            "target_user_id": user_id,
            "current_user_id": current_user["user_id"]
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="用户删除失败，请稍后重试"
        )